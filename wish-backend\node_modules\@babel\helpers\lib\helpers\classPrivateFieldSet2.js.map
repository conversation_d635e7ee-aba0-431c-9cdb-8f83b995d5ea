{"version": 3, "names": ["_assert<PERSON>lassBrand", "require", "_classPrivateFieldSet2", "privateMap", "receiver", "value", "set", "assertClassBrand"], "sources": ["../../src/helpers/classPrivateFieldSet2.ts"], "sourcesContent": ["/* @minVersion 7.24.0 */\n\nimport assertClassBrand from \"./assertClassBrand.ts\";\n\nexport default function _classPrivateFieldSet2(\n  privateMap: WeakMap<any, any>,\n  receiver: any,\n  value: any,\n) {\n  privateMap.set(assertClassBrand(privateMap, receiver), value);\n  return value;\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,iBAAA,GAAAC,OAAA;AAEe,SAASC,sBAAsBA,CAC5CC,UAA6B,EAC7BC,QAAa,EACbC,KAAU,EACV;EACAF,UAAU,CAACG,GAAG,CAAC,IAAAC,yBAAgB,EAACJ,UAAU,EAAEC,QAAQ,CAAC,EAAEC,KAAK,CAAC;EAC7D,OAAOA,KAAK;AACd", "ignoreList": []}