/**
 * 内容安全检测混入
 * 为组件提供内容过滤和安全检测功能
 */

import contentFilter, { isContentSafe, filterContent, assessContentRisk } from '@/utils/contentFilter'
import { useSafetyStore } from '@/store'
import { toast } from '@/utils'

export const contentSafetyMixin = {
  data() {
    return {
      // 内容安全检测状态
      contentSafetyState: {
        checking: false,
        lastCheckTime: null,
        riskLevel: 'safe'
      }
    }
  },

  computed: {
    safetyStore() {
      return useSafetyStore()
    }
  },

  methods: {
    /**
     * 检测内容是否安全
     * @param {string} content - 待检测内容
     * @param {Object} options - 检测选项
     * @returns {Promise<Object>} 检测结果
     */
    async checkContentSafety(content, options = {}) {
      const {
        showToast = true,
        autoFilter = false,
        strictMode = false
      } = options

      this.contentSafetyState.checking = true

      try {
        // 本地检测
        const localResult = contentFilter.detectSensitiveContent(content)
        
        // 如果本地检测发现问题，直接返回
        if (!localResult.isSafe) {
          if (showToast) {
            this.showSafetyWarning(localResult)
          }
          
          return {
            isSafe: false,
            level: localResult.level,
            issues: localResult.issues,
            filteredContent: autoFilter ? filterContent(content) : content,
            source: 'local'
          }
        }

        // 服务端检测（可选）
        if (strictMode) {
          const serverResult = await this.safetyStore.checkContentSafety(content, 'text')
          
          if (!serverResult.isSafe) {
            if (showToast) {
              this.showSafetyWarning(serverResult)
            }
            
            return {
              ...serverResult,
              filteredContent: autoFilter ? filterContent(content) : content,
              source: 'server'
            }
          }
        }

        // 内容安全
        this.contentSafetyState.riskLevel = 'safe'
        this.contentSafetyState.lastCheckTime = Date.now()

        return {
          isSafe: true,
          level: 'safe',
          issues: [],
          filteredContent: content,
          source: strictMode ? 'server' : 'local'
        }

      } catch (error) {
        console.error('内容安全检测失败:', error)
        
        // 检测失败时的降级处理
        const fallbackResult = contentFilter.detectSensitiveContent(content)
        
        return {
          isSafe: fallbackResult.isSafe,
          level: fallbackResult.level,
          issues: fallbackResult.issues,
          filteredContent: autoFilter ? filterContent(content) : content,
          source: 'fallback',
          error: error.message
        }

      } finally {
        this.contentSafetyState.checking = false
      }
    },

    /**
     * 快速检测内容是否安全（仅本地检测）
     * @param {string} content - 待检测内容
     * @returns {boolean} 是否安全
     */
    isContentSafe(content) {
      return isContentSafe(content)
    },

    /**
     * 过滤敏感内容
     * @param {string} content - 原始内容
     * @param {string} replacement - 替换字符
     * @returns {string} 过滤后的内容
     */
    filterSensitiveContent(content, replacement = '*') {
      return filterContent(content, replacement)
    },

    /**
     * 评估内容风险等级
     * @param {string} content - 内容
     * @returns {string} 风险等级
     */
    assessContentRisk(content) {
      return assessContentRisk(content)
    },

    /**
     * 显示安全警告
     * @param {Object} result - 检测结果
     */
    showSafetyWarning(result) {
      const messages = {
        banned: '内容包含违禁词汇，无法发布',
        sensitive: '内容包含敏感词汇，请修改后重试',
        spam: '内容疑似垃圾信息，请检查后重试'
      }

      const message = messages[result.level] || '内容存在安全风险，请检查'
      toast.error(message)
    },

    /**
     * 验证并过滤用户输入
     * @param {string} input - 用户输入
     * @param {Object} options - 验证选项
     * @returns {Promise<Object>} 验证结果
     */
    async validateAndFilterInput(input, options = {}) {
      const {
        required = false,
        minLength = 0,
        maxLength = 1000,
        autoFilter = true,
        strictMode = false
      } = options

      // 基础验证
      if (required && (!input || !input.trim())) {
        return {
          isValid: false,
          error: '内容不能为空',
          filteredInput: input
        }
      }

      const trimmedInput = input.trim()

      if (trimmedInput.length < minLength) {
        return {
          isValid: false,
          error: `内容长度不能少于${minLength}个字符`,
          filteredInput: input
        }
      }

      if (trimmedInput.length > maxLength) {
        return {
          isValid: false,
          error: `内容长度不能超过${maxLength}个字符`,
          filteredInput: input
        }
      }

      // 安全检测
      const safetyResult = await this.checkContentSafety(trimmedInput, {
        showToast: false,
        autoFilter,
        strictMode
      })

      if (!safetyResult.isSafe) {
        return {
          isValid: false,
          error: '内容包含不当信息，请修改后重试',
          filteredInput: safetyResult.filteredContent,
          safetyIssues: safetyResult.issues
        }
      }

      return {
        isValid: true,
        filteredInput: safetyResult.filteredContent,
        originalInput: trimmedInput
      }
    },

    /**
     * 举报内容
     * @param {Object} reportData - 举报数据
     */
    async reportContent(reportData) {
      try {
        const result = await this.safetyStore.submitReport(reportData)
        toast.success('举报提交成功，我们会尽快处理')
        return result
      } catch (error) {
        console.error('举报失败:', error)
        toast.error('举报提交失败，请重试')
        throw error
      }
    },

    /**
     * 检测图片内容安全性
     * @param {string} imageUrl - 图片URL
     * @returns {Promise<Object>} 检测结果
     */
    async checkImageSafety(imageUrl) {
      try {
        const result = await this.safetyStore.checkContentSafety(imageUrl, 'image')
        return result
      } catch (error) {
        console.error('图片安全检测失败:', error)
        return {
          isSafe: true, // 降级处理，默认认为安全
          level: 'safe',
          issues: [],
          error: error.message
        }
      }
    },

    /**
     * 批量检测内容安全性
     * @param {Array} contents - 内容数组
     * @param {Object} options - 检测选项
     * @returns {Promise<Array>} 检测结果数组
     */
    async batchCheckContentSafety(contents, options = {}) {
      const results = []
      
      for (const content of contents) {
        try {
          const result = await this.checkContentSafety(content, {
            ...options,
            showToast: false
          })
          results.push(result)
        } catch (error) {
          results.push({
            isSafe: false,
            level: 'error',
            issues: [],
            error: error.message
          })
        }
      }

      return results
    },

    /**
     * 获取内容安全建议
     * @param {Object} safetyResult - 安全检测结果
     * @returns {Array} 建议列表
     */
    getContentSafetySuggestions(safetyResult) {
      const suggestions = []

      if (!safetyResult.isSafe && safetyResult.issues) {
        safetyResult.issues.forEach(issue => {
          switch (issue.type) {
            case 'banned':
              suggestions.push('请移除违禁词汇')
              break
            case 'sensitive':
              suggestions.push('请替换敏感词汇')
              break
            case 'spam':
              suggestions.push('请避免发布广告或垃圾信息')
              break
            default:
              suggestions.push('请检查内容是否合规')
          }
        })
      }

      return suggestions
    }
  }
}

export default contentSafetyMixin
