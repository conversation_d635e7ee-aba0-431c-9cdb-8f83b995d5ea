* {
  margin: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  -webkit-tap-highlight-color: transparent;
}

html,
body {
  -webkit-user-select: none;
  user-select: none;
  width: 100%;
}

html {
  height: 100%;
  height: 100vh;
  width: 100%;
  width: 100vw;
}

body {
  overflow-x: hidden;
  background-color: white;
  height:100%;
}

#app{
  height: 100%;
}

input[type='search']::-webkit-search-cancel-button {
  display: none;
}

.uni-loading,
uni-button[loading]:before {
  background: transparent
    url('data:image/svg+xml;base64, PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSIxMjAiIGhlaWdodD0iMTIwIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PHBhdGggZmlsbD0ibm9uZSIgZD0iTTAgMGgxMDB2MTAwSDB6Ii8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTlFOUU5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDAgLTMwKSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iIzk4OTY5NyIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgzMCAxMDUuOTggNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjOUI5OTlBIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDYwIDc1Ljk4IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0EzQTFBMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSg5MCA2NSA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNBQkE5QUEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoMTIwIDU4LjY2IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0IyQjJCMiIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgxNTAgNTQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjQkFCOEI5IiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKDE4MCA1MCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDMkMwQzEiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTE1MCA0NS45OCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNDQkNCQ0IiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTEyMCA0MS4zNCA2NSkiLz48cmVjdCB3aWR0aD0iNyIgaGVpZ2h0PSIyMCIgeD0iNDYuNSIgeT0iNDAiIGZpbGw9IiNEMkQyRDIiIHJ4PSI1IiByeT0iNSIgdHJhbnNmb3JtPSJyb3RhdGUoLTkwIDM1IDY1KSIvPjxyZWN0IHdpZHRoPSI3IiBoZWlnaHQ9IjIwIiB4PSI0Ni41IiB5PSI0MCIgZmlsbD0iI0RBREFEQSIgcng9IjUiIHJ5PSI1IiB0cmFuc2Zvcm09InJvdGF0ZSgtNjAgMjQuMDIgNjUpIi8+PHJlY3Qgd2lkdGg9IjciIGhlaWdodD0iMjAiIHg9IjQ2LjUiIHk9IjQwIiBmaWxsPSIjRTJFMkUyIiByeD0iNSIgcnk9IjUiIHRyYW5zZm9ybT0icm90YXRlKC0zMCAtNS45OCA2NSkiLz48L3N2Zz4=')
    no-repeat;
}

.uni-loading {
  width: 20px;
  height: 20px;
  display: inline-block;
  vertical-align: middle;
  animation: uni-loading 1s steps(12, end) infinite;
  background-size: 100%;
}

@keyframes uni-loading {
  0% {
    transform: rotate3d(0, 0, 1, 0deg);
  }

  100% {
    transform: rotate3d(0, 0, 1, 360deg);
  }
}

@media (prefers-color-scheme: dark) {
  html {
    --UI-BG-COLOR-ACTIVE: #373737;
    --UI-BORDER-COLOR-1: #373737;
    /* UI */
    --UI-BG: #000;
    --UI-BG-0: #191919;
    --UI-BG-1: #1f1f1f;
    --UI-BG-2: #232323;
    --UI-BG-3: #2f2f2f;
    --UI-BG-4: #606060;
    --UI-BG-5: #2c2c2c;
    --UI-FG: #fff;
    --UI-FG-0: hsla(0, 0%, 100%, 0.8);
    --UI-FG-HALF: hsla(0, 0%, 100%, 0.6);
    --UI-FG-1: hsla(0, 0%, 100%, 0.5);
    --UI-FG-2: hsla(0, 0%, 100%, 0.3);
    --UI-FG-3: hsla(0, 0%, 100%, 0.05);
  }

  body {
    background-color: var(--UI-BG-0);
    color: var(--UI-FG-0);
  }
}
@keyframes once-show {
  from {
    top: 0;
  }
}
uni-resize-sensor,
uni-resize-sensor > div {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
}
uni-resize-sensor {
  display: block;
  z-index: -1;
  visibility: hidden;
  animation: once-show 1ms;
}
uni-resize-sensor > div > div {
  position: absolute;
  left: 0;
  top: 0;
}
uni-resize-sensor > div:first-child > div {
  width: 100000px;
  height: 100000px;
}
uni-resize-sensor > div:last-child > div {
  width: 200%;
  height: 200%;
}
uni-view {
  display: block;
}
uni-view[hidden] {
  display: none;
}
uni-button {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  padding-left: 14px;
  padding-right: 14px;
  box-sizing: border-box;
  font-size: 18px;
  text-align: center;
  text-decoration: none;
  line-height: 2.55555556;
  border-radius: 5px;
  -webkit-tap-highlight-color: transparent;
  overflow: hidden;
  color: #000000;
  background-color: #f8f8f8;
  cursor: pointer;
}

uni-button[hidden] {
  display: none !important;
}

uni-button:after {
  content: ' ';
  width: 200%;
  height: 200%;
  position: absolute;
  top: 0;
  left: 0;
  border: 1px solid rgba(0, 0, 0, 0.2);
  transform: scale(0.5);
  transform-origin: 0 0;
  box-sizing: border-box;
  border-radius: 10px;
}

uni-button[native] {
  padding-left: 0;
  padding-right: 0;
}

uni-button[native] .uni-button-cover-view-wrapper {
  border: inherit;
  border-color: inherit;
  border-radius: inherit;
  background-color: inherit;
}

uni-button[native] .uni-button-cover-view-inner {
  padding-left: 14px;
  padding-right: 14px;
}

uni-button uni-cover-view {
  line-height: inherit;
  white-space: inherit;
}

uni-button[type='default'] {
  color: #000000;
  background-color: #f8f8f8;
}

uni-button[type='primary'] {
  color: #ffffff;
  background-color: #007aff;
}

uni-button[type='warn'] {
  color: #ffffff;
  background-color: #e64340;
}

uni-button[disabled] {
  color: rgba(255, 255, 255, 0.6);
  cursor: not-allowed;
}

uni-button[disabled][type='default'],
uni-button[disabled]:not([type]) {
  color: rgba(0, 0, 0, 0.3);
  background-color: #f7f7f7;
}

uni-button[disabled][type='primary'] {
  background-color: rgba(0, 122, 255, 0.6);
}

uni-button[disabled][type='warn'] {
  background-color: #ec8b89;
}

uni-button[type='primary'][plain] {
  color: #007aff;
  border: 1px solid #007aff;
  background-color: transparent;
}

uni-button[type='primary'][plain][disabled] {
  color: rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 0, 0, 0.2);
}

uni-button[type='primary'][plain]:after {
  border-width: 0;
}

uni-button[type='default'][plain] {
  color: #353535;
  border: 1px solid #353535;
  background-color: transparent;
}

uni-button[type='default'][plain][disabled] {
  color: rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 0, 0, 0.2);
}

uni-button[type='default'][plain]:after {
  border-width: 0;
}

uni-button[plain] {
  color: #353535;
  border: 1px solid #353535;
  background-color: transparent;
}

uni-button[plain][disabled] {
  color: rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 0, 0, 0.2);
}

uni-button[plain]:after {
  border-width: 0;
}

uni-button[plain][native] .uni-button-cover-view-inner {
  padding: 0;
}

uni-button[type='warn'][plain] {
  color: #e64340;
  border: 1px solid #e64340;
  background-color: transparent;
}

uni-button[type='warn'][plain][disabled] {
  color: rgba(0, 0, 0, 0.2);
  border-color: rgba(0, 0, 0, 0.2);
}

uni-button[type='warn'][plain]:after {
  border-width: 0;
}

uni-button[size='mini'] {
  display: inline-block;
  line-height: 2.3;
  font-size: 13px;
  padding: 0 1.34em;
}

uni-button[size='mini'][native] {
  padding: 0;
}

uni-button[size='mini'][native] .uni-button-cover-view-inner {
  padding: 0 1.34em;
}

uni-button[loading]:not([disabled]) {
  cursor: progress;
}

uni-button[loading]:before {
  content: ' ';
  display: inline-block;
  width: 18px;
  height: 18px;
  vertical-align: middle;
  animation: uni-loading 1s steps(12, end) infinite;
  background-size: 100%;
}

uni-button[loading][type='primary'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: #0062cc;
}

uni-button[loading][type='primary'][plain] {
  color: #007aff;
  background-color: transparent;
}

uni-button[loading][type='default'] {
  color: rgba(0, 0, 0, 0.6);
  background-color: #dedede;
}

uni-button[loading][type='default'][plain] {
  color: #353535;
  background-color: transparent;
}

uni-button[loading][type='warn'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: #ce3c39;
}

uni-button[loading][type='warn'][plain] {
  color: #e64340;
  background-color: transparent;
}

uni-button[loading][native]:before {
  content: none;
}

.button-hover {
  color: rgba(0, 0, 0, 0.6);
  background-color: #dedede;
}

.button-hover[plain] {
  color: rgba(53, 53, 53, 0.6);
  border-color: rgba(53, 53, 53, 0.6);
  background-color: transparent;
}

.button-hover[type='primary'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: #0062cc;
}

.button-hover[type='primary'][plain] {
  color: rgba(0, 122, 255, 0.6);
  border-color: rgba(0, 122, 255, 0.6);
  background-color: transparent;
}

.button-hover[type='default'] {
  color: rgba(0, 0, 0, 0.6);
  background-color: #dedede;
}

.button-hover[type='default'][plain] {
  color: rgba(53, 53, 53, 0.6);
  border-color: rgba(53, 53, 53, 0.6);
  background-color: transparent;
}

.button-hover[type='warn'] {
  color: rgba(255, 255, 255, 0.6);
  background-color: #ce3c39;
}

.button-hover[type='warn'][plain] {
  color: rgba(230, 67, 64, 0.6);
  border-color: rgba(230, 67, 64, 0.6);
  background-color: transparent;
}

@media (prefers-color-scheme: dark) {
  uni-button,
  uni-button[type='default'] {
    color: #d6d6d6;
    background-color: #343434;
  }

  .button-hover,
  .button-hover[type='default'] {
    color: #d6d6d6;
    background-color: rgba(255, 255, 255, 0.1);
  }

  uni-button[disabled][type='default'],
  uni-button[disabled]:not([type]) {
    color: rgba(255, 255, 255, 0.2);
    background-color: rgba(255, 255, 255, 0.08);
  }

  uni-button[type='primary'][plain][disabled] {
    color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.2);
  }

  uni-button[type='default'][plain] {
    color: #d6d6d6;
    border: 1px solid #d6d6d6;
  }

  .button-hover[type='default'][plain] {
    color: rgba(150, 150, 150, 0.6);
    border-color: rgba(150, 150, 150, 0.6);
    background-color: rgba(50, 50, 50, 0.2);
  }

  uni-button[type='default'][plain][disabled] {
    border-color: hsla(0, 0%, 100%, 0.2);
    color: hsla(0, 0%, 100%, 0.2);
  }
}
uni-image {
  width: 320px;
  height: 240px;
  display: inline-block;
  overflow: hidden;
  position: relative;
}

uni-image[hidden] {
  display: none;
}

uni-image > div {
  width: 100%;
  height: 100%;
  background-repeat:no-repeat;
}

uni-image > img {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
          user-select: none;
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

uni-image > .uni-image-will-change {
  will-change: transform;
}
uni-text[selectable] {
  cursor: auto;
  -webkit-user-select: text;
          user-select: text;
}

uni-text {
  white-space: pre-line;
}
uni-navigator {
  height: auto;
  width: auto;
  display: block;
  cursor: pointer;
}

uni-navigator[hidden] {
  display: none;
}

.navigator-hover {
  background-color: rgba(0, 0, 0, 0.1);
  opacity: 0.7;
}

.navigator-wrap {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

.navigator-wrap:link {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

.navigator-wrap:visited {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

.navigator-wrap:hover {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}

.navigator-wrap:active {
  text-decoration: none;
  color: inherit;
  cursor: pointer;
}
uni-progress {
  display: flex;
  align-items: center;
}

uni-progress[hidden] {
  display: none;
}

.uni-progress-bar {
  flex: 1;
}

.uni-progress-inner-bar {
  width: 0;
  height: 100%;
}

.uni-progress-info {
  margin-top: 0;
  margin-bottom: 0;
  min-width: 2em;
  margin-left: 15px;
  font-size: 16px;
}
.uni-label-pointer {
  cursor: pointer;
}
uni-checkbox-group {
  display: block;
}

uni-checkbox-group[hidden] {
  display: none;
}
uni-checkbox {
  -webkit-tap-highlight-color: transparent;
  display: inline-block;
  cursor: pointer;
}

uni-checkbox[hidden] {
  display: none;
}

uni-checkbox[disabled] {
  cursor: not-allowed;
}

.uni-checkbox-wrapper {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

.uni-checkbox-input {
  margin-right: 5px;
  -webkit-appearance: none;
          appearance: none;
  outline: 0;
  border: 1px solid #d1d1d1;
  background-color: #ffffff;
  border-radius: 3px;
  width: 22px;
  height: 22px;
  position: relative;
}

.uni-checkbox-input svg {
  color: #007aff;
  font-size: 22px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -48%) scale(0.73);
}

@media (hover: hover) {
  uni-checkbox:not([disabled]) .uni-checkbox-input:hover {
    border-color: var(--HOVER-BD-COLOR, #007aff) !important;
  }
}

.uni-checkbox-input.uni-checkbox-input-disabled {
  background-color: #e1e1e1;
}

.uni-checkbox-input.uni-checkbox-input-disabled:before {
  color: #adadad;
}

uni-checkbox-group {
  display: block;
}
uni-radio {
  -webkit-tap-highlight-color: transparent;
  display: inline-block;
  cursor: pointer;
}

uni-radio[hidden] {
  display: none;
}

uni-radio[disabled] {
  cursor: not-allowed;
}

.uni-radio-wrapper {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

.uni-radio-input {
  -webkit-appearance: none;
          appearance: none;
  margin-right: 5px;
  outline: 0;
  border: 1px solid #d1d1d1;
  background-color: #ffffff;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  position: relative;
}

@media (hover: hover) {
  uni-radio:not([disabled]) .uni-radio-input:hover {
    border-color: var(--HOVER-BD-COLOR, #007aff) !important;
  }
}

.uni-radio-input svg {
  color: #ffffff;
  font-size: 18px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -48%) scale(0.73);
}

.uni-radio-input.uni-radio-input-disabled {
  background-color: #e1e1e1;
  border-color: #d1d1d1;
}

.uni-radio-input.uni-radio-input-disabled svg{
  color: #adadad;
}
uni-radio-group {
  display: block;
}
uni-radio-group[hidden] {
  display: none;
}
uni-slider {
  margin: 10px 18px;
  padding: 0;
  display: block;
}

uni-slider[hidden] {
  display: none;
}

uni-slider .uni-slider-wrapper {
  display: flex;
  align-items: center;
  min-height: 16px;
}

uni-slider .uni-slider-tap-area {
  flex: 1;
  padding: 8px 0;
}

uni-slider .uni-slider-handle-wrapper {
  position: relative;
  height: 2px;
  border-radius: 5px;
  background-color: #e9e9e9;
  cursor: pointer;
  transition: background-color 0.3s ease;
  -webkit-tap-highlight-color: transparent;
}

uni-slider .uni-slider-track {
  height: 100%;
  border-radius: 6px;
  background-color: #007aff;
  transition: background-color 0.3s ease;
}

uni-slider .uni-slider-handle,
uni-slider .uni-slider-thumb {
  position: absolute;
  left: 50%;
  top: 50%;
  cursor: pointer;
  border-radius: 50%;
  transition: border-color 0.3s ease;
}

uni-slider .uni-slider-handle {
  width: 28px;
  height: 28px;
  margin-top: -14px;
  margin-left: -14px;
  background-color: transparent;
  z-index: 3;
  cursor: grab;
}

uni-slider .uni-slider-thumb {
  z-index: 2;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
}

uni-slider .uni-slider-step {
  position: absolute;
  width: 100%;
  height: 2px;
  background: transparent;
  z-index: 1;
}

uni-slider .uni-slider-value {
  width: 3ch;
  color: #888;
  font-size: 14px;
  margin-left: 1em;
}

uni-slider .uni-slider-disabled .uni-slider-track {
  background-color: #ccc;
}

uni-slider .uni-slider-disabled .uni-slider-thumb {
  background-color: #fff;
  border-color: #ccc;
}
uni-switch {
  -webkit-tap-highlight-color: transparent;
  display: inline-block;
  cursor: pointer;
}

uni-switch[hidden] {
  display: none;
}

uni-switch[disabled] {
  cursor: not-allowed;
}

uni-switch[disabled] .uni-switch-input {
  opacity: 0.7;
}

.uni-switch-wrapper {
  display: inline-flex;
  align-items: center;
  vertical-align: middle;
}

.uni-switch-input {
  -webkit-appearance: none;
          appearance: none;
  position: relative;
  width: 52px;
  height: 32px;
  margin-right: 5px;
  border: 1px solid #dfdfdf;
  outline: 0;
  border-radius: 16px;
  box-sizing: border-box;
  background-color: #dfdfdf;
  transition: background-color 0.1s, border 0.1s;
}

.uni-switch-input:before {
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  width: 50px;
  height: 30px;
  border-radius: 15px;
  background-color: #fdfdfd;
  transition: transform 0.3s;
}

.uni-switch-input:after {
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  width: 30px;
  height: 30px;
  border-radius: 15px;
  background-color: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  transition: transform 0.3s;
}

.uni-switch-input.uni-switch-input-checked {
  border-color: #007aff;
  background-color: #007aff;
}

.uni-switch-input.uni-switch-input-checked:before {
  transform: scale(0);
}

.uni-switch-input.uni-switch-input-checked:after {
  transform: translateX(20px);
}

uni-switch .uni-checkbox-input {
  margin-right: 5px;
  -webkit-appearance: none;
          appearance: none;
  outline: 0;
  border: 1px solid #d1d1d1;
  background-color: #ffffff;
  border-radius: 3px;
  width: 22px;
  height: 22px;
  position: relative;
  color: #007aff;
}

uni-switch:not([disabled]) .uni-checkbox-input:hover {
  border-color: #007aff;
}

uni-switch .uni-checkbox-input svg {
  fill: #007aff;
  font-size: 22px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -48%) scale(0.73);
}

.uni-checkbox-input.uni-checkbox-input-disabled {
  background-color: #e1e1e1;
}

.uni-checkbox-input.uni-checkbox-input-disabled:before {
  color: #adadad;
}

@media (prefers-color-scheme: dark) {
  uni-switch .uni-switch-input {
    border-color: #3b3b3f;
  }

  uni-switch .uni-switch-input,
  uni-switch .uni-switch-input:before {
    background-color: #3b3b3f;
  }

  uni-switch .uni-switch-input:after {
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
  }

  uni-switch .uni-checkbox-input {
    background-color: #2c2c2c;
    border: 1px solid #656565;
  }
}
uni-input {
  display: block;
  font-size: 16px;
  line-height: 1.4em;
  height: 1.4em;
  min-height: 1.4em;
  overflow: hidden;
}

uni-input[hidden] {
  display: none;
}

.uni-input-wrapper,
.uni-input-placeholder,
.uni-input-form,
.uni-input-input {
  outline: none;
  border: none;
  padding: 0;
  margin: 0;
  text-decoration: inherit;
}

.uni-input-wrapper,
.uni-input-form {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  flex-direction: column;
  justify-content: center;
}

.uni-input-placeholder,
.uni-input-input {
  width: 100%;
}

.uni-input-placeholder {
  position: absolute;
  top: auto !important;
  left: 0;
  color: gray;
  overflow: hidden;
  text-overflow: clip;
  white-space: pre;
  word-break: keep-all;
  pointer-events: none;
  line-height: inherit;
}

.uni-input-input {
  position: relative;
  display: block;
  height: 100%;
  background: none;
  color: inherit;
  opacity: 1;
  font: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  text-align: inherit;
  text-indent: inherit;
  text-transform: inherit;
  text-shadow: inherit;
}

.uni-input-input[type='search']::-webkit-search-cancel-button,
.uni-input-input[type="search"]::-webkit-search-decoration {
  display: none;
}

.uni-input-input::-webkit-outer-spin-button,
.uni-input-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
          appearance: none;
  margin: 0;
}

.uni-input-input[type='number'] {
  -moz-appearance: textfield;
}

.uni-input-input:disabled {
  /* 用于重置iOS14以下禁用状态文字颜色 */
  -webkit-text-fill-color: currentcolor;
}
uni-textarea {
  width: 300px;
  height: 150px;
  display: block;
  position: relative;
  font-size: 16px;
  line-height: normal;
  white-space: pre-wrap;
  word-break: break-all;
}
uni-textarea[hidden] {
  display: none;
}
uni-textarea[auto-height="true"] {
  height: -webkit-fit-content !important;
  height: fit-content !important;
}
.uni-textarea-wrapper,
.uni-textarea-placeholder,
.uni-textarea-line,
.uni-textarea-compute,
.uni-textarea-textarea {
  outline: none;
  border: none;
  padding: 0;
  margin: 0;
  text-decoration: inherit;
}
.uni-textarea-wrapper {
  display: block;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: inherit;
  overflow-y: hidden;
}
.uni-textarea-placeholder,
.uni-textarea-line,
.uni-textarea-compute,
.uni-textarea-textarea {
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  white-space: inherit;
  word-break: inherit;
}
.uni-textarea-placeholder {
  color: grey;
  overflow: hidden;
}
.uni-textarea-line,
.uni-textarea-compute {
  visibility: hidden;
  height: auto;
}
.uni-textarea-line {
  width: 1em;
}
.uni-textarea-textarea {
  resize: none;
  background: none;
  color: inherit;
  opacity: 1;
  font: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  text-align: inherit;
  text-indent: inherit;
  text-transform: inherit;
  text-shadow: inherit;
}
/* 用于解决 iOS textarea 内部默认边距 */
.uni-textarea-textarea-fix-margin {
  width: auto;
  right: 0;
  margin: 0 -3px;
}
.uni-textarea-textarea:disabled {
  /* 用于重置iOS14以下禁用状态文字颜色 */
  -webkit-text-fill-color: currentcolor;
}
.ql-container {
  display: block;
  position: relative;
  box-sizing: border-box;
  -webkit-user-select: text;
          user-select: text;
  outline: none;
  overflow: hidden;
  width: 100%;
  height: 200px;
  min-height: 200px;
}
.ql-container[hidden] {
  display: none;
}
.ql-container .ql-editor {
  position: relative;
  font-size: inherit;
  line-height: inherit;
  font-family: inherit;
  min-height: inherit;
  width: 100%;
  height: 100%;
  padding: 0;
  overflow-x: hidden;
  overflow-y: auto;
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-overflow-scrolling: touch;
}
.ql-container .ql-editor::-webkit-scrollbar {
  width: 0 !important;
}
.ql-container .ql-editor.scroll-disabled {
  overflow: hidden;
}
.ql-container .ql-image-overlay {
  display: flex;
  position: absolute;
  box-sizing: border-box;
  border: 1px dashed #ccc;
  justify-content: center;
  align-items: center;
  -webkit-user-select: none;
          user-select: none;
}
.ql-container .ql-image-overlay .ql-image-size {
  position: absolute;
  padding: 4px 8px;
  text-align: center;
  background-color: #fff;
  color: #888;
  border: 1px solid #ccc;
  box-sizing: border-box;
  opacity: 0.8;
  right: 4px;
  top: 4px;
  font-size: 12px;
  display: inline-block;
  width: auto;
}
.ql-container .ql-image-overlay .ql-image-toolbar {
  position: relative;
  text-align: center;
  box-sizing: border-box;
  background: #000;
  border-radius: 5px;
  color: #fff;
  font-size: 0;
  min-height: 24px;
  z-index: 100;
}
.ql-container .ql-image-overlay .ql-image-toolbar span {
  display: inline-block;
  cursor: pointer;
  padding: 5px;
  font-size: 12px;
  border-right: 1px solid #fff;
}
.ql-container .ql-image-overlay .ql-image-toolbar span:last-child {
  border-right: 0;
}
.ql-container .ql-image-overlay .ql-image-toolbar span.triangle-up {
  padding: 0;
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translatex(-50%);
  width: 0;
  height: 0;
  border-width: 6px;
  border-style: solid;
  border-color: transparent transparent black transparent;
}
.ql-container .ql-image-overlay .ql-image-handle {
  position: absolute;
  height: 12px;
  width: 12px;
  border-radius: 50%;
  border: 1px solid #ccc;
  box-sizing: border-box;
  background: #fff;
}
.ql-container img {
  display: inline-block;
  max-width: 100%;
}
.ql-clipboard p {
  margin: 0;
  padding: 0;
}
.ql-editor {
  box-sizing: border-box;
  height: 100%;
  outline: none;
  overflow-y: auto;
  tab-size: 4;
  -moz-tab-size: 4;
  text-align: left;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.ql-editor > * {
  cursor: text;
}
.ql-editor p,
.ql-editor ol,
.ql-editor ul,
.ql-editor pre,
.ql-editor blockquote,
.ql-editor h1,
.ql-editor h2,
.ql-editor h3,
.ql-editor h4,
.ql-editor h5,
.ql-editor h6 {
  margin: 0;
  padding: 0;
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol > li,
.ql-editor ul > li {
  list-style-type: none;
}
.ql-editor ul > li::before {
  content: '\2022';
}
.ql-editor ul[data-checked=true],
.ql-editor ul[data-checked=false] {
  pointer-events: none;
}
.ql-editor ul[data-checked=true] > li *,
.ql-editor ul[data-checked=false] > li * {
  pointer-events: all;
}
.ql-editor ul[data-checked=true] > li::before,
.ql-editor ul[data-checked=false] > li::before {
  color: #777;
  cursor: pointer;
  pointer-events: all;
}
.ql-editor ul[data-checked=true] > li::before {
  content: '\2611';
}
.ql-editor ul[data-checked=false] > li::before {
  content: '\2610';
}
.ql-editor ol,
.ql-editor ul {
  padding-left: 1.5em;
}

.ql-editor li:not(.ql-direction-rtl)::before {
  margin-left: -1.5em;
}

.ql-editor li.ql-direction-rtl::before {
  margin-right: -1.5em;
}

.ql-editor li::before {
  display: inline-block;
  white-space: nowrap;
  width: 2em;
}
.ql-editor ol li {
  counter-reset: list-1 list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
  counter-increment: list-0;
}
.ql-editor ol li:before {
  content: counter(list-0, decimal) '. ';
}
.ql-editor ol li.ql-indent-1 {
  counter-increment: list-1;
}
.ql-editor ol li.ql-indent-1:before {
  content: counter(list-1, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-1 {
  counter-reset: list-2 list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-2 {
  counter-increment: list-2;
}
.ql-editor ol li.ql-indent-2:before {
  content: counter(list-2, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-2 {
  counter-reset: list-3 list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-3 {
  counter-increment: list-3;
}
.ql-editor ol li.ql-indent-3:before {
  content: counter(list-3, decimal) '. ';
}
.ql-editor ol li.ql-indent-3 {
  counter-reset: list-4 list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-4 {
  counter-increment: list-4;
}
.ql-editor ol li.ql-indent-4:before {
  content: counter(list-4, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-4 {
  counter-reset: list-5 list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-5 {
  counter-increment: list-5;
}
.ql-editor ol li.ql-indent-5:before {
  content: counter(list-5, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-5 {
  counter-reset: list-6 list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-6 {
  counter-increment: list-6;
}
.ql-editor ol li.ql-indent-6:before {
  content: counter(list-6, decimal) '. ';
}
.ql-editor ol li.ql-indent-6 {
  counter-reset: list-7 list-8 list-9;
}
.ql-editor ol li.ql-indent-7 {
  counter-increment: list-7;
}
.ql-editor ol li.ql-indent-7:before {
  content: counter(list-7, lower-alpha) '. ';
}
.ql-editor ol li.ql-indent-7 {
  counter-reset: list-8 list-9;
}
.ql-editor ol li.ql-indent-8 {
  counter-increment: list-8;
}
.ql-editor ol li.ql-indent-8:before {
  content: counter(list-8, lower-roman) '. ';
}
.ql-editor ol li.ql-indent-8 {
  counter-reset: list-9;
}
.ql-editor ol li.ql-indent-9 {
  counter-increment: list-9;
}
.ql-editor ol li.ql-indent-9:before {
  content: counter(list-9, decimal) '. ';
}
.ql-editor .ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 2em;
}
.ql-editor li.ql-indent-1:not(.ql-direction-rtl) {
  padding-left: 2em;
}
.ql-editor .ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 2em;
}
.ql-editor li.ql-indent-1.ql-direction-rtl.ql-align-right {
  padding-right: 2em;
}
.ql-editor .ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 4em;
}
.ql-editor li.ql-indent-2:not(.ql-direction-rtl) {
  padding-left: 4em;
}
.ql-editor .ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 4em;
}
.ql-editor li.ql-indent-2.ql-direction-rtl.ql-align-right {
  padding-right: 4em;
}
.ql-editor .ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 6em;
}
.ql-editor li.ql-indent-3:not(.ql-direction-rtl) {
  padding-left: 6em;
}
.ql-editor .ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 6em;
}
.ql-editor li.ql-indent-3.ql-direction-rtl.ql-align-right {
  padding-right: 6em;
}
.ql-editor .ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 8em;
}
.ql-editor li.ql-indent-4:not(.ql-direction-rtl) {
  padding-left: 8em;
}
.ql-editor .ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 8em;
}
.ql-editor li.ql-indent-4.ql-direction-rtl.ql-align-right {
  padding-right: 8em;
}
.ql-editor .ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 10em;
}
.ql-editor li.ql-indent-5:not(.ql-direction-rtl) {
  padding-left: 10em;
}
.ql-editor .ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 10em;
}
.ql-editor li.ql-indent-5.ql-direction-rtl.ql-align-right {
  padding-right: 10em;
}
.ql-editor .ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 12em;
}
.ql-editor li.ql-indent-6:not(.ql-direction-rtl) {
  padding-left: 12em;
}
.ql-editor .ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 12em;
}
.ql-editor li.ql-indent-6.ql-direction-rtl.ql-align-right {
  padding-right: 12em;
}
.ql-editor .ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 14em;
}
.ql-editor li.ql-indent-7:not(.ql-direction-rtl) {
  padding-left: 14em;
}
.ql-editor .ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 14em;
}
.ql-editor li.ql-indent-7.ql-direction-rtl.ql-align-right {
  padding-right: 14em;
}
.ql-editor .ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 16em;
}
.ql-editor li.ql-indent-8:not(.ql-direction-rtl) {
  padding-left: 16em;
}
.ql-editor .ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 16em;
}
.ql-editor li.ql-indent-8.ql-direction-rtl.ql-align-right {
  padding-right: 16em;
}
.ql-editor .ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 18em;
}
.ql-editor li.ql-indent-9:not(.ql-direction-rtl) {
  padding-left: 18em;
}
.ql-editor .ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 18em;
}
.ql-editor li.ql-indent-9.ql-direction-rtl.ql-align-right {
  padding-right: 18em;
}
.ql-editor .ql-direction-rtl {
  direction: rtl;
  text-align: inherit;
}
.ql-editor .ql-align-center {
  text-align: center;
}
.ql-editor .ql-align-justify {
  text-align: justify;
}
.ql-editor .ql-align-right {
  text-align: right;
}
.ql-editor.ql-blank::before {
  color: rgba(0, 0, 0, 0.6);
  content: attr(data-placeholder);
  font-style: italic;
  pointer-events: none;
  position: absolute;
}
.ql-container.ql-disabled .ql-editor ul[data-checked] > li::before {
  pointer-events: none;
}
.ql-clipboard {
  left: -100000px;
  height: 1px;
  overflow-y: hidden;
  position: absolute;
  top: 50%;
}
uni-picker-view {
  display: block;
}

.uni-picker-view-wrapper {
  display: flex;
  position: relative;
  overflow: hidden;
  height: 100%;
}

uni-picker-view[hidden] {
  display: none;
}
uni-picker-view-column {
  flex: 1;
  position: relative;
  height: 100%;
  overflow: hidden;
}

uni-picker-view-column[hidden] {
  display: none;
}

.uni-picker-view-group {
  height: 100%;
  overflow: hidden;
}

.uni-picker-view-mask {
  transform: translateZ(0);
}

.uni-picker-view-indicator,
.uni-picker-view-mask {
  position: absolute;
  left: 0;
  width: 100%;
  z-index: 3;
  pointer-events: none;
}

.uni-picker-view-mask {
  top: 0;
  height: 100%;
  margin: 0 auto;
  background-image: linear-gradient(
      180deg,
      hsla(0, 0%, 100%, 0.95),
      hsla(0, 0%, 100%, 0.6)
    ),
    linear-gradient(0deg, hsla(0, 0%, 100%, 0.95), hsla(0, 0%, 100%, 0.6));
  background-position: top, bottom;
  background-size: 100% 102px;
  background-repeat: no-repeat;
  transform: translateZ(0);
}

.uni-picker-view-indicator {
  height: 34px;
  /* top: 102px; */
  top: 50%;
  transform: translateY(-50%);
}

.uni-picker-view-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  will-change: transform;
  padding: 102px 0;
  cursor: pointer;
}

.uni-picker-view-content > * {
  height: var(--picker-view-column-indicator-height);
  overflow: hidden;
}

.uni-picker-view-indicator:after,
.uni-picker-view-indicator:before {
  content: ' ';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  color: #e5e5e5;
}

.uni-picker-view-indicator:before {
  top: 0;
  border-top: 1px solid #e5e5e5;
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.uni-picker-view-indicator:after {
  bottom: 0;
  border-bottom: 1px solid #e5e5e5;
  transform-origin: 0 100%;
  transform: scaleY(0.5);
}

.uni-picker-view-indicator:after,
.uni-picker-view-indicator:before {
  content: ' ';
  position: absolute;
  left: 0;
  right: 0;
  height: 1px;
  color: #e5e5e5;
}

@media (prefers-color-scheme: dark) {
  .uni-picker-view-indicator:before {
    border-top-color: var(--UI-FG-3);
  }
  .uni-picker-view-indicator:after {
    border-bottom-color: var(--UI-FG-3);
  }
  .uni-picker-view-mask {
    background-image: linear-gradient(
        180deg,
        rgba(35, 35, 35, 0.95),
        rgba(35, 35, 35, 0.6)
      ),
      linear-gradient(0deg, rgba(35, 35, 35, 0.95), rgba(35, 35, 35, 0.6));
  }
}
uni-scroll-view {
  display: block;
  width: 100%;
}

uni-scroll-view[hidden] {
  display: none;
}

.uni-scroll-view {
  position: relative;
  -webkit-overflow-scrolling: touch;
  width: 100%;
  /* display: flex; 时在安卓下会导致scrollWidth和offsetWidth一样 */
  height: 100%;
  max-height: inherit;
}

.uni-scroll-view-scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

.uni-scroll-view-scrollbar-hidden {
  -moz-scrollbars: none;
  scrollbar-width: none;
}

.uni-scroll-view-content {
  width: 100%;
  height: 100%;
}.uni-scroll-view-refresher {
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.uni-scroll-view-refresher-container {
  position: absolute;
  width: 100%;
  bottom: 0;
  display: flex;
  flex-direction: column-reverse;
}

.uni-scroll-view-refresh {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.uni-scroll-view-refresh-inner {
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.117647),
    0 1px 4px rgba(0, 0, 0, 0.117647);
}

.uni-scroll-view-refresh__spinner {
  transform-origin: center center;
  animation: uni-scroll-view-refresh-rotate 2s linear infinite;
}

.uni-scroll-view-refresh__spinner > circle {
  stroke: currentColor;
  stroke-linecap: round;
  animation: uni-scroll-view-refresh-dash 2s linear infinite;
}

@keyframes uni-scroll-view-refresh-rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes uni-scroll-view-refresh-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }

  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}
uni-swiper {
  display: block;
  height: 150px;
}

uni-swiper[hidden] {
  display: none;
}

.uni-swiper-wrapper {
  overflow: hidden;
  position: relative;
  width: 100%;
  height: 100%;
  transform: translateZ(0);
}

.uni-swiper-slides {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}

.uni-swiper-slide-frame {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  will-change: transform;
}

.uni-swiper-dots {
  position: absolute;
  font-size: 0;
}

.uni-swiper-dots-horizontal {
  left: 50%;
  bottom: 10px;
  text-align: center;
  white-space: nowrap;
  transform: translate(-50%, 0);
}

.uni-swiper-dots-horizontal .uni-swiper-dot {
  margin-right: 8px;
}

.uni-swiper-dots-horizontal .uni-swiper-dot:last-child {
  margin-right: 0;
}

.uni-swiper-dots-vertical {
  right: 10px;
  top: 50%;
  text-align: right;
  transform: translate(0, -50%);
}

.uni-swiper-dots-vertical .uni-swiper-dot {
  display: block;
  margin-bottom: 9px;
}

.uni-swiper-dots-vertical .uni-swiper-dot:last-child {
  margin-bottom: 0;
}

.uni-swiper-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  cursor: pointer;
  transition-property: background-color;
  transition-timing-function: ease;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
}

.uni-swiper-dot-active {
  background-color: #000000;
}

.uni-swiper-navigation {
  width: 26px;
  height: 26px;
  cursor: pointer;
  position: absolute;
  top: 50%;
  margin-top: -13px;
  display: flex;
  align-items: center;
  transition: all 0.2s;
  border-radius: 50%;
  opacity: 1;
}

.uni-swiper-navigation-disabled {
  opacity: 0.35;
  cursor: not-allowed;
}

.uni-swiper-navigation-hide {
  opacity: 0;
  cursor: auto;
  pointer-events: none;
}

.uni-swiper-navigation-prev {
  left: 10px;
}

.uni-swiper-navigation-prev svg {
  margin-left: -1px;
  left: 10px;
}

.uni-swiper-navigation-prev.uni-swiper-navigation-vertical {
  top: 18px;
  left: 50%;
  margin-left: -13px;
}

.uni-swiper-navigation-prev.uni-swiper-navigation-vertical svg {
  transform: rotate(90deg);
  margin-left: auto;
  margin-top: -2px;
}

.uni-swiper-navigation-next {
  right: 10px;
}

.uni-swiper-navigation-next svg {
  transform: rotate(180deg);
}

.uni-swiper-navigation-next.uni-swiper-navigation-vertical {
  top: auto;
  bottom: 5px;
  left: 50%;
  margin-left: -13px;
}

.uni-swiper-navigation-next.uni-swiper-navigation-vertical svg {
  margin-top: 2px;
  transform: rotate(270deg);
}
uni-swiper-item {
  display: block;
  overflow: hidden;
  will-change: transform;
  position: absolute;
  width: 100%;
  height: 100%;
  cursor: grab;
}

uni-swiper-item[hidden] {
  display: none;
}
uni-movable-area {
  display: block;
  position: relative;
  width: 10px;
  height: 10px;
}

uni-movable-area[hidden] {
  display: none;
}
uni-movable-view {
  display: inline-block;
  width: 10px;
  height: 10px;
  top: 0px;
  left: 0px;
  position: absolute;
  cursor: grab;
}

uni-movable-view[hidden] {
  display: none;
}
uni-icon {
  display: inline-block;
  font-size: 0;
  box-sizing: border-box;
}

uni-icon[hidden] {
  display: none;
}
uni-web-view {
  display: block;
}

uni-web-view.uni-webview--fullscreen {
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
}
uni-canvas {
  width: 300px;
  height: 150px;
  display: block;
  position: relative;
}

uni-canvas > .uni-canvas-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
uni-video {
  width: 300px;
  height: 225px;
  display: inline-block;
  line-height: 0;
  overflow: hidden;
  position: relative;
}

uni-video[hidden] {
  display: none;
}

.uni-video-container {
  width: 100%;
  height: 100%;
  background-color: black;
  display: inline-block;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
  object-position: inherit;
}

.uni-video-container.uni-video-type-fullscreen {
  position: fixed;
  z-index: 999;
}

.uni-video-video {
  width: 100%;
  height: 100%;
  object-position: inherit;
}

.uni-video-cover {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(1, 1, 1, 0.5);
  z-index: 1;
}

.uni-video-slots {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
}

.uni-video-cover-play-button {
  width: 75px;
  height: 75px;
  line-height: 75px;
  font-size: 56px;
  color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
}

.uni-video-cover-play-button::after {
  content: '\ea24';
}

.uni-video-cover-duration {
  color: #fff;
  font-size: 16px;
  line-height: 1;
  margin-top: 10px;
}

.uni-video-bar {
  height: 44px;
  /* background-color: rgba(0, 0, 0, 0.5); */
  background-image: linear-gradient(-180deg, transparent, rgba(0, 0, 0, 0.5));
  overflow: hidden;
  position: absolute;
  bottom: 0;
  right: 0;
  display: flex;
  display: flex;
  align-items: center;
  align-items: center;
  padding: 0 16px;
  z-index: 0;
  /* 解决全屏后被 video 遮挡的问题 */
  transform: translate3d(0, 0, 0);
}

.uni-video-bar.uni-video-bar-full {
  left: 0;
}

.uni-video-controls {
  display: flex;
  flex-grow: 1;
  margin: 0 8.5px;
  align-items: center;
}

.uni-video-control-button {
  width: 17px;
  height: 17px;
  line-height: 17px;
  padding: 0px 16px 0px 0px;
  margin-left: -6px;
  margin-right: -6px;
  box-sizing: content-box;
  cursor: pointer;
}

.uni-video-control-button::after {
  content: '';
  display: block;
  width: 100%;
  height: 100%;
  color: rgba(255, 255, 255, 0.5);
}

.uni-video-control-button.uni-video-control-button-play::after {
  content: '\ea24';
}

.uni-video-control-button.uni-video-control-button-pause::after {
  content: '\ea25';
}

.uni-video-current-time,
.uni-video-duration {
  height: 15px;
  line-height: 15px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.5);
}

.uni-video-progress-container {
  flex-grow: 2;
  position: relative;
}

.uni-video-progress {
  /* NOTE uni-video-progress-progressing height */
  height: 4px;
  margin: 21px 12px;
  border-radius: 20px;
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.uni-video-progress.uni-video-progress-progressing {
  height: 8px;
}

.uni-video-progress .uni-video-progress-played {
  background-color: #fff;
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}

.uni-video-progress-played,
.uni-video-progress-buffered {
  position: absolute;
  left: 0;
  top: 0;
  width: 0;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.3);
}

.uni-video-progress-buffered {
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
  /* transition: width 0.1s; */
}

.uni-video-ball {
  /* NOTE uni-video-ball-progressing width height */
  width: 8px;
  height: 8px;
  padding: 14px;
  position: absolute;
  box-sizing: content-box;
  left: 0%;
  margin-left: -16px;
}

.uni-video-ball.uni-video-ball-progressing {
  width: 16px;
  height: 16px;
}

.uni-video-inner {
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  border-radius: 50%;
  box-shadow: 0px 0px 2px #ccc;
}

.uni-video-danmu-button {
  width: 24px;
  height: 24px;
  line-height: 24px;
  font-size: 24px;
  white-space: nowrap;
  border-radius: 5px;
  margin: 0 2px;
  cursor: pointer;
  color: rgba(255, 255, 255, 0.5);
}

.uni-video-danmu-button::after {
  content: '\ea26';
}

.uni-video-danmu-button.uni-video-danmu-button-active::after {
  content: '\ea27';
}

.uni-video-fullscreen {
  width: 32px;
  height: 32px;
  line-height: 32px;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.5);
  box-sizing: content-box;
  cursor: pointer;
}

.uni-video-fullscreen::after {
  content: '\ea29';
}

.uni-video-fullscreen.uni-video-type-fullscreen::after {
  content: '\ea28';
}

.uni-video-danmu {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  margin-top: 14px;
  margin-bottom: 44px;
  font-size: 14px;
  line-height: 14px;
  overflow: visible;
}

.uni-video-danmu-item {
  line-height: 1;
  position: absolute;
  color: #ffffff;
  white-space: nowrap;
  left: 100%;
  transform: translatex(0);
  transition-property: left, transform;
  transition-duration: 3s;
  transition-timing-function: linear;
}

.uni-video-toast {
  pointer-events: none;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.6);
  color: #000000;
  display: none;
}

.uni-video-toast.uni-video-toast-progress {
  display: block;
  background-color: rgba(0, 0, 0, 0.5);
  color: rgba(255, 255, 255, 0.6);
  font-size: 24px;
  line-height: 18px;
  padding: 6px;
}

.uni-video-toast.uni-video-toast-progress .uni-video-toast-title-current-time {
  color: rgba(255, 255, 255, 0.9);
}

@font-face {
  font-family: 'uni-video-icon';
  src: url('data:font/ttf;charset=utf-8;base64,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')
    format('truetype');
}

.uni-video-icon {
  /* 解决自定义全局字体会覆盖按钮的问题 */
  font-family: 'uni-video-icon' !important;
  text-align: center;
}

.uni-video-loading {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  pointer-events: none;
}

.uni-video-toast-container {
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 22%;
  min-width: 100px;
  max-width: 200px;
  height: 30px;
  max-height: 30px;
  min-height: 6px;
  background-color: rgba(0, 0, 0, 0.4);
  box-shadow: 0px 0px 2px #ccc;
  margin: 5px auto 0;
  border-radius: 30px;
  overflow: hidden;
  transition-property: height;
  transition-duration: 0.2s;
  transition-timing-function: ease-in-out;
  opacity: 0.6;
}

.uni-video-toast-container.uni-video-toast-container-thin {
  height: 6px;
}

.uni-video-toast-container-thin .uni-video-toast-icon {
  display: none;
}

.uni-video-toast-icon {
  font-size: 20px;
  position: absolute;
  left: 10px;
  color: #222;
  z-index: 1;
}

.uni-video-toast-draw {
  height: 100%;
  background-color: #fff;
}
uni-picker {
  position: relative;
}

uni-picker>embed {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

uni-picker>.uni-picker-slot {
  position: relative;
}uni-map {
  position: relative;
  width: 300px;
  height: 150px;
  display: block;
}

uni-map[hidden] {
  display: none;
}
/* 处理高德地图 marker label 默认样式 */
.amap-marker-label{
  padding:0;
  border:none;
  background-color: transparent;
}
/* 处理高德地图 open-location icon 被遮挡问题 */
.amap-marker>.amap-icon>img{
  left:0!important;
  top:0!important;
}

.uni-map-control{
  position:absolute;
  width:0;
  height:0;
  top:0;
  left:0;
  z-index:999;
}
.uni-map-control-icon{
  position:absolute;
  max-width:initial;
}.uni-system-choose-location {
  display: block;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #f8f8f8;
  z-index: 999;
}

.uni-system-choose-location .map {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 300px;
}

.uni-system-choose-location .map-location {
  position: absolute;
  left: 50%;
  bottom: 50%;
  width: 32px;
  height: 52px;
  margin-left: -16px;
  cursor: pointer;
  background-size: 100%;
}

.uni-system-choose-location .map-move {
  position: absolute;
  bottom: 50px;
  right: 10px;
  width: 40px;
  height: 40px;
  box-sizing: border-box;
  line-height: 40px;
  background-color: white;
  border-radius: 50%;
  pointer-events: auto;
  cursor: pointer;
  box-shadow: 0px 0 5px 1px rgba(0, 0, 0, 0.3);
}

.uni-system-choose-location .map-move>svg {
  display: block;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 8px;
}

.uni-system-choose-location .nav {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(44px + var(--status-bar-height));
  background-color: transparent;
  background-image: linear-gradient( to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
}

.uni-system-choose-location .nav-btn {
  position: absolute;
  box-sizing: border-box;
  top: var(--status-bar-height);
  left: 0;
  width: 60px;
  height: 44px;
  padding: 6px;
  line-height: 32px;
  font-size: 26px;
  color: white;
  text-align: center;
  cursor: pointer;
}

.uni-system-choose-location .nav-btn.confirm {
  left: auto;
  right: 0;
}

.uni-system-choose-location .nav-btn.disable {
  opacity: 0.4;
}

.uni-system-choose-location .nav-btn>svg {
  display: block;
  width: 100%;
  height: 100%;
  /* line-height: inherit; */
  border-radius: 2px;
  box-sizing: border-box;
  padding: 3px;
}

.uni-system-choose-location .nav-btn.confirm>svg {
  background-color: #007aff;
  padding: 5px;
}

.uni-system-choose-location .menu {
  position: absolute;
  top: 300px;
  left: 0;
  width: 100%;
  bottom: 0;
  background-color: white;
}

.uni-system-choose-location .search {
  display: flex;
  flex-direction: row;
  height: 50px;
  padding: 8px;
  line-height: 34px;
  box-sizing: border-box;
  background-color: white;
}

.uni-system-choose-location .search-input {
  flex: 1;
  height: 100%;
  border-radius: 5px;
  padding: 0 5px;
  background: #ebebeb;
}

.uni-system-choose-location .search-btn {
  margin-left: 5px;
  color: #007aff;
  font-size: 17px;
  text-align: center;
}

.uni-system-choose-location .list {
  position: absolute;
  top: 50px;
  left: 0;
  width: 100%;
  bottom: 0;
  padding-bottom: 10px;
  /* background-color: #f6f6f6; */
}

.uni-system-choose-location .list-loading {
  display: flex;
  height: 50px;
  justify-content: center;
  align-items: center;
}

.uni-system-choose-location .list-item {
  position: relative;
  padding: 10px;
  padding-right: 40px;
  cursor: pointer;
}

.uni-system-choose-location .list-item>svg {
  display: none;
  position: absolute;
  top: 50%;
  right: 10px;
  width: 30px;
  height: 30px;
  margin-top: -15px;
  box-sizing: border-box;
  padding: 5px;
}

.uni-system-choose-location .list-item.selected>svg {
  display: block;
}

.uni-system-choose-location .list-item:not(:last-child)::after {
  position: absolute;
  content: "";
  height: 1px;
  left: 10px;
  bottom: 0;
  width: 100%;
  background-color: #d3d3d3;
}

.uni-system-choose-location .list-item-title {
  font-size: 14px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.uni-system-choose-location .list-item-detail {
  font-size: 12px;
  color: #808080;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

@media screen and (min-width: 800px) {
  .uni-system-choose-location .map {
    top: 0;
    height: 100%;
  }
  .uni-system-choose-location .map-move {
    bottom: 10px;
    right: 320px;
  }
  .uni-system-choose-location .menu {
    top: calc(54px + var(--status-bar-height));
    left: auto;
    right: 10px;
    width: 300px;
    bottom: 10px;
    max-height: 600px;
    box-shadow: 0px 0 20px 5px rgba(0, 0, 0, 0.3);
  }
}.uni-system-open-location {
  display: block;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: #f8f8f8;
  z-index: 999;
}

.uni-system-open-location .map {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  bottom: 80px;
  height: auto;
}

.uni-system-open-location .info {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 80px;
  background-color: white;
  padding: 15px;
  box-sizing: border-box;
  line-height: 1.5;
}

.uni-system-open-location .info>.name {
  font-size: 17px;
  color: #111111;
}

.uni-system-open-location .info>.address {
  font-size: 14px;
  color: #666666;
}

.uni-system-open-location .info>.nav {
  position: absolute;
  top: 50%;
  right: 15px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-top: -25px;
  background-color: #007aff;
}

.uni-system-open-location .info>.nav>svg {
  display: block;
  width: 100%;
  height: 100%;
  padding: 10px;
  box-sizing: border-box;
}

.uni-system-open-location .map-move {
  position: absolute;
  bottom: 50px;
  right: 10px;
  width: 40px;
  height: 40px;
  box-sizing: border-box;
  line-height: 40px;
  background-color: white;
  border-radius: 50%;
  pointer-events: auto;
  cursor: pointer;
  box-shadow: 0px 0 5px 1px rgba(0, 0, 0, 0.3);
}

.uni-system-open-location .map-move>svg {
  display: block;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 8px;
}

.uni-system-open-location .nav-btn-back {
  position: absolute;
  box-sizing: border-box;
  top: var(--status-bar-height);
  left: 0;
  width: 44px;
  height: 44px;
  padding: 6px;
  cursor: pointer;
}

.uni-system-open-location .nav-btn-back>svg {
  display: block;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  padding: 3px;
  box-sizing: border-box;
}

.uni-system-open-location .map-content {
  position: absolute;
  left: 0;
  top: 0px;
  width: 100%;
  bottom: 0;
  overflow: hidden;
}

.uni-system-open-location .map-content.fix-position {
  top: -74px;
  bottom: -44px;
}

.uni-system-open-location .map-content>iframe {
  width: 100%;
  height: 100%;
  border: none;
}

.uni-system-open-location .actTonav {
  position: absolute;
  right: 16px;
  bottom: 56px;
  width: 60px;
  height: 60px;
  border-radius: 60px;
}

.uni-system-open-location .nav-view {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.uni-system-open-location .nav-view-top-placeholder {
  width: 100%;
  height: var(--status-bar-height);
  background-color: white;
}

.uni-system-open-location .nav-view-frame {
  width: 100%;
  flex: 1;
}uni-cover-image {
  display: block;
  line-height: 1.2;
  overflow: hidden;
  height: 100%;
  width: 100%;
  pointer-events: auto;
}

uni-cover-image img {
  width: 100%;
  height: 100%;
}

uni-cover-image[hidden] {
  display: none;
}

uni-cover-image .uni-cover-image {
  width: 100%;
  height: 100%;
  text-overflow: inherit;
  overflow: inherit;
  white-space: nowrap;
  align-items: inherit;
  justify-content: inherit;
  flex-direction: inherit;
  font-size: 0;
  display: inherit;
}
uni-cover-view {
  display: block;
  line-height: 1.2;
  overflow: hidden;
  white-space: nowrap;
  pointer-events: auto;
}

uni-cover-view[hidden] {
  display: none;
}

uni-cover-view .uni-cover-view {
  width: 100%;
  height: 100%;
  text-overflow: inherit;
  overflow: hidden;
  white-space: inherit;
  align-items: inherit;
  justify-content: inherit;
  flex-direction: inherit;
  flex-wrap: inherit;
  display: inherit;
  overflow: inherit;
}uni-ad {
  position: relative;
  display: block;
}

uni-ad>embed {
  opacity: 0;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
