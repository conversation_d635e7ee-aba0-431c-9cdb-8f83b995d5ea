{"name": "wish-backend", "version": "1.0.0", "description": "愿境应用后端API服务 - 基于Node.js + Express + MySQL + Socket.IO", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "db:migrate": "node src/database/migrate.js", "db:seed": "node src/database/seed.js", "db:reset": "node src/database/reset.js"}, "keywords": ["wish", "api", "backend", "nodejs", "express", "mysql", "sequelize", "socket.io", "realtime", "chat"], "author": "Wish Team", "license": "MIT", "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "fs-extra": "^11.2.0", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "nodemailer": "^6.9.7", "path": "^0.12.7", "qrcode": "^1.5.3", "redis": "^4.6.10", "sequelize": "^6.35.2", "sharp": "^0.33.5", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/wish-team/wish-backend.git"}, "bugs": {"url": "https://github.com/wish-team/wish-backend/issues"}, "homepage": "https://github.com/wish-team/wish-backend#readme"}