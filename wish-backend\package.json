{"name": "wish-backend", "version": "1.0.0", "description": "愿境应用后端API服务", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js"}, "keywords": ["wish", "blessing", "community", "api", "nodejs", "express"], "author": "Wish Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "redis": "^4.6.10", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "joi": "^17.11.0", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.1", "dotenv": "^16.3.1", "winston": "^3.11.0", "express-winston": "^4.2.0"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}}