/**
 * 安全功能相关API
 * 包括举报、内容审核、敏感词过滤等接口
 */

import request from './request'

export const safetyAPI = {
  /**
   * 提交举报
   */
  submitReport(reportData) {
    return request.post('/safety/reports', reportData)
  },

  /**
   * 获取举报对象信息
   */
  getReportTargetInfo(type, targetId) {
    return request.get(`/safety/reports/target-info`, {
      params: { type, targetId }
    })
  },

  /**
   * 获取举报列表
   */
  getReports(params = {}) {
    return request.get('/safety/reports', { params })
  },

  /**
   * 获取举报详情
   */
  getReportDetail(reportId) {
    return request.get(`/safety/reports/${reportId}`)
  },

  /**
   * 处理举报
   */
  processReport(reportId, processData) {
    return request.put(`/safety/reports/${reportId}/process`, processData)
  },

  /**
   * 批量处理举报
   */
  batchProcessReports(batchData) {
    return request.post('/safety/reports/batch-process', batchData)
  },

  /**
   * 获取审核统计
   */
  getModerationStats() {
    return request.get('/safety/moderation/stats')
  },

  /**
   * 检测内容安全性
   */
  checkContentSafety(contentData) {
    return request.post('/safety/content/check', contentData)
  },

  /**
   * 获取敏感词库
   */
  getWordLibrary() {
    return request.get('/safety/word-library')
  },

  /**
   * 更新敏感词库
   */
  updateWordLibrary(library) {
    return request.put('/safety/word-library', library)
  },

  /**
   * 获取过滤配置
   */
  getFilterConfig() {
    return request.get('/safety/filter-config')
  },

  /**
   * 更新过滤配置
   */
  updateFilterConfig(config) {
    return request.put('/safety/filter-config', config)
  },

  /**
   * 拉黑用户
   */
  blockUser(userId, reason = '') {
    return request.post(`/safety/users/${userId}/block`, { reason })
  },

  /**
   * 解除拉黑
   */
  unblockUser(userId) {
    return request.delete(`/safety/users/${userId}/block`)
  },

  /**
   * 获取拉黑列表
   */
  getBlockedUsers(params = {}) {
    return request.get('/safety/blocked-users', { params })
  },

  /**
   * 删除违规内容
   */
  deleteViolationContent(contentData) {
    return request.post('/safety/content/delete', contentData)
  },

  /**
   * 获取违规记录
   */
  getViolationRecords(params = {}) {
    return request.get('/safety/violation-records', { params })
  },

  /**
   * 上传举报证据
   */
  uploadReportEvidence(filePath) {
    return request.upload('/safety/reports/upload-evidence', filePath)
  },

  /**
   * 获取举报统计
   */
  getReportStats(params = {}) {
    return request.get('/safety/reports/stats', { params })
  },

  /**
   * 获取用户举报历史
   */
  getUserReportHistory(userId, params = {}) {
    return request.get(`/safety/users/${userId}/report-history`, { params })
  },

  /**
   * 获取内容审核日志
   */
  getModerationLogs(params = {}) {
    return request.get('/safety/moderation/logs', { params })
  },

  /**
   * 添加敏感词
   */
  addSensitiveWords(words) {
    return request.post('/safety/word-library/sensitive', { words })
  },

  /**
   * 删除敏感词
   */
  deleteSensitiveWords(words) {
    return request.delete('/safety/word-library/sensitive', { data: { words } })
  },

  /**
   * 添加违禁词
   */
  addBannedWords(words) {
    return request.post('/safety/word-library/banned', { words })
  },

  /**
   * 删除违禁词
   */
  deleteBannedWords(words) {
    return request.delete('/safety/word-library/banned', { data: { words } })
  },

  /**
   * 获取内容风险评估
   */
  assessContentRisk(content) {
    return request.post('/safety/content/risk-assessment', { content })
  },

  /**
   * 获取用户安全等级
   */
  getUserSafetyLevel(userId) {
    return request.get(`/safety/users/${userId}/safety-level`)
  },

  /**
   * 更新用户安全等级
   */
  updateUserSafetyLevel(userId, level, reason = '') {
    return request.put(`/safety/users/${userId}/safety-level`, { level, reason })
  },

  /**
   * 获取安全规则
   */
  getSafetyRules() {
    return request.get('/safety/rules')
  },

  /**
   * 更新安全规则
   */
  updateSafetyRules(rules) {
    return request.put('/safety/rules', rules)
  },

  /**
   * 获取自动审核配置
   */
  getAutoModerationConfig() {
    return request.get('/safety/auto-moderation/config')
  },

  /**
   * 更新自动审核配置
   */
  updateAutoModerationConfig(config) {
    return request.put('/safety/auto-moderation/config', config)
  },

  /**
   * 触发内容重新审核
   */
  triggerContentReview(contentType, contentId) {
    return request.post('/safety/content/re-review', {
      contentType,
      contentId
    })
  },

  /**
   * 获取审核队列状态
   */
  getModerationQueueStatus() {
    return request.get('/safety/moderation/queue-status')
  },

  /**
   * 导出举报数据
   */
  exportReportData(params = {}) {
    return request.get('/safety/reports/export', { 
      params,
      responseType: 'blob'
    })
  },

  /**
   * 导入敏感词库
   */
  importWordLibrary(file) {
    return request.upload('/safety/word-library/import', file)
  }
}
