/**
 * 愿境应用API接口定义
 * 统一管理所有API接口调用
 */

import request from './request'
import { API_CONFIG } from '@/config'

const { endpoints } = API_CONFIG

/**
 * 用户认证相关API
 */
export const authAPI = {
  // 手机号登录
  login(phone, password) {
    return request.post(endpoints.auth.login, {
      phone,
      password
    })
  },
  
  // 手机号注册
  register(phone, password, nickname, verifyCode) {
    return request.post(endpoints.auth.register, {
      phone,
      password,
      nickname,
      verifyCode
    })
  },
  
  // 发送验证码
  sendVerifyCode(phone, type = 'register') {
    return request.post('/auth/send-code', {
      phone,
      type
    })
  },
  
  // 退出登录
  logout() {
    return request.post(endpoints.auth.logout)
  },
  
  // 刷新token
  refreshToken() {
    return request.post(endpoints.auth.refresh)
  },
  
  // 获取用户信息
  getProfile() {
    return request.get(endpoints.auth.profile)
  }
}

/**
 * 用户系统相关API
 */
export const userAPI = {
  // 获取用户详情
  getProfile(userId) {
    return request.get(endpoints.user.profile, { userId })
  },
  
  // 更新用户信息
  updateProfile(data) {
    return request.put(endpoints.user.update, data)
  },
  
  // 切换用户角色
  switchRole(role) {
    return request.post(endpoints.user.switch, { role })
  },
  
  // 上传头像
  uploadAvatar(filePath) {
    return request.upload('/user/upload-avatar', filePath)
  },
  
  // 获取用户统计数据
  getUserStats(userId) {
    return request.get('/user/stats', { userId })
  }
}

/**
 * 祈愿相关API
 */
export const wishAPI = {
  // 创建心愿
  createWish(data) {
    return request.post(endpoints.wish.create, data)
  },
  
  // 获取心愿列表
  getWishList(params = {}) {
    const defaultParams = {
      page: 1,
      pageSize: 20,
      sortBy: 'latest', // latest, hot, random
      tag: '',
      userId: ''
    }
    return request.get(endpoints.wish.list, { ...defaultParams, ...params })
  },
  
  // 获取心愿详情
  getWishDetail(wishId) {
    return request.get(endpoints.wish.detail, { wishId })
  },
  
  // 删除心愿
  deleteWish(wishId) {
    return request.delete(`${endpoints.wish.delete}/${wishId}`)
  },
  
  // 助力心愿
  supportWish(wishId) {
    return request.post(endpoints.wish.support, { wishId })
  },
  
  // 获取我的心愿列表
  getMyWishes(params = {}) {
    const defaultParams = {
      page: 1,
      pageSize: 20,
      status: 'all' // all, pending, blessed
    }
    return request.get('/wish/my-wishes', { ...defaultParams, ...params })
  },
  
  // 搜索心愿
  searchWishes(keyword, params = {}) {
    return request.get('/wish/search', {
      keyword,
      page: 1,
      pageSize: 20,
      ...params
    })
  }
}

/**
 * 守护者相关API
 */
export const guardianAPI = {
  // 赐福心愿
  blessWish(data) {
    return request.post(endpoints.guardian.bless, data)
  },
  
  // 获取赐福列表
  getBlessingList(params = {}) {
    const defaultParams = {
      page: 1,
      pageSize: 20,
      userId: ''
    }
    return request.get(endpoints.guardian.list, { ...defaultParams, ...params })
  },
  
  // 获取我的赐福列表
  getMyBlessings(params = {}) {
    const defaultParams = {
      page: 1,
      pageSize: 20
    }
    return request.get('/guardian/my-blessings', { ...defaultParams, ...params })
  },
  
  // 反馈赐福（叩谢）
  feedbackBlessing(blessingId, feedback) {
    return request.post(endpoints.guardian.feedback, {
      blessingId,
      feedback
    })
  },
  
  // 获取赐福卡片模板
  getBlessingTemplates() {
    return request.get('/guardian/blessing-templates')
  },
  
  // 上传赐福图片
  uploadBlessingImage(filePath) {
    return request.upload('/guardian/upload-blessing-image', filePath)
  }
}

/**
 * 社交功能相关API
 */
export const socialAPI = {
  // 获取排行榜
  getLeaderboard(params) {
    return request.get('/social/leaderboard', { params })
  },

  // 获取评论列表
  getComments(params) {
    return request.get('/social/comments', { params })
  },

  // 发表评论
  createComment(commentData) {
    return request.post('/social/comments', commentData)
  },

  // 点赞评论
  likeComment(commentId) {
    return request.post(`/social/comments/${commentId}/like`)
  },

  // 取消点赞评论
  unlikeComment(commentId) {
    return request.delete(`/social/comments/${commentId}/like`)
  },

  // 删除评论
  deleteComment(commentId) {
    return request.delete(`/social/comments/${commentId}`)
  },

  // 感谢赐福
  thankBlessing(blessingId, feedback = '') {
    return request.post(`/social/blessings/${blessingId}/thank`, { feedback })
  },

  // 获取对话列表
  getConversations(params = {}) {
    return request.get('/social/conversations', { params })
  },

  // 创建对话
  createConversation(userId) {
    return request.post('/social/conversations', { userId })
  },

  // 获取消息列表
  getMessages(params = {}) {
    return request.get('/social/messages', { params })
  },

  // 发送消息
  sendMessage(messageData) {
    return request.post('/social/messages', messageData)
  },

  // 上传消息图片
  uploadMessageImage(filePath) {
    return request.upload('/social/messages/upload-image', filePath)
  },

  // 搜索用户
  searchUsers(keyword) {
    return request.get('/social/users/search', {
      params: { keyword }
    })
  },

  // 获取用户信息
  getUserInfo(userId) {
    return request.get(`/social/users/${userId}`)
  },

  // 获取消息数量统计
  getMessageCounts() {
    return request.get('/social/messages/counts')
  },

  // 置顶/取消置顶对话
  toggleConversationPin(conversationId) {
    return request.put(`/social/conversations/${conversationId}/pin`)
  },

  // 免打扰/取消免打扰对话
  toggleConversationMute(conversationId) {
    return request.put(`/social/conversations/${conversationId}/mute`)
  },

  // 标记对话为已读
  markConversationRead(conversationId) {
    return request.put(`/social/conversations/${conversationId}/read`)
  },

  // 标记消息为已读
  markMessagesAsRead(conversationId) {
    return request.put(`/social/conversations/${conversationId}/messages/read`)
  },

  // 删除对话
  deleteConversation(conversationId) {
    return request.delete(`/social/conversations/${conversationId}`)
  },

  // 清空消息
  clearMessages(conversationId) {
    return request.delete(`/social/conversations/${conversationId}/messages`)
  },

  // 拉黑用户
  blockUser(userId) {
    return request.post(`/social/users/${userId}/block`)
  },

  // 标记消息已读（兼容旧接口）
  markMessageRead(messageId) {
    return request.put(`/social/messages/${messageId}/read`)
  }
}

/**
 * 系统功能相关API
 */
export const systemAPI = {
  // 每日签到
  dailyCheckIn() {
    return request.post('/system/daily-checkin')
  },
  
  // 获取签到状态
  getCheckInStatus() {
    return request.get('/system/checkin-status')
  },
  
  // 举报内容
  reportContent(data) {
    return request.post('/system/report', data)
  },
  
  // 获取系统通知
  getNotifications(params = {}) {
    const defaultParams = {
      page: 1,
      pageSize: 20,
      type: 'all'
    }
    return request.get('/system/notifications', { ...defaultParams, ...params })
  },
  
  // 获取应用配置
  getAppConfig() {
    return request.get('/system/config')
  },
  
  // 获取标签列表
  getTags() {
    return request.get('/system/tags')
  },
  
  // 意见反馈
  submitFeedback(data) {
    return request.post('/system/feedback', data)
  }
}

/**
 * 活动相关API
 */
export const eventAPI = {
  // 获取活动列表
  getEvents(params = {}) {
    const defaultParams = {
      page: 1,
      pageSize: 20,
      status: 'active' // active, ended, all
    }
    return request.get('/event/list', { ...defaultParams, ...params })
  },
  
  // 获取活动详情
  getEventDetail(eventId) {
    return request.get(`/event/detail/${eventId}`)
  },
  
  // 参与活动
  joinEvent(eventId, data = {}) {
    return request.post(`/event/join/${eventId}`, data)
  },
  
  // 获取活动参与记录
  getEventParticipation(eventId) {
    return request.get(`/event/participation/${eventId}`)
  }
}

// 导出所有API
export default {
  auth: authAPI,
  user: userAPI,
  wish: wishAPI,
  guardian: guardianAPI,
  social: socialAPI,
  system: systemAPI,
  event: eventAPI
}
