/**
 * 愿境应用API接口定义
 * 统一管理所有API接口调用
 */

// 导入各模块API
import authAPI from './auth'
import wishesAPI from './wishes'
import usersAPI from './users'
import systemAPI from './system'
import eventAPI from './events'
import guardianAPI from './guardian'
import { socialAPI } from './social'
import { safetyAPI } from './safety'

// 重新导出认证API（保持向后兼容）
export { authAPI }

// 导出新的模块化API
export { wishesAPI, usersAPI, systemAPI, eventAPI, guardianAPI, socialAPI, safetyAPI }

// 为了向后兼容，创建一些别名
export const userAPI = usersAPI
export const wishAPI = wishesAPI

// 导出所有API的默认对象
export default {
  auth: authAPI,
  wishes: wishesAPI,
  users: usersAPI,
  system: systemAPI,
  event: eventAPI,
  guardian: guardianAPI,
  social: socialAPI,
  safety: safetyAPI,
  // 别名
  user: usersAPI,
  wish: wishesAPI
}
