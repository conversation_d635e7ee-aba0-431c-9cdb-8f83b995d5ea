<!--
  愿境按钮组件
  支持多种样式和状态的按钮组件
-->
<template>
  <button 
    class="wish-button"
    :class="[
      `wish-button--${type}`,
      `wish-button--${size}`,
      {
        'wish-button--disabled': disabled,
        'wish-button--loading': loading,
        'wish-button--round': round,
        'wish-button--plain': plain
      }
    ]"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <!-- 加载状态 -->
    <view v-if="loading" class="wish-button__loading">
      <view class="wish-button__loading-icon"></view>
    </view>
    
    <!-- 图标 -->
    <view v-if="icon && !loading" class="wish-button__icon">
      <image :src="icon" class="wish-button__icon-img" />
    </view>
    
    <!-- 按钮文字 -->
    <view class="wish-button__text">
      <slot>{{ text }}</slot>
    </view>
  </button>
</template>

<script>
export default {
  name: 'WishButton',
  props: {
    // 按钮类型
    type: {
      type: String,
      default: 'primary',
      validator: (value) => ['primary', 'secondary', 'success', 'warning', 'error', 'info', 'ghost'].includes(value)
    },
    // 按钮大小
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    // 按钮文字
    text: {
      type: String,
      default: ''
    },
    // 图标
    icon: {
      type: String,
      default: ''
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 是否加载中
    loading: {
      type: Boolean,
      default: false
    },
    // 是否圆角
    round: {
      type: Boolean,
      default: false
    },
    // 是否朴素按钮
    plain: {
      type: Boolean,
      default: false
    }
  },
  
  methods: {
    handleClick(event) {
      if (this.disabled || this.loading) {
        return
      }
      this.$emit('click', event)
    }
  }
}
</script>

<style lang="scss" scoped>
.wish-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: $wish-radius-md;
  font-size: $wish-font-md;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:active::before {
    opacity: 1;
  }
  
  // 按钮类型样式
  &--primary {
    background-color: $wish-color-primary;
    color: $wish-text-inverse;
    
    &.wish-button--plain {
      background-color: transparent;
      border: 2rpx solid $wish-color-primary;
      color: $wish-color-primary;
    }
  }
  
  &--secondary {
    background-color: $wish-color-secondary;
    color: $wish-text-inverse;
    
    &.wish-button--plain {
      background-color: transparent;
      border: 2rpx solid $wish-color-secondary;
      color: $wish-color-secondary;
    }
  }
  
  &--success {
    background-color: $wish-color-success;
    color: $wish-text-inverse;
    
    &.wish-button--plain {
      background-color: transparent;
      border: 2rpx solid $wish-color-success;
      color: $wish-color-success;
    }
  }
  
  &--warning {
    background-color: $wish-color-warning;
    color: $wish-text-primary;
    
    &.wish-button--plain {
      background-color: transparent;
      border: 2rpx solid $wish-color-warning;
      color: $wish-color-warning;
    }
  }
  
  &--error {
    background-color: $wish-color-error;
    color: $wish-text-inverse;
    
    &.wish-button--plain {
      background-color: transparent;
      border: 2rpx solid $wish-color-error;
      color: $wish-color-error;
    }
  }
  
  &--info {
    background-color: $wish-color-info;
    color: $wish-text-inverse;
    
    &.wish-button--plain {
      background-color: transparent;
      border: 2rpx solid $wish-color-info;
      color: $wish-color-info;
    }
  }
  
  &--ghost {
    background-color: transparent;
    border: 2rpx solid $wish-border-medium;
    color: $wish-text-primary;
  }
  
  // 按钮大小样式
  &--small {
    padding: $wish-spacing-xs $wish-spacing-sm;
    font-size: $wish-font-sm;
    min-height: 60rpx;
  }
  
  &--medium {
    padding: $wish-spacing-sm $wish-spacing-md;
    font-size: $wish-font-md;
    min-height: 80rpx;
  }
  
  &--large {
    padding: $wish-spacing-md $wish-spacing-lg;
    font-size: $wish-font-lg;
    min-height: 100rpx;
  }
  
  // 圆角样式
  &--round {
    border-radius: 50rpx;
  }
  
  // 禁用状态
  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  // 加载状态
  &--loading {
    cursor: not-allowed;
  }
}

.wish-button__loading {
  margin-right: $wish-spacing-xs;
}

.wish-button__loading-icon {
  width: 32rpx;
  height: 32rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-top-color: currentColor;
  border-radius: 50%;
  animation: wish-button-loading 1s linear infinite;
}

.wish-button__icon {
  margin-right: $wish-spacing-xs;
}

.wish-button__icon-img {
  width: 32rpx;
  height: 32rpx;
}

.wish-button__text {
  flex: 1;
}

@keyframes wish-button-loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
