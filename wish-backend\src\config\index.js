/**
 * 应用配置文件
 * 统一管理所有配置项
 */

require('dotenv').config();

module.exports = {
  // 基础配置
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT, 10) || 3000,
  
  // 数据库配置 (MySQL)
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT, 10) || 3306,
    database: process.env.DB_NAME || 'wish_app',
    username: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    dialect: 'mysql',
    pool: {
      max: parseInt(process.env.DB_POOL_MAX, 10) || 10,
      min: parseInt(process.env.DB_POOL_MIN, 10) || 0,
      acquire: parseInt(process.env.DB_POOL_ACQUIRE, 10) || 30000,
      idle: parseInt(process.env.DB_POOL_IDLE, 10) || 10000
    },
    logging: process.env.NODE_ENV === 'development' ? console.log : false,
    timezone: '+08:00'
  },

  // Redis配置 (可选)
  redis: {
    enabled: process.env.REDIS_ENABLED === 'true',
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB, 10) || 0,
    keyPrefix: 'wish_app:',
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
    issuer: 'wish-app',
    audience: 'wish-users'
  },

  // 密码加密配置
  bcrypt: {
    saltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS, 10) || 12
  },

  // CORS配置
  cors: {
    origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : [
      'http://localhost:8080',
      'http://localhost:5173',
      'http://127.0.0.1:8080',
      'http://127.0.0.1:5173'
    ],
    credentials: true,
    optionsSuccessStatus: 200,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin']
  },

  // 速率限制配置
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000, // 15分钟
    max: parseInt(process.env.RATE_LIMIT_MAX, 10) || 100, // 最多100次请求
    message: {
      error: '请求过于频繁，请稍后再试',
      code: 'RATE_LIMIT_EXCEEDED'
    },
    standardHeaders: true,
    legacyHeaders: false
  },

  // 文件上传配置
  upload: {
    maxFileSize: parseInt(process.env.UPLOAD_MAX_FILE_SIZE, 10) || 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    destination: process.env.UPLOAD_DESTINATION || 'uploads/',
    publicPath: process.env.UPLOAD_PUBLIC_PATH || '/uploads/'
  },

  // 邮件配置
  email: {
    enabled: process.env.EMAIL_ENABLED === 'true',
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT, 10) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || ''
      }
    },
    from: process.env.EMAIL_FROM || '<EMAIL>'
  },

  // 短信配置
  sms: {
    enabled: process.env.SMS_ENABLED === 'true',
    provider: process.env.SMS_PROVIDER || 'aliyun',
    accessKeyId: process.env.SMS_ACCESS_KEY_ID || '',
    accessKeySecret: process.env.SMS_ACCESS_KEY_SECRET || '',
    signName: process.env.SMS_SIGN_NAME || '愿境',
    templateCode: process.env.SMS_TEMPLATE_CODE || ''
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: {
      enabled: process.env.LOG_FILE_ENABLED === 'true',
      filename: process.env.LOG_FILE_NAME || 'logs/app.log',
      maxsize: parseInt(process.env.LOG_FILE_MAX_SIZE, 10) || 5 * 1024 * 1024,
      maxFiles: parseInt(process.env.LOG_FILE_MAX_FILES, 10) || 5
    },
    console: {
      enabled: process.env.LOG_CONSOLE_ENABLED !== 'false'
    }
  },

  // 缓存配置
  cache: {
    ttl: {
      default: parseInt(process.env.CACHE_TTL_DEFAULT, 10) || 300, // 5分钟
      user: parseInt(process.env.CACHE_TTL_USER, 10) || 600, // 10分钟
      wish: parseInt(process.env.CACHE_TTL_WISH, 10) || 180, // 3分钟
      leaderboard: parseInt(process.env.CACHE_TTL_LEADERBOARD, 10) || 900 // 15分钟
    }
  },

  // 分页配置
  pagination: {
    defaultLimit: parseInt(process.env.PAGINATION_DEFAULT_LIMIT, 10) || 20,
    maxLimit: parseInt(process.env.PAGINATION_MAX_LIMIT, 10) || 100
  },

  // 安全配置
  security: {
    password: {
      minLength: parseInt(process.env.PASSWORD_MIN_LENGTH, 10) || 8,
      requireUppercase: process.env.PASSWORD_REQUIRE_UPPERCASE === 'true',
      requireLowercase: process.env.PASSWORD_REQUIRE_LOWERCASE === 'true',
      requireNumbers: process.env.PASSWORD_REQUIRE_NUMBERS === 'true',
      requireSymbols: process.env.PASSWORD_REQUIRE_SYMBOLS === 'true'
    },
    accountLock: {
      maxAttempts: parseInt(process.env.ACCOUNT_LOCK_MAX_ATTEMPTS, 10) || 5,
      lockTime: parseInt(process.env.ACCOUNT_LOCK_TIME, 10) || 30 * 60 * 1000 // 30分钟
    },
    captcha: {
      length: parseInt(process.env.CAPTCHA_LENGTH, 10) || 6,
      expiresIn: parseInt(process.env.CAPTCHA_EXPIRES_IN, 10) || 5 * 60 * 1000, // 5分钟
      maxAttempts: parseInt(process.env.CAPTCHA_MAX_ATTEMPTS, 10) || 3
    }
  },

  // Socket.IO配置
  socket: {
    enabled: process.env.SOCKET_ENABLED !== 'false',
    cors: {
      origin: process.env.SOCKET_CORS_ORIGIN ? process.env.SOCKET_CORS_ORIGIN.split(',') : [
        'http://localhost:8080',
        'http://localhost:5173',
        'http://127.0.0.1:8080',
        'http://127.0.0.1:5173'
      ],
      credentials: true,
      methods: ['GET', 'POST']
    },
    pingTimeout: parseInt(process.env.SOCKET_PING_TIMEOUT, 10) || 60000,
    pingInterval: parseInt(process.env.SOCKET_PING_INTERVAL, 10) || 25000
  },

  // 业务配置
  business: {
    wishPower: {
      dailyRitual: parseInt(process.env.WISH_POWER_DAILY_RITUAL, 10) || 10,
      shareWish: parseInt(process.env.WISH_POWER_SHARE_WISH, 10) || 5,
      receiveBlessing: parseInt(process.env.WISH_POWER_RECEIVE_BLESSING, 10) || 3
    },
    merit: {
      blessWish: parseInt(process.env.MERIT_BLESS_WISH, 10) || 10,
      blessWithImage: parseInt(process.env.MERIT_BLESS_WITH_IMAGE, 10) || 5,
      blessWithLongText: parseInt(process.env.MERIT_BLESS_WITH_LONG_TEXT, 10) || 5,
      receiveThanks: parseInt(process.env.MERIT_RECEIVE_THANKS, 10) || 2
    },
    leaderboard: {
      updateInterval: parseInt(process.env.LEADERBOARD_UPDATE_INTERVAL, 10) || 60 * 60 * 1000, // 1小时
      topCount: parseInt(process.env.LEADERBOARD_TOP_COUNT, 10) || 100
    }
  },

  // 第三方服务配置
  thirdParty: {
    oss: {
      enabled: process.env.OSS_ENABLED === 'true',
      region: process.env.OSS_REGION || 'oss-cn-hangzhou',
      accessKeyId: process.env.OSS_ACCESS_KEY_ID || '',
      accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET || '',
      bucket: process.env.OSS_BUCKET || ''
    },
    wechat: {
      enabled: process.env.WECHAT_ENABLED === 'true',
      appId: process.env.WECHAT_APP_ID || '',
      appSecret: process.env.WECHAT_APP_SECRET || ''
    }
  }
};
