/**
 * 应用配置文件
 * 统一管理所有配置项
 */

require('dotenv').config();

const config = {
  // 环境配置
  env: process.env.NODE_ENV || 'development',
  port: parseInt(process.env.PORT, 10) || 3000,

  // 数据库配置
  database: {
    mongodb: {
      uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/wish_app',
      options: {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        maxPoolSize: 10,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      }
    }
  },

  // Redis配置
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT, 10) || 6379,
    password: process.env.REDIS_PASSWORD || '',
    db: parseInt(process.env.REDIS_DB, 10) || 0,
    keyPrefix: 'wish_app:',
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3,
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d',
    issuer: 'wish-app',
    audience: 'wish-app-users'
  },

  // 密码加密配置
  bcrypt: {
    saltRounds: parseInt(process.env.BCRYPT_SALT_ROUNDS, 10) || 12
  },

  // CORS配置
  cors: {
    origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : [
      'http://localhost:8080',
      'http://localhost:3000',
      'https://wish.example.com'
    ]
  },

  // 速率限制配置
  rateLimit: {
    max: parseInt(process.env.RATE_LIMIT_MAX, 10) || 100,
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS, 10) || 15 * 60 * 1000
  },

  // 文件上传配置
  upload: {
    maxFileSize: parseInt(process.env.UPLOAD_MAX_FILE_SIZE, 10) || 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    destination: process.env.UPLOAD_DESTINATION || 'uploads/',
    publicPath: process.env.UPLOAD_PUBLIC_PATH || '/uploads/'
  },

  // 邮件配置
  email: {
    smtp: {
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT, 10) || 587,
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER || '',
        pass: process.env.SMTP_PASS || ''
      }
    },
    from: process.env.EMAIL_FROM || '<EMAIL>',
    templates: {
      verification: 'email-verification',
      passwordReset: 'password-reset',
      welcome: 'welcome'
    }
  },

  // 短信配置
  sms: {
    provider: process.env.SMS_PROVIDER || 'aliyun',
    accessKeyId: process.env.SMS_ACCESS_KEY_ID || '',
    accessKeySecret: process.env.SMS_ACCESS_KEY_SECRET || '',
    signName: process.env.SMS_SIGN_NAME || '愿境',
    templates: {
      verification: process.env.SMS_TEMPLATE_VERIFICATION || 'SMS_123456789'
    }
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: {
      enabled: process.env.LOG_FILE_ENABLED === 'true',
      filename: process.env.LOG_FILE_NAME || 'logs/app.log',
      maxsize: parseInt(process.env.LOG_FILE_MAX_SIZE, 10) || 5242880, // 5MB
      maxFiles: parseInt(process.env.LOG_FILE_MAX_FILES, 10) || 5
    },
    console: {
      enabled: process.env.LOG_CONSOLE_ENABLED !== 'false'
    }
  },

  // 缓存配置
  cache: {
    ttl: {
      default: parseInt(process.env.CACHE_TTL_DEFAULT, 10) || 300, // 5分钟
      user: parseInt(process.env.CACHE_TTL_USER, 10) || 600, // 10分钟
      wish: parseInt(process.env.CACHE_TTL_WISH, 10) || 180, // 3分钟
      leaderboard: parseInt(process.env.CACHE_TTL_LEADERBOARD, 10) || 900 // 15分钟
    }
  },

  // 分页配置
  pagination: {
    defaultLimit: parseInt(process.env.PAGINATION_DEFAULT_LIMIT, 10) || 20,
    maxLimit: parseInt(process.env.PAGINATION_MAX_LIMIT, 10) || 100
  },

  // 安全配置
  security: {
    // 密码策略
    password: {
      minLength: parseInt(process.env.PASSWORD_MIN_LENGTH, 10) || 8,
      requireUppercase: process.env.PASSWORD_REQUIRE_UPPERCASE === 'true',
      requireLowercase: process.env.PASSWORD_REQUIRE_LOWERCASE === 'true',
      requireNumbers: process.env.PASSWORD_REQUIRE_NUMBERS === 'true',
      requireSymbols: process.env.PASSWORD_REQUIRE_SYMBOLS === 'true'
    },
    // 账户锁定策略
    accountLock: {
      maxAttempts: parseInt(process.env.ACCOUNT_LOCK_MAX_ATTEMPTS, 10) || 5,
      lockTime: parseInt(process.env.ACCOUNT_LOCK_TIME, 10) || 30 * 60 * 1000 // 30分钟
    },
    // 验证码配置
    captcha: {
      length: parseInt(process.env.CAPTCHA_LENGTH, 10) || 6,
      expiresIn: parseInt(process.env.CAPTCHA_EXPIRES_IN, 10) || 5 * 60 * 1000, // 5分钟
      maxAttempts: parseInt(process.env.CAPTCHA_MAX_ATTEMPTS, 10) || 3
    }
  },

  // 内容审核配置
  moderation: {
    autoReview: process.env.MODERATION_AUTO_REVIEW === 'true',
    strictMode: process.env.MODERATION_STRICT_MODE === 'true',
    queueSize: parseInt(process.env.MODERATION_QUEUE_SIZE, 10) || 1000,
    batchSize: parseInt(process.env.MODERATION_BATCH_SIZE, 10) || 50
  },

  // 推送通知配置
  push: {
    enabled: process.env.PUSH_ENABLED === 'true',
    provider: process.env.PUSH_PROVIDER || 'fcm',
    fcm: {
      serverKey: process.env.FCM_SERVER_KEY || '',
      senderId: process.env.FCM_SENDER_ID || ''
    }
  },

  // 第三方服务配置
  thirdParty: {
    // 阿里云OSS
    oss: {
      region: process.env.OSS_REGION || '',
      accessKeyId: process.env.OSS_ACCESS_KEY_ID || '',
      accessKeySecret: process.env.OSS_ACCESS_KEY_SECRET || '',
      bucket: process.env.OSS_BUCKET || ''
    },
    // 微信小程序
    wechat: {
      appId: process.env.WECHAT_APP_ID || '',
      appSecret: process.env.WECHAT_APP_SECRET || ''
    }
  },

  // 业务配置
  business: {
    // 心愿力配置
    wishPower: {
      dailyRitual: parseInt(process.env.WISH_POWER_DAILY_RITUAL, 10) || 10,
      shareWish: parseInt(process.env.WISH_POWER_SHARE_WISH, 10) || 5,
      receiveBlessing: parseInt(process.env.WISH_POWER_RECEIVE_BLESSING, 10) || 3
    },
    // 功德值配置
    merit: {
      blessWish: parseInt(process.env.MERIT_BLESS_WISH, 10) || 10,
      blessWithImage: parseInt(process.env.MERIT_BLESS_WITH_IMAGE, 10) || 5,
      blessWithLongText: parseInt(process.env.MERIT_BLESS_WITH_LONG_TEXT, 10) || 5,
      receiveThanks: parseInt(process.env.MERIT_RECEIVE_THANKS, 10) || 2
    },
    // 排行榜配置
    leaderboard: {
      updateInterval: parseInt(process.env.LEADERBOARD_UPDATE_INTERVAL, 10) || 60 * 60 * 1000, // 1小时
      topCount: parseInt(process.env.LEADERBOARD_TOP_COUNT, 10) || 100
    }
  }
};

// 验证必需的环境变量
const requiredEnvVars = [
  'JWT_SECRET'
];

if (config.env === 'production') {
  requiredEnvVars.push(
    'MONGODB_URI',
    'REDIS_HOST',
    'SMTP_USER',
    'SMTP_PASS'
  );
}

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
  throw new Error(`缺少必需的环境变量: ${missingEnvVars.join(', ')}`);
}

module.exports = config;
