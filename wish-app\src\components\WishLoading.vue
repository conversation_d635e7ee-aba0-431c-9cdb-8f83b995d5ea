<!--
  愿境加载组件
  用于显示加载状态的组件
-->
<template>
  <view 
    class="wish-loading"
    :class="[
      `wish-loading--${type}`,
      `wish-loading--${size}`,
      {
        'wish-loading--vertical': vertical
      }
    ]"
  >
    <!-- 加载动画 -->
    <view class="wish-loading__spinner">
      <!-- 圆形加载器 -->
      <view v-if="type === 'spinner'" class="wish-loading__spinner-circle"></view>
      
      <!-- 点状加载器 -->
      <view v-else-if="type === 'dots'" class="wish-loading__dots">
        <view class="wish-loading__dot" v-for="i in 3" :key="i"></view>
      </view>
      
      <!-- 波浪加载器 -->
      <view v-else-if="type === 'wave'" class="wish-loading__wave">
        <view class="wish-loading__wave-bar" v-for="i in 5" :key="i"></view>
      </view>
      
      <!-- 心形加载器（愿境特色） -->
      <view v-else-if="type === 'heart'" class="wish-loading__heart">
        <view class="wish-loading__heart-shape"></view>
      </view>
    </view>
    
    <!-- 加载文字 -->
    <view v-if="text" class="wish-loading__text">
      {{ text }}
    </view>
  </view>
</template>

<script>
export default {
  name: 'WishLoading',
  props: {
    // 加载器类型
    type: {
      type: String,
      default: 'spinner',
      validator: (value) => ['spinner', 'dots', 'wave', 'heart'].includes(value)
    },
    // 大小
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large'].includes(value)
    },
    // 加载文字
    text: {
      type: String,
      default: ''
    },
    // 是否垂直布局
    vertical: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.wish-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  
  &--vertical {
    flex-direction: column;
  }
  
  &--small {
    .wish-loading__spinner {
      width: 32rpx;
      height: 32rpx;
    }
    
    .wish-loading__text {
      font-size: $wish-font-sm;
    }
  }
  
  &--medium {
    .wish-loading__spinner {
      width: 48rpx;
      height: 48rpx;
    }
    
    .wish-loading__text {
      font-size: $wish-font-md;
    }
  }
  
  &--large {
    .wish-loading__spinner {
      width: 64rpx;
      height: 64rpx;
    }
    
    .wish-loading__text {
      font-size: $wish-font-lg;
    }
  }
}

.wish-loading__spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.wish-loading__text {
  color: $wish-text-secondary;
  margin-left: $wish-spacing-sm;
  
  .wish-loading--vertical & {
    margin-left: 0;
    margin-top: $wish-spacing-sm;
  }
}

/* 圆形加载器 */
.wish-loading__spinner-circle {
  width: 100%;
  height: 100%;
  border: 4rpx solid rgba(232, 180, 160, 0.2);
  border-top-color: $wish-color-primary;
  border-radius: 50%;
  animation: wish-loading-spin 1s linear infinite;
}

/* 点状加载器 */
.wish-loading__dots {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.wish-loading__dot {
  width: 20%;
  height: 20%;
  background-color: $wish-color-primary;
  border-radius: 50%;
  animation: wish-loading-dots 1.4s ease-in-out infinite both;
  
  &:nth-child(1) {
    animation-delay: -0.32s;
  }
  
  &:nth-child(2) {
    animation-delay: -0.16s;
  }
  
  &:nth-child(3) {
    animation-delay: 0s;
  }
}

/* 波浪加载器 */
.wish-loading__wave {
  display: flex;
  align-items: flex-end;
  justify-content: space-between;
  width: 100%;
  height: 100%;
}

.wish-loading__wave-bar {
  width: 16%;
  background-color: $wish-color-primary;
  animation: wish-loading-wave 1.2s ease-in-out infinite;
  
  &:nth-child(1) {
    animation-delay: -1.2s;
  }
  
  &:nth-child(2) {
    animation-delay: -1.1s;
  }
  
  &:nth-child(3) {
    animation-delay: -1.0s;
  }
  
  &:nth-child(4) {
    animation-delay: -0.9s;
  }
  
  &:nth-child(5) {
    animation-delay: -0.8s;
  }
}

/* 心形加载器 */
.wish-loading__heart {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wish-loading__heart-shape {
  width: 80%;
  height: 80%;
  background-color: $wish-color-primary;
  position: relative;
  transform: rotate(-45deg);
  animation: wish-loading-heart 1.2s ease-in-out infinite;
  
  &::before,
  &::after {
    content: '';
    width: 52%;
    height: 80%;
    position: absolute;
    left: 50%;
    top: 0;
    background-color: $wish-color-primary;
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    transform: rotate(-45deg);
    transform-origin: 0 100%;
  }
  
  &::after {
    left: 0;
    transform: rotate(45deg);
    transform-origin: 100% 100%;
  }
}

/* 动画定义 */
@keyframes wish-loading-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes wish-loading-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes wish-loading-wave {
  0%, 40%, 100% {
    height: 20%;
  }
  20% {
    height: 100%;
  }
}

@keyframes wish-loading-heart {
  0%, 100% {
    transform: rotate(-45deg) scale(1);
  }
  50% {
    transform: rotate(-45deg) scale(1.2);
  }
}
</style>
