/**
 * 活动相关API
 */

import request from './request'

export const eventAPI = {
  // 获取活动列表
  getEvents(params = {}) {
    const defaultParams = {
      page: 1,
      limit: 20,
      status: 'active'
    }
    
    // 返回模拟的活动数据
    return Promise.resolve({
      data: {
        list: [
          {
            id: 1,
            title: '新年祈愿活动',
            description: '在新年到来之际，让我们一起许下美好的愿望',
            startTime: '2024-01-01 00:00:00',
            endTime: '2024-01-31 23:59:59',
            status: 'active',
            isJoined: false,
            participantCount: 1234,
            maxParticipants: 10000,
            rewards: ['心愿力+100', '专属头像框'],
            image: '/static/images/event-newyear.jpg'
          },
          {
            id: 2,
            title: '守护天使招募',
            description: '成为守护天使，为他人的愿望送上祝福',
            startTime: '2024-02-01 00:00:00',
            endTime: '2024-02-29 23:59:59',
            status: 'active',
            isJoined: true,
            participantCount: 567,
            maxParticipants: 1000,
            rewards: ['功德值+200', '守护者徽章'],
            image: '/static/images/event-guardian.jpg'
          }
        ],
        total: 2,
        page: params.page || 1,
        limit: params.limit || 20
      }
    })
  },

  // 获取活动详情
  getEventDetail(eventId) {
    // 返回模拟的活动详情
    return Promise.resolve({
      data: {
        id: eventId,
        title: '新年祈愿活动',
        description: '在新年到来之际，让我们一起许下美好的愿望。参与活动可以获得丰厚奖励，还能结识更多志同道合的朋友。',
        content: '<p>活动详细内容...</p>',
        startTime: '2024-01-01 00:00:00',
        endTime: '2024-01-31 23:59:59',
        status: 'active',
        isJoined: false,
        participantCount: 1234,
        maxParticipants: 10000,
        rewards: [
          { type: 'wishPower', amount: 100, name: '心愿力+100' },
          { type: 'item', id: 'avatar_frame_1', name: '专属头像框' }
        ],
        rules: [
          '活动期间每日签到可获得额外奖励',
          '分享活动给好友可获得推荐奖励',
          '完成指定任务可获得成就奖励'
        ],
        tasks: [
          {
            id: 1,
            title: '许下新年愿望',
            description: '创建一个新年相关的愿望',
            completed: false,
            reward: '心愿力+50'
          },
          {
            id: 2,
            title: '为他人祝福',
            description: '为其他用户的愿望送上祝福',
            completed: true,
            reward: '功德值+30'
          }
        ],
        image: '/static/images/event-newyear.jpg',
        bannerImage: '/static/images/event-banner.jpg'
      }
    })
  },

  // 参与活动
  joinEvent(eventId, data = {}) {
    return Promise.resolve({
      data: {
        success: true,
        message: '参与活动成功',
        rewards: [
          { type: 'wishPower', amount: 10, name: '参与奖励：心愿力+10' }
        ]
      }
    })
  },

  // 完成活动任务
  completeTask(eventId, taskId) {
    return Promise.resolve({
      data: {
        success: true,
        message: '任务完成',
        rewards: [
          { type: 'wishPower', amount: 50, name: '心愿力+50' }
        ]
      }
    })
  },

  // 获取活动排行榜
  getEventLeaderboard(eventId, params = {}) {
    const defaultParams = {
      page: 1,
      limit: 50
    }
    
    return Promise.resolve({
      data: {
        list: [
          {
            rank: 1,
            userId: 1,
            username: 'user1',
            nickname: '愿望使者',
            avatar: '/static/images/avatar1.jpg',
            score: 1500,
            level: 10
          },
          {
            rank: 2,
            userId: 2,
            username: 'user2',
            nickname: '守护天使',
            avatar: '/static/images/avatar2.jpg',
            score: 1200,
            level: 8
          }
        ],
        total: 100,
        userRank: {
          rank: 25,
          score: 800
        }
      }
    })
  }
}

export default eventAPI
