<template>
  <view class="home-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar safe-area-top">
      <view class="navbar-content">
        <view class="navbar-title">愿境</view>
        <view class="navbar-actions">
          <view class="navbar-action" @click="showNotifications">
            <image src="/static/icons/notification.png" class="navbar-icon" />
            <view v-if="unreadCount > 0" class="navbar-badge">{{ unreadCount }}</view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <scroll-view class="main-content" scroll-y>
      <!-- 欢迎区域 -->
      <view class="welcome-section">
        <view class="welcome-text">
          <text class="welcome-greeting">{{ greeting }}</text>
          <text class="welcome-subtitle">愿你的心愿都能被温柔以待</text>
        </view>
        <view class="user-stats">
          <view class="stat-item">
            <text class="stat-value">{{ userStats.wishPower }}</text>
            <text class="stat-label">心愿力</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-value">{{ userStats.meritPoints }}</text>
            <text class="stat-label">功德值</text>
          </view>
        </view>
      </view>

      <!-- 每日仪式卡片 -->
      <wish-card class="daily-ritual-card" shadow="medium" clickable @click="goToDailyRitual">
        <view class="daily-ritual">
          <view class="ritual-icon">
            <image src="/static/icons/ritual.png" class="ritual-image" />
          </view>
          <view class="ritual-content">
            <text class="ritual-title">每日仪式</text>
            <text class="ritual-desc">{{ dailyRitualText }}</text>
          </view>
          <view class="ritual-action">
            <wish-button
              v-if="!isCheckedIn"
              type="primary"
              size="small"
              text="开始仪式"
              @click.stop="startDailyRitual"
            />
            <view v-else class="ritual-completed">
              <image src="/static/icons/check.png" class="check-icon" />
              <text>已完成</text>
            </view>
          </view>
        </view>
      </wish-card>

      <!-- 快捷操作 -->
      <view class="quick-actions">
        <view class="action-item" @click="goToPostWish">
          <view class="action-icon">
            <image src="/static/icons/wish.png" class="action-image" />
          </view>
          <text class="action-text">发布心愿</text>
        </view>
        <view class="action-item" @click="goToWishPlaza">
          <view class="action-icon">
            <image src="/static/icons/plaza.png" class="action-image" />
          </view>
          <text class="action-text">许愿广场</text>
        </view>
        <view class="action-item" @click="switchToGuardian">
          <view class="action-icon">
            <image src="/static/icons/guardian.png" class="action-image" />
          </view>
          <text class="action-text">成为守护者</text>
        </view>
        <view class="action-item" @click="goToLeaderboard">
          <view class="action-icon">
            <image src="/static/icons/leaderboard.png" class="action-image" />
          </view>
          <text class="action-text">排行榜</text>
        </view>
      </view>

      <!-- 最新心愿 -->
      <view class="section">
        <view class="section-header">
          <text class="section-title">最新心愿</text>
          <text class="section-more" @click="goToWishPlaza">查看更多</text>
        </view>
        <view class="wish-list">
          <wish-card
            v-for="wish in latestWishes"
            :key="wish.id"
            class="wish-item"
            shadow="light"
            hover
            clickable
            @click="goToWishDetail(wish.id)"
          >
            <view class="wish-content">
              <view class="wish-header">
                <image :src="wish.user.avatar" class="wish-avatar" />
                <view class="wish-user">
                  <text class="wish-nickname">{{ wish.user.nickname }}</text>
                  <text class="wish-time">{{ formatTime(wish.createdAt) }}</text>
                </view>
              </view>
              <text class="wish-title">{{ wish.title }}</text>
              <text class="wish-desc">{{ wish.content }}</text>
              <view class="wish-footer">
                <view class="wish-tags">
                  <text
                    v-for="tag in wish.tags"
                    :key="tag"
                    class="wish-tag"
                  >
                    #{{ tag }}
                  </text>
                </view>
                <view class="wish-stats">
                  <text class="wish-support">{{ wish.supportCount || 0 }} 助力</text>
                </view>
              </view>
            </view>
          </wish-card>
        </view>
      </view>

      <!-- 底部安全区域 -->
      <view class="safe-area-bottom"></view>
    </scroll-view>

    <!-- 加载状态 -->
    <wish-loading
      v-if="loading"
      type="heart"
      text="加载中..."
      class="page-loading"
    />
  </view>
</template>

<script>
import { useUserStore, useWishStore, useSystemStore } from '@/store'
import { timeUtils, navigation } from '@/utils'

export default {
  data() {
    return {
      loading: false,
      latestWishes: []
    }
  },

  computed: {
    userStore() {
      return useUserStore()
    },

    wishStore() {
      return useWishStore()
    },

    systemStore() {
      return useSystemStore()
    },

    userStats() {
      return this.userStore.userStats
    },

    isCheckedIn() {
      return this.systemStore.checkInStatus.isCheckedIn
    },

    unreadCount() {
      return 0 // TODO: 实现未读消息数量
    },

    greeting() {
      const hour = new Date().getHours()
      if (hour < 6) return '夜深了'
      if (hour < 12) return '早上好'
      if (hour < 18) return '下午好'
      return '晚上好'
    },

    dailyRitualText() {
      return this.isCheckedIn ? '今日仪式已完成' : '开始今日的心灵仪式'
    }
  },

  onLoad() {
    this.initPage()
  },

  onShow() {
    this.refreshData()
  },

  onPullDownRefresh() {
    this.refreshData().finally(() => {
      uni.stopPullDownRefresh()
    })
  },

  methods: {
    async initPage() {
      this.loading = true
      try {
        await this.loadLatestWishes()
      } catch (error) {
        console.error('页面初始化失败:', error)
      } finally {
        this.loading = false
      }
    },

    async refreshData() {
      try {
        await Promise.all([
          this.userStore.fetchUserStats(),
          this.systemStore.fetchCheckInStatus(),
          this.loadLatestWishes()
        ])
      } catch (error) {
        console.error('刷新数据失败:', error)
      }
    },

    async loadLatestWishes() {
      try {
        const result = await this.wishStore.fetchWishList({
          page: 1,
          pageSize: 5,
          sortBy: 'latest'
        }, true)
        this.latestWishes = result.list || []
      } catch (error) {
        console.error('加载最新心愿失败:', error)
        this.latestWishes = []
      }
    },

    formatTime(timestamp) {
      return timeUtils.relativeTime(timestamp)
    },

    // 页面跳转方法
    goToDailyRitual() {
      navigation.navigateTo('/pages/wisher/daily-ritual')
    },

    goToPostWish() {
      navigation.switchTab('/pages/wisher/post-wish')
    },

    goToWishPlaza() {
      navigation.switchTab('/pages/wisher/wish-plaza')
    },

    goToLeaderboard() {
      navigation.navigateTo('/pages/social/leaderboard')
    },

    goToWishDetail(wishId) {
      navigation.navigateTo('/pages/wisher/wish-detail', { id: wishId })
    },

    // 功能方法
    async startDailyRitual() {
      try {
        await this.systemStore.dailyCheckIn()
        this.goToDailyRitual()
      } catch (error) {
        console.error('开始每日仪式失败:', error)
      }
    },

    async switchToGuardian() {
      try {
        await this.userStore.switchRole('guardian')
        navigation.navigateTo('/pages/guardian/listen-wishes')
      } catch (error) {
        console.error('切换守护者身份失败:', error)
      }
    },

    showNotifications() {
      navigation.navigateTo('/pages/social/notifications')
    }
  }
}
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
  background-color: $wish-bg-primary;
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $wish-spacing-md;
}

.navbar-title {
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-color-primary;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.navbar-action {
  position: relative;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: $wish-spacing-sm;
}

.navbar-icon {
  width: 32rpx;
  height: 32rpx;
}

.navbar-badge {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  min-width: 32rpx;
  height: 32rpx;
  background-color: $wish-color-error;
  color: $wish-text-inverse;
  font-size: $wish-font-xs;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

/* 主要内容区域 */
.main-content {
  height: calc(100vh - 88rpx);
  padding: $wish-spacing-md;
}

/* 欢迎区域 */
.welcome-section {
  margin-bottom: $wish-spacing-lg;
}

.welcome-text {
  text-align: center;
  margin-bottom: $wish-spacing-md;
}

.welcome-greeting {
  display: block;
  font-size: $wish-font-xxl;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.welcome-subtitle {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
}

.user-stats {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-md;
  box-shadow: $wish-shadow-sm;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-color-primary;
  margin-bottom: $wish-spacing-xs;
}

.stat-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background-color: $wish-border-light;
  margin: 0 $wish-spacing-md;
}

/* 每日仪式卡片 */
.daily-ritual-card {
  margin-bottom: $wish-spacing-lg;
}

.daily-ritual {
  display: flex;
  align-items: center;
}

.ritual-icon {
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(232, 180, 160, 0.1);
  border-radius: $wish-radius-md;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $wish-spacing-md;
}

.ritual-image {
  width: 48rpx;
  height: 48rpx;
}

.ritual-content {
  flex: 1;
}

.ritual-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.ritual-desc {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
}

.ritual-action {
  margin-left: $wish-spacing-md;
}

.ritual-completed {
  display: flex;
  align-items: center;
  color: $wish-color-success;
  font-size: $wish-font-sm;
}

.check-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: $wish-spacing-xs;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: $wish-spacing-lg;
}

.action-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  box-shadow: $wish-shadow-sm;
  margin: 0 $wish-spacing-xs;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
  }
}

.action-icon {
  width: 64rpx;
  height: 64rpx;
  background-color: rgba(232, 180, 160, 0.1);
  border-radius: $wish-radius-md;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: $wish-spacing-sm;
}

.action-image {
  width: 40rpx;
  height: 40rpx;
}

.action-text {
  font-size: $wish-font-sm;
  color: $wish-text-primary;
  text-align: center;
}

/* 区域样式 */
.section {
  margin-bottom: $wish-spacing-lg;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $wish-spacing-md;
}

.section-title {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
}

.section-more {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
}

/* 心愿列表 */
.wish-list {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.wish-item {
  margin: 0;
}

.wish-content {
  padding: 0;
}

.wish-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-sm;
}

.wish-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
}

.wish-user {
  flex: 1;
}

.wish-nickname {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: 4rpx;
}

.wish-time {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.wish-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
  @extend .ellipsis;
}

.wish-desc {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-sm;
  @extend .ellipsis-2;
}

.wish-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.wish-tags {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-xs;
}

.wish-tag {
  font-size: $wish-font-xs;
  color: $wish-color-primary;
  background-color: rgba(232, 180, 160, 0.1);
  padding: 4rpx 8rpx;
  border-radius: $wish-radius-sm;
}

.wish-stats {
  display: flex;
  align-items: center;
}

.wish-support {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

/* 页面加载 */
.page-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}
</style>
