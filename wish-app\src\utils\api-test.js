/**
 * API连接测试工具
 * 用于测试前端与后端的API连接
 */

import { authAPI, wishesAPI, usersAPI } from '@/api'
import { APP_CONFIG } from '@/config'

export class APITester {
  constructor() {
    this.results = []
  }

  // 测试单个API端点
  async testEndpoint(name, apiCall) {
    try {
      console.log(`🧪 测试 ${name}...`)
      const startTime = Date.now()
      
      const response = await apiCall()
      const endTime = Date.now()
      
      const result = {
        name,
        success: true,
        status: response.statusCode || 200,
        data: response.data || response,
        responseTime: endTime - startTime,
        error: null
      }
      
      this.results.push(result)
      console.log(`✅ ${name} - 成功 (${result.responseTime}ms)`)
      return result
      
    } catch (error) {
      const result = {
        name,
        success: false,
        status: error.statusCode || 0,
        data: null,
        responseTime: 0,
        error: error.message || error.toString()
      }
      
      this.results.push(result)
      console.log(`❌ ${name} - 失败: ${result.error}`)
      return result
    }
  }

  // 测试后端连接
  async testBackendConnection() {
    console.log('🚀 开始测试后端API连接...\n')

    // 检查是否使用代理
    const useProxy = APP_CONFIG.useProxy
    console.log(`使用代理: ${useProxy ? '是' : '否'}`)
    console.log(`API基础URL: ${APP_CONFIG.baseURL}`)

    // 测试基础端点
    await this.testEndpoint('健康检查', async () => {
      const response = await uni.request({
        url: useProxy ? '/health' : 'http://localhost:3000/health',
        method: 'GET'
      })
      return response
    })

    await this.testEndpoint('API根路径', async () => {
      const response = await uni.request({
        url: useProxy ? '/api' : 'http://localhost:3000/api',
        method: 'GET'
      })
      return response
    })

    // 测试各模块根路径
    const modules = ['auth', 'users', 'wishes', 'guardian', 'social', 'chat', 'upload']
    
    for (const module of modules) {
      await this.testEndpoint(`${module}模块`, async () => {
        const response = await uni.request({
          url: `http://localhost:3000/api/${module}`,
          method: 'GET'
        })
        return response
      })
    }

    // 测试心愿列表API
    await this.testEndpoint('心愿列表', async () => {
      return await wishesAPI.getWishList({ page: 1, limit: 5 })
    })

    return this.generateReport()
  }

  // 生成测试报告
  generateReport() {
    const total = this.results.length
    const successful = this.results.filter(r => r.success).length
    const failed = this.results.filter(r => r.success === false).length
    
    const report = {
      summary: {
        total,
        successful,
        failed,
        successRate: ((successful / total) * 100).toFixed(1) + '%'
      },
      results: this.results,
      timestamp: new Date().toISOString()
    }

    console.log('\n📊 测试报告:')
    console.log(`总计: ${total} | 成功: ${successful} | 失败: ${failed} | 成功率: ${report.summary.successRate}`)
    
    if (failed > 0) {
      console.log('\n❌ 失败的测试:')
      this.results.filter(r => !r.success).forEach(result => {
        console.log(`  - ${result.name}: ${result.error}`)
      })
    }

    return report
  }

  // 清除测试结果
  clear() {
    this.results = []
  }
}

// 创建全局测试实例
export const apiTester = new APITester()

// 快速测试函数
export async function quickTest() {
  return await apiTester.testBackendConnection()
}

export default APITester
