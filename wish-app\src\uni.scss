/**
 * 愿境应用样式变量
 * 基于温暖治愈的设计理念，采用低饱和度色彩和柔和的视觉效果
 */

/* ==================== 愿境主题色彩系统 ==================== */

/* 主色调 - 温暖治愈的色彩 */
$wish-color-primary: #E8B4A0;      // 温暖的粉橙色
$wish-color-secondary: #D4A574;     // 柔和的金色
$wish-color-accent: #B8C5D1;       // 静谧的蓝灰色

/* 背景色系 */
$wish-bg-primary: #F9F7F4;         // 温暖的米白色
$wish-bg-secondary: #FFFFFF;       // 纯白色
$wish-bg-card: #FEFEFE;           // 卡片背景
$wish-bg-overlay: rgba(249, 247, 244, 0.95); // 遮罩背景

/* 文字色系 */
$wish-text-primary: #4A4A4A;       // 深灰色文字
$wish-text-secondary: #8A8A8A;     // 中灰色文字
$wish-text-disabled: #CCCCCC;      // 浅灰色文字
$wish-text-inverse: #FFFFFF;       // 反色文字

/* 状态色系 - 柔和版本 */
$wish-color-success: #A8D8A8;      // 柔和的绿色
$wish-color-warning: #F4D03F;      // 柔和的黄色
$wish-color-error: #E8A4A4;        // 柔和的红色
$wish-color-info: #A4C4E8;         // 柔和的蓝色

/* 边框色系 */
$wish-border-light: #F0F0F0;       // 浅边框
$wish-border-medium: #E0E0E0;      // 中等边框
$wish-border-dark: #D0D0D0;        // 深边框

/* ==================== 兼容uni-app原有变量 ==================== */

/* 行为相关颜色 - 使用愿境主题色 */
$uni-color-primary: $wish-color-primary;
$uni-color-success: $wish-color-success;
$uni-color-warning: $wish-color-warning;
$uni-color-error: $wish-color-error;

/* 文字基本颜色 */
$uni-text-color: $wish-text-primary;
$uni-text-color-inverse: $wish-text-inverse;
$uni-text-color-grey: $wish-text-secondary;
$uni-text-color-placeholder: #B0B0B0;
$uni-text-color-disable: $wish-text-disabled;

/* 背景颜色 */
$uni-bg-color: $wish-bg-secondary;
$uni-bg-color-grey: $wish-bg-primary;
$uni-bg-color-hover: rgba(232, 180, 160, 0.1); // 主色调的浅色版本
$uni-bg-color-mask: rgba(0, 0, 0, 0.4);

/* 边框颜色 */
$uni-border-color: $wish-border-medium;

/* ==================== 愿境尺寸系统 ==================== */

/* 圆角系统 - 更加柔和的圆角 */
$wish-radius-sm: 8rpx;             // 小圆角
$wish-radius-md: 16rpx;            // 中等圆角
$wish-radius-lg: 24rpx;            // 大圆角
$wish-radius-xl: 32rpx;            // 超大圆角
$wish-radius-round: 50%;           // 圆形

/* 间距系统 */
$wish-spacing-xs: 8rpx;            // 超小间距
$wish-spacing-sm: 16rpx;           // 小间距
$wish-spacing-md: 24rpx;           // 中等间距
$wish-spacing-lg: 32rpx;           // 大间距
$wish-spacing-xl: 48rpx;           // 超大间距
$wish-spacing-xxl: 64rpx;          // 超超大间距

/* 字体大小系统 */
$wish-font-xs: 20rpx;              // 超小字体
$wish-font-sm: 24rpx;              // 小字体
$wish-font-md: 28rpx;              // 中等字体
$wish-font-lg: 32rpx;              // 大字体
$wish-font-xl: 36rpx;              // 超大字体
$wish-font-xxl: 48rpx;             // 标题字体

/* 阴影系统 */
$wish-shadow-sm: 0 2rpx 8rpx rgba(0,0,0,0.08);    // 轻微阴影
$wish-shadow-md: 0 4rpx 16rpx rgba(0,0,0,0.12);   // 中等阴影
$wish-shadow-lg: 0 8rpx 32rpx rgba(0,0,0,0.16);   // 重阴影
$wish-shadow-card: 0 2rpx 12rpx rgba(232,180,160,0.15); // 卡片阴影

/* ==================== 兼容uni-app原有尺寸变量 ==================== */

/* 文字尺寸 */
$uni-font-size-sm: 24rpx;
$uni-font-size-base: 28rpx;
$uni-font-size-lg: 32rpx;

/* 图片尺寸 */
$uni-img-size-sm: 40rpx;
$uni-img-size-base: 52rpx;
$uni-img-size-lg: 80rpx;

/* Border Radius */
$uni-border-radius-sm: $wish-radius-sm;
$uni-border-radius-base: $wish-radius-md;
$uni-border-radius-lg: $wish-radius-lg;
$uni-border-radius-circle: $wish-radius-round;

/* 水平间距 */
$uni-spacing-row-sm: $wish-spacing-xs;
$uni-spacing-row-base: $wish-spacing-sm;
$uni-spacing-row-lg: $wish-spacing-md;

/* 垂直间距 */
$uni-spacing-col-sm: $wish-spacing-xs;
$uni-spacing-col-base: $wish-spacing-sm;
$uni-spacing-col-lg: $wish-spacing-md;

/* 透明度 */
$uni-opacity-disabled: 0.4;

/* 文章场景相关 - 使用愿境主题色 */
$uni-color-title: $wish-text-primary;
$uni-font-size-title: $wish-font-xl;
$uni-color-subtitle: $wish-text-secondary;
$uni-font-size-subtitle: $wish-font-lg;
$uni-color-paragraph: $wish-text-primary;
$uni-font-size-paragraph: $wish-font-md;

/* ==================== 愿境全局样式 ==================== */

/* 页面基础样式 */
page {
  background-color: $wish-bg-primary;
  color: $wish-text-primary;
  font-size: $wish-font-md;
  line-height: 1.6;
}

/* 通用卡片样式 */
.wish-card {
  background-color: $wish-bg-card;
  border-radius: $wish-radius-lg;
  box-shadow: $wish-shadow-card;
  padding: $wish-spacing-md;
  margin: $wish-spacing-sm;
}

/* 通用按钮样式 */
.wish-btn {
  border-radius: $wish-radius-md;
  padding: $wish-spacing-sm $wish-spacing-md;
  font-size: $wish-font-md;
  border: none;
  transition: all 0.3s ease;

  &.primary {
    background-color: $wish-color-primary;
    color: $wish-text-inverse;
  }

  &.secondary {
    background-color: $wish-color-secondary;
    color: $wish-text-inverse;
  }

  &.ghost {
    background-color: transparent;
    border: 2rpx solid $wish-color-primary;
    color: $wish-color-primary;
  }
}

/* 通用输入框样式 */
.wish-input {
  background-color: $wish-bg-secondary;
  border: 2rpx solid $wish-border-light;
  border-radius: $wish-radius-md;
  padding: $wish-spacing-sm $wish-spacing-md;
  font-size: $wish-font-md;
  color: $wish-text-primary;

  &:focus {
    border-color: $wish-color-primary;
  }

  &::placeholder {
    color: $wish-text-disabled;
  }
}

/* 通用标题样式 */
.wish-title {
  font-size: $wish-font-xl;
  color: $wish-text-primary;
  font-weight: 600;
  margin-bottom: $wish-spacing-sm;
}

.wish-subtitle {
  font-size: $wish-font-lg;
  color: $wish-text-secondary;
  font-weight: 500;
  margin-bottom: $wish-spacing-xs;
}

/* 通用文本样式 */
.wish-text {
  font-size: $wish-font-md;
  color: $wish-text-primary;
  line-height: 1.6;
}

.wish-text-secondary {
  color: $wish-text-secondary;
}

.wish-text-disabled {
  color: $wish-text-disabled;
}

/* 通用布局样式 */
.wish-container {
  padding: $wish-spacing-md;
}

.wish-section {
  margin-bottom: $wish-spacing-lg;
}

.wish-divider {
  height: 2rpx;
  background-color: $wish-border-light;
  margin: $wish-spacing-md 0;
}

/* 通用动画 */
.wish-fade-in {
  animation: wishFadeIn 0.3s ease-in-out;
}

.wish-slide-up {
  animation: wishSlideUp 0.3s ease-out;
}

@keyframes wishFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes wishSlideUp {
  from {
    transform: translateY(20rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* ==================== 导入工具类 ==================== */
@import './styles/utils.scss';