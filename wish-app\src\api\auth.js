/**
 * 认证相关API
 */

import request from './request'
import { API_CONFIG } from '@/config'

const { endpoints } = API_CONFIG

export const authAPI = {
  // 用户登录
  login(identifier, password) {
    return request.post(endpoints.auth.login, {
      identifier,
      password
    })
  },

  // 用户注册
  register(username, email, password, nickname, phone) {
    return request.post(endpoints.auth.register, {
      username,
      email,
      password,
      nickname,
      phone
    })
  },

  // 刷新令牌
  refreshToken(refreshToken) {
    return request.post(endpoints.auth.refresh, {
      refreshToken
    })
  },

  // 发送邮箱验证码
  sendEmailVerification() {
    return request.post(endpoints.auth.sendEmailVerification)
  },

  // 验证邮箱
  verifyEmail(token) {
    return request.post(endpoints.auth.verifyEmail, {
      token
    })
  },

  // 获取当前用户信息
  getCurrentUser() {
    return request.get(endpoints.auth.me)
  },

  // 用户登出
  logout() {
    return request.post(endpoints.auth.logout)
  }
}

export default authAPI
