/**
 * 用户状态管理
 * 管理用户信息、登录状态、角色切换等
 */

import { defineStore } from 'pinia'
import { authAPI, userAPI, systemAPI } from '@/api'
import { storage } from '@/utils'
import { STORAGE_KEYS, USER_ROLES } from '@/config'

export const useUserStore = defineStore('user', {
  state: () => ({
    // 用户基本信息
    userInfo: null,
    // 登录状态
    isLoggedIn: false,
    // 当前用户角色
    currentRole: USER_ROLES.WISHER,
    // 用户统计数据
    userStats: {
      wishPower: 0,        // 心愿力
      meritPoints: 0,      // 功德值
      totalWishes: 0,      // 总心愿数
      totalBlessings: 0,   // 总赐福数
      level: 1,            // 等级
      title: ''            // 称号
    },
    // 每日签到状态
    dailyCheckIn: {
      isCheckedIn: false,
      streak: 0,
      lastCheckInDate: null
    },
    // 加载状态
    loading: {
      login: false,
      profile: false,
      stats: false
    }
  }),

  getters: {
    // 是否是祈愿者
    isWisher: (state) => state.currentRole === USER_ROLES.WISHER,
    
    // 是否是守护者
    isGuardian: (state) => state.currentRole === USER_ROLES.GUARDIAN,
    
    // 用户头像
    avatar: (state) => state.userInfo?.avatar || '/static/default-avatar.png',
    
    // 用户昵称
    nickname: (state) => state.userInfo?.nickname || '未知用户',
    
    // 用户ID
    userId: (state) => state.userInfo?.id,
    
    // 是否可以发布心愿（心愿力足够）
    canPostWish: (state) => state.userStats.wishPower >= 5,
    
    // 是否可以创建神殿（功德值足够）
    canCreateTemple: (state) => state.userStats.meritPoints >= 100,
    
    // 用户等级信息
    levelInfo: (state) => {
      const { meritPoints } = state.userStats
      let level = 1
      let title = '新手守护者'
      
      if (meritPoints >= 1000) {
        level = 5
        title = '传说守护者'
      } else if (meritPoints >= 500) {
        level = 4
        title = '大师守护者'
      } else if (meritPoints >= 200) {
        level = 3
        title = '资深守护者'
      } else if (meritPoints >= 50) {
        level = 2
        title = '初级守护者'
      }
      
      return { level, title }
    }
  },

  actions: {
    /**
     * 用户登录
     */
    async login(phone, password) {
      this.loading.login = true
      try {
        const result = await authAPI.login(phone, password)
        
        // 保存token和用户信息
        storage.set(STORAGE_KEYS.TOKEN, result.token)
        storage.set(STORAGE_KEYS.USER_INFO, result.userInfo)
        storage.set(STORAGE_KEYS.USER_ROLE, result.userInfo.defaultRole || USER_ROLES.WISHER)
        
        // 更新状态
        this.userInfo = result.userInfo
        this.isLoggedIn = true
        this.currentRole = result.userInfo.defaultRole || USER_ROLES.WISHER
        
        // 获取用户统计数据
        await this.fetchUserStats()
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.login = false
      }
    },

    /**
     * 用户注册
     */
    async register(phone, password, nickname, verifyCode) {
      try {
        const result = await authAPI.register(phone, password, nickname, verifyCode)
        
        // 注册成功后自动登录
        await this.login(phone, password)
        
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 退出登录
     */
    async logout() {
      try {
        await authAPI.logout()
      } catch (error) {
        console.error('退出登录失败:', error)
      } finally {
        // 清除本地存储
        storage.remove(STORAGE_KEYS.TOKEN)
        storage.remove(STORAGE_KEYS.USER_INFO)
        storage.remove(STORAGE_KEYS.USER_ROLE)
        
        // 重置状态
        this.userInfo = null
        this.isLoggedIn = false
        this.currentRole = USER_ROLES.WISHER
        this.userStats = {
          wishPower: 0,
          meritPoints: 0,
          totalWishes: 0,
          totalBlessings: 0,
          level: 1,
          title: ''
        }
        this.dailyCheckIn = {
          isCheckedIn: false,
          streak: 0,
          lastCheckInDate: null
        }
      }
    },

    /**
     * 切换用户角色
     */
    async switchRole(role) {
      try {
        await userAPI.switchRole(role)
        
        this.currentRole = role
        storage.set(STORAGE_KEYS.USER_ROLE, role)
        
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取用户信息
     */
    async fetchUserProfile() {
      this.loading.profile = true
      try {
        const userInfo = await authAPI.getProfile()
        this.userInfo = userInfo
        storage.set(STORAGE_KEYS.USER_INFO, userInfo)
        return userInfo
      } catch (error) {
        throw error
      } finally {
        this.loading.profile = false
      }
    },

    /**
     * 更新用户信息
     */
    async updateProfile(data) {
      try {
        const result = await userAPI.updateProfile(data)
        
        // 更新本地用户信息
        this.userInfo = { ...this.userInfo, ...result }
        storage.set(STORAGE_KEYS.USER_INFO, this.userInfo)
        
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取用户统计数据
     */
    async fetchUserStats() {
      this.loading.stats = true
      try {
        const stats = await userAPI.getUserStats(this.userId)
        this.userStats = { ...this.userStats, ...stats }
        return stats
      } catch (error) {
        console.error('获取用户统计失败:', error)
      } finally {
        this.loading.stats = false
      }
    },

    /**
     * 每日签到
     */
    async dailyCheckInAction() {
      try {
        const result = await systemAPI.dailyCheckIn()
        
        // 更新签到状态
        this.dailyCheckIn = {
          isCheckedIn: true,
          streak: result.streak,
          lastCheckInDate: new Date().toDateString()
        }
        
        // 更新心愿力
        this.userStats.wishPower += result.reward
        
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 消耗心愿力
     */
    consumeWishPower(amount) {
      if (this.userStats.wishPower >= amount) {
        this.userStats.wishPower -= amount
        return true
      }
      return false
    },

    /**
     * 增加功德值
     */
    addMeritPoints(amount) {
      this.userStats.meritPoints += amount
      
      // 更新等级和称号
      const levelInfo = this.levelInfo
      this.userStats.level = levelInfo.level
      this.userStats.title = levelInfo.title
    },

    /**
     * 初始化用户状态（从本地存储恢复）
     */
    initUserState() {
      const token = storage.get(STORAGE_KEYS.TOKEN)
      const userInfo = storage.get(STORAGE_KEYS.USER_INFO)
      const userRole = storage.get(STORAGE_KEYS.USER_ROLE)
      
      if (token && userInfo) {
        this.isLoggedIn = true
        this.userInfo = userInfo
        this.currentRole = userRole || USER_ROLES.WISHER
        
        // 获取最新的用户统计数据
        this.fetchUserStats()
      }
    }
  }
})
