/**
 * 社交路由
 * 处理社交互动相关的API请求
 */

const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const { getModel } = require('../models');
const authMiddleware = require('../middleware/auth');
const { AppError, catchAsync, handleValidationErrors } = require('../middleware/errorHandler');
const { Cache } = require('../utils/redis');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * 社交模块根路由
 */
router.get('/', (req, res) => {
  res.json({
    module: '社交模块',
    endpoints: {
      follow: 'POST /api/social/follow/:userId',
      unfollow: 'DELETE /api/social/follow/:userId',
      following: 'GET /api/social/following/:userId',
      followers: 'GET /api/social/followers/:userId',
      addComment: 'POST /api/social/comments/:wishId',
      getComments: 'GET /api/social/comments/:wishId',
      deleteComment: 'DELETE /api/social/comments/:commentId',
      share: 'POST /api/social/share/:wishId',
      followStatus: 'GET /api/social/follow-status/:userId'
    }
  });
});

/**
 * 关注用户
 */
router.post('/follow/:userId', [
  param('userId').isInt().withMessage('用户ID必须是整数')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { userId } = req.params;
  const followerId = req.user.userId;

  if (parseInt(userId) === followerId) {
    throw new AppError('不能关注自己', 400, 'CANNOT_FOLLOW_SELF');
  }

  const User = getModel('User');
  const Follow = getModel('Follow');

  // 检查被关注用户是否存在
  const targetUser = await User.findByPk(userId);
  if (!targetUser) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  // 检查是否已经关注
  const existingFollow = await Follow.findOne({
    where: {
      follower_id: followerId,
      following_id: userId
    }
  });

  if (existingFollow) {
    throw new AppError('已经关注过此用户', 400, 'ALREADY_FOLLOWING');
  }

  // 创建关注关系
  await Follow.create({
    follower_id: followerId,
    following_id: userId
  });

  // 清除相关缓存
  await Cache.delPattern(`user:follows:*`);

  logger.info(`用户 ${req.user.username} 关注了用户 ${targetUser.username}`);

  res.status(201).json({
    message: '关注成功'
  });
}));

/**
 * 取消关注用户
 */
router.delete('/follow/:userId', [
  param('userId').isInt().withMessage('用户ID必须是整数')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { userId } = req.params;
  const followerId = req.user.userId;

  const Follow = getModel('Follow');

  const follow = await Follow.findOne({
    where: {
      follower_id: followerId,
      following_id: userId
    }
  });

  if (!follow) {
    throw new AppError('未关注此用户', 400, 'NOT_FOLLOWING');
  }

  await follow.destroy();

  // 清除相关缓存
  await Cache.delPattern(`user:follows:*`);

  logger.info(`用户 ${req.user.username} 取消关注用户 ${userId}`);

  res.json({
    message: '取消关注成功'
  });
}));

/**
 * 获取关注列表
 */
router.get('/following/:userId', [
  param('userId').isInt().withMessage('用户ID必须是整数'),
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间')
], handleValidationErrors, authMiddleware.optional, catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { page = 1, limit = 20 } = req.query;

  const Follow = getModel('Follow');
  const User = getModel('User');

  const result = await Follow.findAndCountAll({
    where: { follower_id: userId },
    order: [['created_at', 'DESC']],
    offset: (page - 1) * limit,
    limit: parseInt(limit),
    include: [{
      model: User,
      as: 'following',
      attributes: ['id', 'username', 'nickname', 'avatar', 'level']
    }]
  });

  res.json({
    following: result.rows.map(follow => follow.following),
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: result.count,
      pages: Math.ceil(result.count / limit)
    }
  });
}));

/**
 * 获取粉丝列表
 */
router.get('/followers/:userId', [
  param('userId').isInt().withMessage('用户ID必须是整数'),
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间')
], handleValidationErrors, authMiddleware.optional, catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { page = 1, limit = 20 } = req.query;

  const Follow = getModel('Follow');
  const User = getModel('User');

  const result = await Follow.findAndCountAll({
    where: { following_id: userId },
    order: [['created_at', 'DESC']],
    offset: (page - 1) * limit,
    limit: parseInt(limit),
    include: [{
      model: User,
      as: 'follower',
      attributes: ['id', 'username', 'nickname', 'avatar', 'level']
    }]
  });

  res.json({
    followers: result.rows.map(follow => follow.follower),
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: result.count,
      pages: Math.ceil(result.count / limit)
    }
  });
}));

/**
 * 添加评论
 */
router.post('/comments/:wishId', [
  param('wishId').isInt().withMessage('心愿ID必须是整数'),
  body('content')
    .isLength({ min: 1, max: 500 })
    .withMessage('评论内容长度必须在1-500字符之间'),
  body('parentId')
    .optional()
    .isInt()
    .withMessage('父评论ID必须是整数')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { wishId } = req.params;
  const { content, parentId } = req.body;

  const Wish = getModel('Wish');
  const Comment = getModel('Comment');
  const User = getModel('User');

  // 检查心愿是否存在
  const wish = await Wish.findByPk(wishId);
  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  // 如果是回复评论，检查父评论是否存在
  if (parentId) {
    const parentComment = await Comment.findByPk(parentId);
    if (!parentComment || parentComment.wish_id !== parseInt(wishId)) {
      throw new AppError('父评论不存在', 404, 'PARENT_COMMENT_NOT_FOUND');
    }
  }

  // 创建评论
  const comment = await Comment.create({
    wish_id: wishId,
    user_id: req.user.userId,
    parent_id: parentId || null,
    content
  });

  // 更新心愿评论数
  await wish.increment('comments');

  // 更新用户统计
  await User.increment('comments_posted', { where: { id: req.user.userId } });

  // 获取完整的评论信息
  const fullComment = await Comment.findByPk(comment.id, {
    include: [{
      model: User,
      as: 'author',
      attributes: ['id', 'username', 'nickname', 'avatar', 'level']
    }]
  });

  // 清除相关缓存
  await Cache.delPattern('wishes:*');
  await Cache.delPattern('comments:*');

  logger.info(`用户 ${req.user.username} 评论心愿 ${wish.title}`);

  res.status(201).json({
    message: '评论成功',
    comment: fullComment
  });
}));

/**
 * 获取评论列表
 */
router.get('/comments/:wishId', [
  param('wishId').isInt().withMessage('心愿ID必须是整数'),
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间')
], handleValidationErrors, authMiddleware.optional, catchAsync(async (req, res) => {
  const { wishId } = req.params;
  const { page = 1, limit = 20 } = req.query;

  const Comment = getModel('Comment');
  const User = getModel('User');

  // 获取顶级评论
  const result = await Comment.findAndCountAll({
    where: {
      wish_id: wishId,
      parent_id: null,
      status: 'active'
    },
    order: [['created_at', 'DESC']],
    offset: (page - 1) * limit,
    limit: parseInt(limit),
    include: [
      {
        model: User,
        as: 'author',
        attributes: ['id', 'username', 'nickname', 'avatar', 'level']
      },
      {
        model: Comment,
        as: 'replies',
        where: { status: 'active' },
        required: false,
        order: [['created_at', 'ASC']],
        limit: 3, // 只显示前3个回复
        include: [{
          model: User,
          as: 'author',
          attributes: ['id', 'username', 'nickname', 'avatar', 'level']
        }]
      }
    ]
  });

  res.json({
    comments: result.rows,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: result.count,
      pages: Math.ceil(result.count / limit)
    }
  });
}));

/**
 * 删除评论
 */
router.delete('/comments/:commentId', [
  param('commentId').isInt().withMessage('评论ID必须是整数')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { commentId } = req.params;

  const Comment = getModel('Comment');
  const comment = await Comment.findByPk(commentId);

  if (!comment) {
    throw new AppError('评论不存在', 404, 'COMMENT_NOT_FOUND');
  }

  // 检查权限
  if (comment.user_id !== req.user.userId && !req.user.roles.includes('admin')) {
    throw new AppError('无权删除此评论', 403, 'ACCESS_DENIED');
  }

  // 软删除
  await comment.update({ status: 'deleted' });

  // 清除相关缓存
  await Cache.delPattern('comments:*');

  logger.info(`用户 ${req.user.username} 删除评论 ${commentId}`);

  res.json({
    message: '评论删除成功'
  });
}));

/**
 * 分享心愿
 */
router.post('/share/:wishId', [
  param('wishId').isInt().withMessage('心愿ID必须是整数'),
  body('platform')
    .isIn(['wechat', 'weibo', 'qq', 'link'])
    .withMessage('分享平台无效')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { wishId } = req.params;
  const { platform } = req.body;

  const Wish = getModel('Wish');
  const wish = await Wish.findByPk(wishId);

  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  // 记录分享
  await wish.addShare(req.user.userId, platform);

  // 奖励心愿力
  const User = getModel('User');
  const user = await User.findByPk(req.user.userId);
  await user.addPoints('wishPower', 2); // 分享奖励2点心愿力

  logger.info(`用户 ${req.user.username} 分享心愿 ${wish.title} 到 ${platform}`);

  res.json({
    message: '分享成功',
    reward: {
      wishPower: 2
    }
  });
}));

/**
 * 检查关注状态
 */
router.get('/follow-status/:userId', [
  param('userId').isInt().withMessage('用户ID必须是整数')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { userId } = req.params;
  const followerId = req.user.userId;

  const Follow = getModel('Follow');

  const isFollowing = await Follow.findOne({
    where: {
      follower_id: followerId,
      following_id: userId
    }
  });

  res.json({
    isFollowing: !!isFollowing
  });
}));

module.exports = router;
