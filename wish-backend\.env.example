# 环境配置
NODE_ENV=development
PORT=3000

# 数据库配置
MONGODB_URI=mongodb://localhost:27017/wish_app

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# 密码加密配置
BCRYPT_SALT_ROUNDS=12

# CORS配置
CORS_ORIGIN=http://localhost:8080,http://localhost:3000

# 速率限制配置
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW_MS=900000

# 文件上传配置
UPLOAD_MAX_FILE_SIZE=5242880
UPLOAD_DESTINATION=uploads/
UPLOAD_PUBLIC_PATH=/uploads/

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password
EMAIL_FROM=<EMAIL>

# 短信配置
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY_ID=your-access-key-id
SMS_ACCESS_KEY_SECRET=your-access-key-secret
SMS_SIGN_NAME=愿境
SMS_TEMPLATE_VERIFICATION=SMS_123456789

# 日志配置
LOG_LEVEL=info
LOG_FILE_ENABLED=true
LOG_FILE_NAME=logs/app.log
LOG_FILE_MAX_SIZE=5242880
LOG_FILE_MAX_FILES=5
LOG_CONSOLE_ENABLED=true

# 缓存配置
CACHE_TTL_DEFAULT=300
CACHE_TTL_USER=600
CACHE_TTL_WISH=180
CACHE_TTL_LEADERBOARD=900

# 分页配置
PAGINATION_DEFAULT_LIMIT=20
PAGINATION_MAX_LIMIT=100

# 安全配置
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SYMBOLS=false

ACCOUNT_LOCK_MAX_ATTEMPTS=5
ACCOUNT_LOCK_TIME=1800000

CAPTCHA_LENGTH=6
CAPTCHA_EXPIRES_IN=300000
CAPTCHA_MAX_ATTEMPTS=3

# 内容审核配置
MODERATION_AUTO_REVIEW=true
MODERATION_STRICT_MODE=false
MODERATION_QUEUE_SIZE=1000
MODERATION_BATCH_SIZE=50

# 推送通知配置
PUSH_ENABLED=false
PUSH_PROVIDER=fcm
FCM_SERVER_KEY=your-fcm-server-key
FCM_SENDER_ID=your-fcm-sender-id

# 第三方服务配置
# 阿里云OSS
OSS_REGION=oss-cn-hangzhou
OSS_ACCESS_KEY_ID=your-oss-access-key-id
OSS_ACCESS_KEY_SECRET=your-oss-access-key-secret
OSS_BUCKET=your-oss-bucket

# 微信小程序
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# 业务配置
# 心愿力奖励
WISH_POWER_DAILY_RITUAL=10
WISH_POWER_SHARE_WISH=5
WISH_POWER_RECEIVE_BLESSING=3

# 功德值奖励
MERIT_BLESS_WISH=10
MERIT_BLESS_WITH_IMAGE=5
MERIT_BLESS_WITH_LONG_TEXT=5
MERIT_RECEIVE_THANKS=2

# 排行榜配置
LEADERBOARD_UPDATE_INTERVAL=3600000
LEADERBOARD_TOP_COUNT=100
