<!--
  愿境举报页面
  用户举报不当内容或用户的统一页面
-->
<template>
  <view class="report-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar safe-area-top">
      <view class="navbar-content">
        <view class="navbar-back" @click="goBack">
          <image src="/static/icons/arrow-left.png" class="back-icon" />
        </view>
        <text class="navbar-title">举报</text>
        <view class="navbar-placeholder"></view>
      </view>
    </view>
    
    <!-- 举报对象信息 -->
    <view class="report-target" v-if="targetInfo">
      <wish-card class="target-card" shadow="light">
        <view class="target-content">
          <view class="target-header">
            <image :src="getTargetIcon()" class="target-icon" />
            <text class="target-type">{{ getTargetTypeText() }}</text>
          </view>
          
          <!-- 用户举报 -->
          <view v-if="reportType === 'user'" class="user-target">
            <image :src="targetInfo.avatar" class="user-avatar" />
            <view class="user-info">
              <text class="user-nickname">{{ targetInfo.nickname }}</text>
              <text class="user-desc">{{ targetInfo.bio || '这个人很神秘，什么都没留下' }}</text>
            </view>
          </view>
          
          <!-- 内容举报 -->
          <view v-else class="content-target">
            <text class="content-title">{{ targetInfo.title || '内容举报' }}</text>
            <text class="content-preview">{{ getContentPreview() }}</text>
          </view>
        </view>
      </wish-card>
    </view>
    
    <!-- 举报原因选择 -->
    <view class="report-reasons">
      <text class="section-title">请选择举报原因</text>
      
      <view class="reason-list">
        <view 
          class="reason-item"
          :class="{ 'reason-item--selected': selectedReason === reason.value }"
          v-for="reason in reportReasons"
          :key="reason.value"
          @click="selectReason(reason.value)"
        >
          <view class="reason-content">
            <image :src="reason.icon" class="reason-icon" />
            <view class="reason-info">
              <text class="reason-title">{{ reason.title }}</text>
              <text class="reason-desc">{{ reason.description }}</text>
            </view>
          </view>
          
          <view class="reason-check">
            <image 
              v-if="selectedReason === reason.value"
              src="/static/icons/check-circle.png" 
              class="check-icon" 
            />
            <view v-else class="check-circle"></view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 详细描述 -->
    <view class="report-description">
      <text class="section-title">详细描述（可选）</text>
      <text class="section-desc">请详细描述您遇到的问题，这将帮助我们更好地处理</text>
      
      <wish-input
        v-model="reportDescription"
        type="textarea"
        placeholder="请详细描述举报原因..."
        :maxlength="500"
        show-word-limit
        :auto-height="true"
        class="description-input"
      />
    </view>
    
    <!-- 证据上传 -->
    <view class="report-evidence">
      <text class="section-title">上传证据（可选）</text>
      <text class="section-desc">上传相关截图或证据，最多3张</text>
      
      <view class="evidence-upload">
        <view class="uploaded-images" v-if="evidenceImages.length > 0">
          <view 
            v-for="(image, index) in evidenceImages"
            :key="index"
            class="image-item"
          >
            <image :src="image" class="evidence-image" mode="aspectFill" />
            <view class="image-remove" @click="removeEvidence(index)">
              <image src="/static/icons/close-white.png" class="remove-icon" />
            </view>
          </view>
        </view>
        
        <view 
          v-if="evidenceImages.length < 3" 
          class="upload-area" 
          @click="chooseEvidence"
        >
          <image src="/static/icons/camera-large.png" class="upload-icon" />
          <text class="upload-text">点击上传证据</text>
        </view>
      </view>
    </view>
    
    <!-- 联系方式 -->
    <view class="report-contact">
      <text class="section-title">联系方式（可选）</text>
      <text class="section-desc">留下联系方式，我们会及时反馈处理结果</text>
      
      <wish-input
        v-model="contactInfo"
        placeholder="手机号或邮箱"
        :maxlength="50"
        class="contact-input"
      />
    </view>
    
    <!-- 提交按钮 -->
    <view class="report-actions">
      <view class="action-tips">
        <image src="/static/icons/shield.png" class="tips-icon" />
        <text class="tips-text">我们会在24小时内处理您的举报，感谢您维护社区环境</text>
      </view>
      
      <wish-button
        type="primary"
        size="large"
        text="提交举报"
        :loading="submitting"
        :disabled="!canSubmit"
        @click="submitReport"
        class="submit-button"
      />
    </view>
    
    <!-- 举报成功模态框 -->
    <wish-modal
      v-model:visible="showSuccessModal"
      title="举报提交成功"
      :show-close="false"
      :mask-closable="false"
    >
      <view class="success-content">
        <image src="/static/icons/report-success.png" class="success-icon" />
        <text class="success-text">感谢您的举报</text>
        <text class="success-desc">我们已收到您的举报，将在24小时内进行处理</text>
        
        <view class="report-id">
          <text class="id-label">举报编号：</text>
          <text class="id-value">{{ reportId }}</text>
        </view>
      </view>
      
      <template #footer>
        <wish-button
          type="primary"
          text="确定"
          @click="handleSuccess"
          class="success-button"
        />
      </template>
    </wish-modal>
  </view>
</template>

<script>
import { useSafetyStore } from '@/store'
import { navigation, toast } from '@/utils'

export default {
  data() {
    return {
      reportType: '', // user, wish, comment, blessing
      targetId: '',
      targetInfo: null,
      selectedReason: '',
      reportDescription: '',
      evidenceImages: [],
      contactInfo: '',
      submitting: false,
      showSuccessModal: false,
      reportId: '',
      reportReasons: []
    }
  },
  
  computed: {
    safetyStore() {
      return useSafetyStore()
    },
    
    canSubmit() {
      return this.selectedReason && !this.submitting
    }
  },
  
  onLoad(options) {
    this.reportType = options.type || 'user'
    this.targetId = options.targetId || ''
    this.initPage()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      try {
        // 获取举报对象信息
        await this.fetchTargetInfo()
        
        // 获取举报原因列表
        this.loadReportReasons()
      } catch (error) {
        console.error('页面初始化失败:', error)
        toast.error('页面加载失败')
        navigation.navigateBack()
      }
    },
    
    /**
     * 获取举报对象信息
     */
    async fetchTargetInfo() {
      try {
        this.targetInfo = await this.safetyStore.getReportTargetInfo(this.reportType, this.targetId)
      } catch (error) {
        throw error
      }
    },
    
    /**
     * 加载举报原因
     */
    loadReportReasons() {
      const reasonMap = {
        user: [
          {
            value: 'harassment',
            title: '骚扰他人',
            description: '发送骚扰信息或恶意私信',
            icon: '/static/icons/harassment.png'
          },
          {
            value: 'spam',
            title: '垃圾信息',
            description: '发布广告、刷屏等垃圾内容',
            icon: '/static/icons/spam.png'
          },
          {
            value: 'fake',
            title: '虚假信息',
            description: '冒充他人或发布虚假信息',
            icon: '/static/icons/fake.png'
          },
          {
            value: 'inappropriate',
            title: '不当行为',
            description: '其他不当或违规行为',
            icon: '/static/icons/inappropriate.png'
          }
        ],
        content: [
          {
            value: 'inappropriate_content',
            title: '不当内容',
            description: '包含色情、暴力等不当内容',
            icon: '/static/icons/inappropriate-content.png'
          },
          {
            value: 'hate_speech',
            title: '仇恨言论',
            description: '包含歧视、仇恨等言论',
            icon: '/static/icons/hate-speech.png'
          },
          {
            value: 'false_info',
            title: '虚假信息',
            description: '传播谣言或虚假信息',
            icon: '/static/icons/false-info.png'
          },
          {
            value: 'copyright',
            title: '版权侵犯',
            description: '侵犯他人版权或知识产权',
            icon: '/static/icons/copyright.png'
          },
          {
            value: 'other',
            title: '其他原因',
            description: '其他违规或不当内容',
            icon: '/static/icons/other-reason.png'
          }
        ]
      }
      
      this.reportReasons = reasonMap[this.reportType === 'user' ? 'user' : 'content'] || []
    },
    
    /**
     * 选择举报原因
     */
    selectReason(reason) {
      this.selectedReason = reason
    },
    
    /**
     * 选择证据图片
     */
    chooseEvidence() {
      const maxCount = 3 - this.evidenceImages.length
      if (maxCount <= 0) {
        toast.error('最多只能上传3张图片')
        return
      }
      
      uni.chooseImage({
        count: maxCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.evidenceImages.push(...res.tempFilePaths)
        }
      })
    },
    
    /**
     * 移除证据图片
     */
    removeEvidence(index) {
      this.evidenceImages.splice(index, 1)
    },
    
    /**
     * 提交举报
     */
    async submitReport() {
      if (!this.canSubmit) return
      
      this.submitting = true
      try {
        const reportData = {
          type: this.reportType,
          targetId: this.targetId,
          reason: this.selectedReason,
          description: this.reportDescription.trim(),
          evidence: this.evidenceImages,
          contact: this.contactInfo.trim()
        }
        
        const result = await this.safetyStore.submitReport(reportData)
        this.reportId = result.reportId
        
        this.showSuccessModal = true
      } catch (error) {
        console.error('提交举报失败:', error)
        toast.error(error.message || '提交失败，请重试')
      } finally {
        this.submitting = false
      }
    },
    
    /**
     * 处理成功
     */
    handleSuccess() {
      this.showSuccessModal = false
      navigation.navigateBack()
    },
    
    /**
     * 获取目标图标
     */
    getTargetIcon() {
      const iconMap = {
        user: '/static/icons/user-report.png',
        wish: '/static/icons/wish-report.png',
        comment: '/static/icons/comment-report.png',
        blessing: '/static/icons/blessing-report.png'
      }
      return iconMap[this.reportType] || '/static/icons/report.png'
    },
    
    /**
     * 获取目标类型文本
     */
    getTargetTypeText() {
      const textMap = {
        user: '举报用户',
        wish: '举报心愿',
        comment: '举报评论',
        blessing: '举报赐福'
      }
      return textMap[this.reportType] || '举报内容'
    },
    
    /**
     * 获取内容预览
     */
    getContentPreview() {
      if (!this.targetInfo) return ''
      
      const content = this.targetInfo.content || this.targetInfo.text || ''
      return content.length > 100 ? content.substring(0, 100) + '...' : content
    },
    
    /**
     * 页面跳转
     */
    goBack() {
      navigation.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.report-page {
  min-height: 100vh;
  background-color: $wish-bg-primary;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $wish-spacing-md;
}

.navbar-back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
}

.navbar-title {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
}

.navbar-placeholder {
  width: 48rpx;
}

/* 举报对象信息 */
.report-target {
  padding: $wish-spacing-md;
}

.target-card {
  margin: 0;
}

.target-content {
  padding: 0;
}

.target-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-md;
}

.target-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: $wish-spacing-sm;
}

.target-type {
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
}

.user-target {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-md;
}

.user-info {
  flex: 1;
}

.user-nickname {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.user-desc {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-target {
  display: flex;
  flex-direction: column;
}

.content-title {
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-preview {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 举报原因选择 */
.report-reasons {
  padding: $wish-spacing-md;
}

.section-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-md;
}

.section-desc {
  display: block;
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-md;
  line-height: 1.6;
}

.reason-list {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.reason-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-md;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;

  &--selected {
    border-color: $wish-color-primary;
    background-color: rgba(232, 180, 160, 0.1);
  }

  &:active {
    transform: scale(0.98);
  }
}

.reason-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.reason-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: $wish-spacing-md;
}

.reason-info {
  flex: 1;
}

.reason-title {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.reason-desc {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  line-height: 1.4;
}

.reason-check {
  margin-left: $wish-spacing-md;
}

.check-icon {
  width: 32rpx;
  height: 32rpx;
}

.check-circle {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid $wish-border-medium;
  border-radius: 50%;
}

/* 详细描述 */
.report-description {
  padding: $wish-spacing-md;
}

.description-input {
  margin-top: $wish-spacing-sm;
}

/* 证据上传 */
.report-evidence {
  padding: $wish-spacing-md;
}

.evidence-upload {
  margin-top: $wish-spacing-sm;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-sm;
  margin-bottom: $wish-spacing-sm;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
}

.evidence-image {
  width: 100%;
  height: 100%;
  border-radius: $wish-radius-md;
}

.image-remove {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-icon {
  width: 16rpx;
  height: 16rpx;
}

.upload-area {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed $wish-border-medium;
  border-radius: $wish-radius-md;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: border-color 0.3s ease;

  &:active {
    border-color: $wish-color-primary;
  }
}

.upload-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: $wish-spacing-sm;
  opacity: 0.6;
}

.upload-text {
  font-size: $wish-font-sm;
  color: $wish-text-disabled;
}

/* 联系方式 */
.report-contact {
  padding: $wish-spacing-md;
}

.contact-input {
  margin-top: $wish-spacing-sm;
}

/* 提交按钮 */
.report-actions {
  padding: $wish-spacing-md;
  margin-top: auto;
}

.action-tips {
  display: flex;
  align-items: flex-start;
  background-color: rgba(232, 180, 160, 0.1);
  border-radius: $wish-radius-md;
  padding: $wish-spacing-md;
  margin-bottom: $wish-spacing-lg;
}

.tips-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: $wish-spacing-sm;
  flex-shrink: 0;
  margin-top: 4rpx;
}

.tips-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  line-height: 1.6;
}

.submit-button {
  width: 100%;
}

/* 举报成功模态框 */
.success-content {
  text-align: center;
  padding: $wish-spacing-lg 0;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: $wish-spacing-lg;
}

.success-text {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.success-desc {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-lg;
  line-height: 1.6;
}

.report-id {
  background-color: $wish-bg-primary;
  border-radius: $wish-radius-md;
  padding: $wish-spacing-md;
  display: flex;
  align-items: center;
  justify-content: center;
}

.id-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin-right: $wish-spacing-xs;
}

.id-value {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
  font-weight: 500;
}

.success-button {
  width: 100%;
}
</style>
