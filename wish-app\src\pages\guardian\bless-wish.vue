<!--
  愿境赐福心愿页面
  守护者为心愿提供赐福和回应
-->
<template>
  <view class="bless-wish-page">
    <!-- 心愿信息 -->
    <view class="wish-info" v-if="wishDetail">
      <wish-card class="wish-card" shadow="medium">
        <view class="wish-content">
          <view class="wish-header">
            <image 
              :src="wishDetail.anonymous ? '/static/default-anonymous.png' : wishDetail.user.avatar" 
              class="user-avatar" 
            />
            <view class="user-info">
              <text class="user-nickname">
                {{ wishDetail.anonymous ? '匿名祈愿者' : wishDetail.user.nickname }}
              </text>
              <text class="wish-time">{{ formatTime(wishDetail.createdAt) }}</text>
            </view>
          </view>
          
          <text class="wish-title">{{ wishDetail.title }}</text>
          <text class="wish-desc">{{ wishDetail.content }}</text>
          
          <view class="wish-tags">
            <text 
              v-for="tag in wishDetail.tags" 
              :key="tag"
              class="wish-tag"
            >
              #{{ tag }}
            </text>
          </view>
        </view>
      </wish-card>
    </view>
    
    <!-- 赐福模板选择 -->
    <view class="blessing-templates">
      <text class="section-title">选择赐福模板</text>
      
      <view class="template-categories">
        <view 
          class="category-tab"
          :class="{ 'category-tab--active': selectedCategory === category.type }"
          v-for="category in templateCategories"
          :key="category.type"
          @click="selectCategory(category.type)"
        >
          <text class="category-text">{{ category.name }}</text>
        </view>
      </view>
      
      <scroll-view class="template-list" scroll-y>
        <view 
          class="template-item"
          :class="{ 'template-item--selected': selectedTemplate?.id === template.id }"
          v-for="template in currentTemplates"
          :key="template.id"
          @click="selectTemplate(template)"
        >
          <view class="template-preview">
            <text class="template-title">{{ template.title }}</text>
            <text class="template-content">{{ template.content }}</text>
          </view>
          
          <view class="template-meta">
            <view class="template-tags">
              <text 
                v-for="tag in template.tags"
                :key="tag"
                class="template-tag"
              >
                {{ tag }}
              </text>
            </view>
            <text class="template-usage">使用 {{ template.usageCount || 0 }} 次</text>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 自定义赐福 -->
    <view class="custom-blessing">
      <text class="section-title">个性化赐福内容</text>
      
      <wish-input
        v-model="blessingData.content"
        type="textarea"
        :placeholder="customPlaceholder"
        :maxlength="500"
        show-word-limit
        :auto-height="true"
        :error="errors.content"
        :error-message="errors.content"
        @input="clearError('content')"
      />
      
      <!-- 赐福图片 -->
      <view class="blessing-image">
        <text class="image-label">添加赐福图片（可选）</text>
        
        <view class="image-upload" @click="chooseImage">
          <image 
            v-if="blessingData.image"
            :src="blessingData.image"
            class="uploaded-image"
            mode="aspectFill"
          />
          <view v-else class="upload-placeholder">
            <image src="/static/icons/camera-large.png" class="upload-icon" />
            <text class="upload-text">点击添加图片</text>
          </view>
        </view>
        
        <view v-if="blessingData.image" class="image-actions">
          <wish-button
            type="ghost"
            size="small"
            text="重新选择"
            @click="chooseImage"
            class="image-button"
          />
          <wish-button
            type="error"
            size="small"
            text="删除"
            @click="removeImage"
            class="image-button"
          />
        </view>
      </view>
    </view>
    
    <!-- 赐福预览 -->
    <view class="blessing-preview" v-if="blessingData.content">
      <text class="section-title">赐福预览</text>
      
      <wish-card class="preview-card" shadow="light">
        <view class="preview-content">
          <view class="preview-header">
            <image :src="userStore.avatar" class="guardian-avatar" />
            <view class="guardian-info">
              <text class="guardian-name">{{ userStore.nickname }}</text>
              <text class="guardian-role">守护者</text>
            </view>
          </view>
          
          <text class="blessing-text">{{ blessingData.content }}</text>
          
          <image 
            v-if="blessingData.image"
            :src="blessingData.image"
            class="blessing-image-preview"
            mode="aspectFill"
          />
          
          <view class="blessing-footer">
            <text class="blessing-time">刚刚</text>
          </view>
        </view>
      </wish-card>
    </view>
    
    <!-- 赐福按钮 */
    <view class="blessing-actions">
      <view class="merit-info">
        <image src="/static/icons/merit-points.png" class="merit-icon" />
        <text class="merit-text">赐福将获得 {{ meritReward }} 功德值</text>
      </view>
      
      <wish-button
        type="primary"
        size="large"
        text="发送赐福"
        :loading="blessing"
        :disabled="!canBless"
        @click="sendBlessing"
        class="bless-button"
      />
    </view>
    
    <!-- 赐福成功模态框 -->
    <wish-modal
      v-model:visible="showSuccessModal"
      title="赐福发送成功"
      :show-close="false"
      :mask-closable="false"
    >
      <view class="success-content">
        <image src="/static/icons/blessing-success.png" class="success-icon" />
        <text class="success-text">你的赐福已经送达</text>
        <text class="success-desc">祈愿者会收到你温暖的回应</text>
        
        <view class="reward-info">
          <text class="reward-text">获得功德值 +{{ meritReward }}</text>
        </view>
      </view>
      
      <template #footer>
        <view class="success-actions">
          <wish-button
            type="ghost"
            text="继续守护"
            @click="continueGuarding"
            class="success-button"
          />
          <wish-button
            type="primary"
            text="查看我的赐福"
            @click="goToMyBlessings"
            class="success-button"
          />
        </view>
      </template>
    </wish-modal>
  </view>
</template>

<script>
import { useUserStore, useWishStore, useGuardianStore } from '@/store'
import { timeUtils, navigation, toast } from '@/utils'

export default {
  data() {
    return {
      wishId: '',
      wishDetail: null,
      selectedCategory: 'encouragement',
      selectedTemplate: null,
      blessingData: {
        content: '',
        image: ''
      },
      errors: {},
      blessing: false,
      showSuccessModal: false,
      templateCategories: [
        { type: 'encouragement', name: '鼓励' },
        { type: 'comfort', name: '安慰' },
        { type: 'blessing', name: '祝福' },
        { type: 'guidance', name: '指导' },
        { type: 'support', name: '支持' }
      ]
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    wishStore() {
      return useWishStore()
    },
    
    guardianStore() {
      return useGuardianStore()
    },
    
    currentTemplates() {
      return this.guardianStore.templatesByType[this.selectedCategory] || []
    },
    
    customPlaceholder() {
      if (this.selectedTemplate) {
        return `基于模板进行个性化修改：\n${this.selectedTemplate.content}`
      }
      return '写下你想对祈愿者说的话，传递温暖和力量...'
    },
    
    canBless() {
      return this.blessingData.content.trim().length > 0
    },
    
    meritReward() {
      let baseReward = 10
      
      // 图片赐福额外奖励
      if (this.blessingData.image) {
        baseReward += 5
      }
      
      // 内容长度奖励
      if (this.blessingData.content.length > 100) {
        baseReward += 5
      }
      
      return baseReward
    }
  },
  
  onLoad(options) {
    this.wishId = options.wishId
    this.initPage()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      try {
        // 获取心愿详情
        await this.fetchWishDetail()
        
        // 获取赐福模板
        await this.guardianStore.fetchBlessingTemplates()
      } catch (error) {
        console.error('页面初始化失败:', error)
        toast.error('页面加载失败')
        navigation.navigateBack()
      }
    },
    
    /**
     * 获取心愿详情
     */
    async fetchWishDetail() {
      try {
        this.wishDetail = await this.wishStore.fetchWishDetail(this.wishId)
      } catch (error) {
        throw error
      }
    },
    
    /**
     * 选择模板分类
     */
    selectCategory(category) {
      this.selectedCategory = category
      this.selectedTemplate = null
    },
    
    /**
     * 选择模板
     */
    selectTemplate(template) {
      this.selectedTemplate = template
      this.blessingData.content = template.content
    },
    
    /**
     * 选择图片
     */
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          try {
            const tempFilePath = res.tempFilePaths[0]
            
            // 显示加载提示
            uni.showLoading({
              title: '上传中...',
              mask: true
            })
            
            // TODO: 上传图片到服务器
            // const uploadResult = await this.guardianStore.uploadBlessingImage(tempFilePath)
            // this.blessingData.image = uploadResult.url
            
            // 临时使用本地图片
            this.blessingData.image = tempFilePath
            
            toast.success('图片添加成功')
            
          } catch (error) {
            console.error('上传图片失败:', error)
            toast.error('上传图片失败')
          } finally {
            uni.hideLoading()
          }
        }
      })
    },
    
    /**
     * 删除图片
     */
    removeImage() {
      this.blessingData.image = ''
    },
    
    /**
     * 表单验证
     */
    validateForm() {
      const errors = {}
      
      if (!this.blessingData.content.trim()) {
        errors.content = '请输入赐福内容'
      } else if (this.blessingData.content.length > 500) {
        errors.content = '赐福内容不能超过500字'
      }
      
      this.errors = errors
      return Object.keys(errors).length === 0
    },
    
    /**
     * 清除错误信息
     */
    clearError(field) {
      if (this.errors[field]) {
        this.$delete(this.errors, field)
      }
    },
    
    /**
     * 发送赐福
     */
    async sendBlessing() {
      if (!this.validateForm()) {
        return
      }
      
      this.blessing = true
      try {
        const blessingData = {
          wishId: this.wishId,
          content: this.blessingData.content.trim(),
          image: this.blessingData.image,
          templateId: this.selectedTemplate?.id
        }
        
        // 发送赐福
        await this.guardianStore.blessWish(blessingData)
        
        // 更新心愿状态
        this.wishStore.updateWishStatus(this.wishId, 'blessed')
        
        // 增加功德值
        this.userStore.addMeritPoints(this.meritReward)
        
        // 显示成功模态框
        this.showSuccessModal = true
        
      } catch (error) {
        console.error('发送赐福失败:', error)
        toast.error(error.message || '发送赐福失败')
      } finally {
        this.blessing = false
      }
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      return timeUtils.relativeTime(timestamp)
    },
    
    /**
     * 继续守护
     */
    continueGuarding() {
      this.showSuccessModal = false
      navigation.navigateBack()
    },
    
    /**
     * 查看我的赐福
     */
    goToMyBlessings() {
      this.showSuccessModal = false
      navigation.navigateTo('/pages/guardian/my-blessings')
    }
  }
}
</script>

<style lang="scss" scoped>
.bless-wish-page {
  min-height: 100vh;
  background-color: $wish-bg-primary;
  padding: $wish-spacing-md;
  padding-bottom: calc($wish-spacing-md + constant(safe-area-inset-bottom));
  padding-bottom: calc($wish-spacing-md + env(safe-area-inset-bottom));
}

/* 心愿信息 */
.wish-info {
  margin-bottom: $wish-spacing-xl;
}

.wish-card {
  margin: 0;
}

.wish-content {
  padding: 0;
}

.wish-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-sm;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
}

.user-info {
  flex: 1;
}

.user-nickname {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: 4rpx;
}

.wish-time {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.wish-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
  line-height: 1.4;
}

.wish-desc {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-sm;
}

.wish-tags {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-xs;
}

.wish-tag {
  font-size: $wish-font-xs;
  color: $wish-color-primary;
  background-color: rgba(232, 180, 160, 0.1);
  padding: 4rpx 8rpx;
  border-radius: $wish-radius-sm;
}

/* 赐福模板选择 */
.blessing-templates {
  margin-bottom: $wish-spacing-xl;
}

.section-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-md;
}

.template-categories {
  display: flex;
  margin-bottom: $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-xs;
  box-shadow: $wish-shadow-sm;
}

.category-tab {
  flex: 1;
  text-align: center;
  padding: $wish-spacing-sm;
  border-radius: $wish-radius-md;
  transition: all 0.3s ease;

  &--active {
    background-color: $wish-color-secondary;

    .category-text {
      color: $wish-text-inverse;
      font-weight: 600;
    }
  }
}

.category-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  transition: all 0.3s ease;
}

.template-list {
  max-height: 400rpx;
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-sm;
  box-shadow: $wish-shadow-sm;
}

.template-item {
  padding: $wish-spacing-md;
  border: 2rpx solid $wish-border-light;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
  transition: all 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &--selected {
    border-color: $wish-color-secondary;
    background-color: rgba(212, 165, 116, 0.1);
  }
}

.template-preview {
  margin-bottom: $wish-spacing-sm;
}

.template-title {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.template-content {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.template-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.template-tags {
  display: flex;
  gap: $wish-spacing-xs;
}

.template-tag {
  font-size: $wish-font-xs;
  color: $wish-color-secondary;
  background-color: rgba(212, 165, 116, 0.1);
  padding: 2rpx 6rpx;
  border-radius: $wish-radius-sm;
}

.template-usage {
  font-size: $wish-font-xs;
  color: $wish-text-disabled;
}

/* 自定义赐福 */
.custom-blessing {
  margin-bottom: $wish-spacing-xl;
}

.blessing-image {
  margin-top: $wish-spacing-md;
}

.image-label {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-sm;
}

.image-upload {
  width: 100%;
  height: 300rpx;
  border: 2rpx dashed $wish-border-medium;
  border-radius: $wish-radius-md;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: $wish-spacing-sm;
  transition: border-color 0.3s ease;

  &:active {
    border-color: $wish-color-secondary;
  }
}

.uploaded-image {
  width: 100%;
  height: 100%;
  border-radius: $wish-radius-md;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  width: 64rpx;
  height: 64rpx;
  margin-bottom: $wish-spacing-sm;
  opacity: 0.6;
}

.upload-text {
  font-size: $wish-font-md;
  color: $wish-text-disabled;
}

.image-actions {
  display: flex;
  gap: $wish-spacing-sm;
}

.image-button {
  flex: 1;
  min-height: auto;
  padding: $wish-spacing-xs $wish-spacing-sm;
}

/* 赐福预览 */
.blessing-preview {
  margin-bottom: $wish-spacing-xl;
}

.preview-card {
  margin: 0;
}

.preview-content {
  padding: 0;
}

.preview-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-sm;
}

.guardian-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
}

.guardian-info {
  flex: 1;
}

.guardian-name {
  display: block;
  font-size: $wish-font-sm;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: 2rpx;
}

.guardian-role {
  font-size: $wish-font-xs;
  color: $wish-color-secondary;
}

.blessing-text {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-primary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-sm;
}

.blessing-image-preview {
  width: 100%;
  max-height: 400rpx;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
}

.blessing-footer {
  display: flex;
  justify-content: flex-end;
}

.blessing-time {
  font-size: $wish-font-xs;
  color: $wish-text-disabled;
}

/* 赐福按钮 */
.blessing-actions {
  position: sticky;
  bottom: 0;
  background-color: $wish-bg-primary;
  padding-top: $wish-spacing-md;
}

.merit-info {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: $wish-spacing-md;
}

.merit-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: $wish-spacing-xs;
}

.merit-text {
  font-size: $wish-font-sm;
  color: $wish-color-secondary;
  font-weight: 500;
}

.bless-button {
  width: 100%;
}

/* 成功模态框 */
.success-content {
  text-align: center;
  padding: $wish-spacing-lg 0;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: $wish-spacing-lg;
}

.success-text {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.success-desc {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-md;
}

.reward-info {
  background-color: rgba(212, 165, 116, 0.1);
  border-radius: $wish-radius-md;
  padding: $wish-spacing-sm;
}

.reward-text {
  font-size: $wish-font-md;
  color: $wish-color-secondary;
  font-weight: 600;
}

.success-actions {
  display: flex;
  gap: $wish-spacing-md;
}

.success-button {
  flex: 1;
}
</style>
