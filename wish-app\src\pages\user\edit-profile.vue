<!--
  愿境编辑资料页面
  用户个人信息编辑
-->
<template>
  <view class="edit-profile-page">
    <!-- 头像编辑 -->
    <view class="avatar-section">
      <text class="section-title">头像</text>
      <view class="avatar-container" @click="changeAvatar">
        <image :src="formData.avatar" class="user-avatar" />
        <view class="avatar-overlay">
          <image src="/static/icons/camera.png" class="camera-icon" />
          <text class="change-text">更换头像</text>
        </view>
      </view>
    </view>
    
    <!-- 基本信息编辑 -->
    <view class="form-section">
      <wish-input
        v-model="formData.nickname"
        label="昵称"
        placeholder="请输入昵称"
        :maxlength="20"
        show-word-limit
        :error="errors.nickname"
        :error-message="errors.nickname"
        @input="clearError('nickname')"
      />
      
      <wish-input
        v-model="formData.bio"
        type="textarea"
        label="个人简介"
        placeholder="介绍一下自己吧..."
        :maxlength="200"
        show-word-limit
        :auto-height="true"
        :error="errors.bio"
        :error-message="errors.bio"
        @input="clearError('bio')"
      />
      
      <!-- 性别选择 -->
      <view class="form-item">
        <text class="form-label">性别</text>
        <view class="gender-options">
          <view 
            class="gender-option"
            :class="{ 'gender-option--active': formData.gender === 'male' }"
            @click="selectGender('male')"
          >
            <text class="gender-text">男</text>
          </view>
          <view 
            class="gender-option"
            :class="{ 'gender-option--active': formData.gender === 'female' }"
            @click="selectGender('female')"
          >
            <text class="gender-text">女</text>
          </view>
          <view 
            class="gender-option"
            :class="{ 'gender-option--active': formData.gender === 'other' }"
            @click="selectGender('other')"
          >
            <text class="gender-text">其他</text>
          </view>
        </view>
      </view>
      
      <!-- 生日选择 -->
      <view class="form-item">
        <text class="form-label">生日</text>
        <picker 
          mode="date" 
          :value="formData.birthday"
          @change="onBirthdayChange"
          class="birthday-picker"
        >
          <view class="picker-content">
            <text class="picker-text">{{ formData.birthday || '请选择生日' }}</text>
            <image src="/static/icons/arrow-right.png" class="picker-arrow" />
          </view>
        </picker>
      </view>
      
      <!-- 地区选择 -->
      <view class="form-item">
        <text class="form-label">地区</text>
        <picker 
          mode="region" 
          :value="regionArray"
          @change="onRegionChange"
          class="region-picker"
        >
          <view class="picker-content">
            <text class="picker-text">{{ regionText || '请选择地区' }}</text>
            <image src="/static/icons/arrow-right.png" class="picker-arrow" />
          </view>
        </picker>
      </view>
    </view>
    
    <!-- 保存按钮 -->
    <view class="save-section">
      <wish-button
        type="primary"
        size="large"
        text="保存"
        :loading="saving"
        @click="handleSave"
        class="save-button"
      />
    </view>
  </view>
</template>

<script>
import { useUserStore } from '@/store'
import { validator, toast, navigation } from '@/utils'

export default {
  data() {
    return {
      formData: {
        avatar: '',
        nickname: '',
        bio: '',
        gender: '',
        birthday: '',
        region: ''
      },
      regionArray: [],
      errors: {},
      saving: false
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    regionText() {
      return this.regionArray.length > 0 ? this.regionArray.join(' ') : ''
    }
  },
  
  onLoad() {
    this.initFormData()
  },
  
  methods: {
    /**
     * 初始化表单数据
     */
    initFormData() {
      const userInfo = this.userStore.userInfo
      if (userInfo) {
        this.formData = {
          avatar: userInfo.avatar || '/static/default-avatar.png',
          nickname: userInfo.nickname || '',
          bio: userInfo.bio || '',
          gender: userInfo.gender || '',
          birthday: userInfo.birthday || '',
          region: userInfo.region || ''
        }
        
        // 解析地区数据
        if (userInfo.region) {
          this.regionArray = userInfo.region.split(' ')
        }
      }
    },
    
    /**
     * 表单验证
     */
    validateForm() {
      const errors = {}
      
      // 验证昵称
      if (!this.formData.nickname) {
        errors.nickname = '请输入昵称'
      } else if (!validator.isValidNickname(this.formData.nickname)) {
        errors.nickname = '昵称格式不正确'
      }
      
      // 验证个人简介
      if (this.formData.bio && this.formData.bio.length > 200) {
        errors.bio = '个人简介不能超过200字'
      }
      
      this.errors = errors
      return Object.keys(errors).length === 0
    },
    
    /**
     * 清除错误信息
     */
    clearError(field) {
      if (this.errors[field]) {
        this.$delete(this.errors, field)
      }
    },
    
    /**
     * 更换头像
     */
    changeAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          try {
            const tempFilePath = res.tempFilePaths[0]
            
            // 显示加载提示
            uni.showLoading({
              title: '上传中...',
              mask: true
            })
            
            // TODO: 上传头像到服务器
            // const uploadResult = await this.userStore.uploadAvatar(tempFilePath)
            // this.formData.avatar = uploadResult.url
            
            // 临时使用本地图片
            this.formData.avatar = tempFilePath
            
            toast.success('头像更新成功')
            
          } catch (error) {
            console.error('上传头像失败:', error)
            toast.error('上传头像失败')
          } finally {
            uni.hideLoading()
          }
        },
        fail: (error) => {
          console.error('选择图片失败:', error)
        }
      })
    },
    
    /**
     * 选择性别
     */
    selectGender(gender) {
      this.formData.gender = gender
    },
    
    /**
     * 生日变更
     */
    onBirthdayChange(event) {
      this.formData.birthday = event.detail.value
    },
    
    /**
     * 地区变更
     */
    onRegionChange(event) {
      this.regionArray = event.detail.value
      this.formData.region = event.detail.value.join(' ')
    },
    
    /**
     * 保存资料
     */
    async handleSave() {
      if (!this.validateForm()) {
        return
      }
      
      this.saving = true
      try {
        await this.userStore.updateProfile(this.formData)
        
        toast.success('保存成功')
        
        // 返回上一页
        setTimeout(() => {
          navigation.navigateBack()
        }, 1000)
        
      } catch (error) {
        console.error('保存失败:', error)
        toast.error(error.message || '保存失败，请重试')
      } finally {
        this.saving = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.edit-profile-page {
  min-height: 100vh;
  background-color: $wish-bg-primary;
  padding: $wish-spacing-md;
}

/* 头像编辑 */
.avatar-section {
  text-align: center;
  margin-bottom: $wish-spacing-xl;
}

.section-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-md;
}

.avatar-container {
  position: relative;
  display: inline-block;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 4rpx solid $wish-bg-secondary;
  box-shadow: $wish-shadow-md;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  .avatar-container:active & {
    opacity: 1;
  }
}

.camera-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: $wish-spacing-xs;
}

.change-text {
  font-size: $wish-font-xs;
  color: $wish-text-inverse;
}

/* 表单区域 */
.form-section {
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-md;
  box-shadow: $wish-shadow-sm;
  margin-bottom: $wish-spacing-lg;
}

.form-item {
  margin-bottom: $wish-spacing-lg;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-sm;
}

/* 性别选择 */
.gender-options {
  display: flex;
  gap: $wish-spacing-sm;
}

.gender-option {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid $wish-border-medium;
  border-radius: $wish-radius-md;
  transition: all 0.3s ease;
  
  &--active {
    border-color: $wish-color-primary;
    background-color: rgba(232, 180, 160, 0.1);
  }
}

.gender-text {
  font-size: $wish-font-md;
  color: $wish-text-primary;
}

/* 选择器样式 */
.birthday-picker,
.region-picker {
  width: 100%;
}

.picker-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 80rpx;
  padding: 0 $wish-spacing-md;
  border: 2rpx solid $wish-border-light;
  border-radius: $wish-radius-md;
  background-color: $wish-bg-secondary;
}

.picker-text {
  font-size: $wish-font-md;
  color: $wish-text-primary;
}

.picker-arrow {
  width: 16rpx;
  height: 16rpx;
}

/* 保存按钮 */
.save-section {
  margin-top: $wish-spacing-xl;
}

.save-button {
  width: 100%;
}
</style>
