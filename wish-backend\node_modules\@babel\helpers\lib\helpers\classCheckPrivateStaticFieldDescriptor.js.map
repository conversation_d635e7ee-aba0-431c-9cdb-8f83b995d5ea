{"version": 3, "names": ["_classCheckPrivateStaticFieldDescriptor", "descriptor", "action", "undefined", "TypeError"], "sources": ["../../src/helpers/classCheckPrivateStaticFieldDescriptor.js"], "sourcesContent": ["/* @minVersion 7.13.10 */\n/* @onlyBabel7 */\n\nexport default function _classCheckPrivateStaticFieldDescriptor(\n  descriptor,\n  action,\n) {\n  if (descriptor === undefined) {\n    throw new TypeError(\n      \"attempted to \" + action + \" private static field before its declaration\",\n    );\n  }\n}\n"], "mappings": ";;;;;;AAGe,SAASA,uCAAuCA,CAC7DC,UAAU,EACVC,MAAM,EACN;EACA,IAAID,UAAU,KAAKE,SAAS,EAAE;IAC5B,MAAM,IAAIC,SAAS,CACjB,eAAe,GAAGF,MAAM,GAAG,8CAC7B,CAAC;EACH;AACF", "ignoreList": []}