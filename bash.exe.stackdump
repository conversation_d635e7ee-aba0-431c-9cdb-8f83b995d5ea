Stack trace:
Frame         Function      Args
0007FFFFAC10  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9B10) msys-2.0.dll+0x1FE8E
0007FFFFAC10  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAEE8) msys-2.0.dll+0x67F9
0007FFFFAC10  000210046832 (000210286019, 0007FFFFAAC8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFAC10  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAC10  000210068E24 (0007FFFFAC20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFAEF0  00021006A225 (0007FFFFAC20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA0C570000 ntdll.dll
7FFA0BB70000 KERNEL32.DLL
7FFA09950000 KERNELBASE.dll
7FFA0AF90000 USER32.dll
7FFA09850000 win32u.dll
7FFA0BC50000 GDI32.dll
7FFA09D30000 gdi32full.dll
7FFA098B0000 msvcp_win.dll
7FFA09EE0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA0AD80000 advapi32.dll
7FFA0B190000 msvcrt.dll
7FFA0BC80000 sechost.dll
7FFA09880000 bcrypt.dll
7FFA0A750000 RPCRT4.dll
7FFA08E70000 CRYPTBASE.DLL
7FFA09E60000 bcryptPrimitives.dll
7FFA0B150000 IMM32.DLL
