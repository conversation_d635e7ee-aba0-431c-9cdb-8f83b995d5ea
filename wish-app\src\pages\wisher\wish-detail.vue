<!--
  愿境心愿详情页面
  展示心愿完整信息和互动功能
-->
<template>
  <view class="wish-detail-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar safe-area-top">
      <view class="navbar-content">
        <view class="navbar-back" @click="goBack">
          <image src="/static/icons/arrow-left.png" class="back-icon" />
        </view>
        <text class="navbar-title">心愿详情</text>
        <view class="navbar-actions">
          <view class="navbar-action" @click="shareWish">
            <image src="/static/icons/share.png" class="action-icon" />
          </view>
          <view class="navbar-action" @click="showMoreActions" v-if="isMyWish">
            <image src="/static/icons/more.png" class="action-icon" />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 主要内容 -->
    <scroll-view class="main-content" scroll-y v-if="wishDetail">
      <!-- 心愿信息 -->
      <view class="wish-info">
        <wish-card class="wish-card" shadow="medium">
          <view class="wish-content">
            <!-- 用户信息 -->
            <view class="wish-header">
              <image 
                :src="wishDetail.anonymous ? '/static/default-anonymous.png' : wishDetail.user.avatar" 
                class="user-avatar" 
                @click="goToUserProfile"
              />
              <view class="user-info">
                <text class="user-nickname">
                  {{ wishDetail.anonymous ? '匿名祈愿者' : wishDetail.user.nickname }}
                </text>
                <text class="wish-time">{{ formatTime(wishDetail.createdAt) }}</text>
              </view>
              <view class="wish-type">
                <image :src="getTypeIcon(wishDetail.type)" class="type-icon" />
                <text class="type-text">{{ getTypeText(wishDetail.type) }}</text>
              </view>
            </view>
            
            <!-- 心愿内容 -->
            <text class="wish-title">{{ wishDetail.title }}</text>
            <text class="wish-desc">{{ wishDetail.content }}</text>
            
            <!-- 心愿图片 -->
            <view v-if="wishDetail.images && wishDetail.images.length > 0" class="wish-images">
              <image 
                v-for="(image, index) in wishDetail.images"
                :key="index"
                :src="image"
                class="wish-image"
                mode="aspectFill"
                @click="previewImages(index)"
              />
            </view>
            
            <!-- 心愿标签 -->
            <view class="wish-tags">
              <text 
                v-for="tag in wishDetail.tags" 
                :key="tag"
                class="wish-tag"
              >
                #{{ tag }}
              </text>
            </view>
            
            <!-- 心愿状态 -->
            <view class="wish-status">
              <view class="status-item">
                <image :src="getStatusIcon(wishDetail.status)" class="status-icon" />
                <text class="status-text">{{ getStatusText(wishDetail.status) }}</text>
              </view>
              
              <view v-if="wishDetail.status === 'blessed'" class="blessed-info">
                <text class="blessed-text">已收到 {{ wishDetail.blessings?.length || 0 }} 个赐福</text>
              </view>
            </view>
          </view>
        </wish-card>
      </view>
      
      <!-- 互动数据 -->
      <view class="interaction-stats">
        <view class="stat-item" @click="toggleSupport">
          <image 
            :src="wishDetail.isSupported ? '/static/icons/heart-filled.png' : '/static/icons/heart.png'" 
            class="stat-icon"
            :class="{ 'stat-icon--active': wishDetail.isSupported }"
          />
          <text class="stat-text">{{ wishDetail.supportCount || 0 }}</text>
          <text class="stat-label">助力</text>
        </view>
        
        <view class="stat-item" @click="scrollToComments">
          <image src="/static/icons/comment.png" class="stat-icon" />
          <text class="stat-text">{{ wishDetail.commentCount || 0 }}</text>
          <text class="stat-label">评论</text>
        </view>
        
        <view class="stat-item" @click="shareWish">
          <image src="/static/icons/share.png" class="stat-icon" />
          <text class="stat-label">分享</text>
        </view>
        
        <view v-if="!isMyWish && userStore.isGuardian" class="stat-item" @click="blessWish">
          <image src="/static/icons/blessing.png" class="stat-icon" />
          <text class="stat-label">赐福</text>
        </view>
      </view>
      
      <!-- 赐福列表 -->
      <view v-if="wishDetail.blessings && wishDetail.blessings.length > 0" class="blessings-section">
        <text class="section-title">收到的赐福</text>
        
        <view class="blessing-list">
          <wish-card
            v-for="blessing in wishDetail.blessings"
            :key="blessing.id"
            class="blessing-item"
            shadow="light"
            @click="goToBlessingDetail(blessing.id)"
          >
            <view class="blessing-content">
              <view class="blessing-header">
                <image :src="blessing.guardian.avatar" class="guardian-avatar" />
                <view class="guardian-info">
                  <text class="guardian-name">{{ blessing.guardian.nickname }}</text>
                  <text class="guardian-role">守护者</text>
                </view>
                <text class="blessing-time">{{ formatTime(blessing.createdAt) }}</text>
              </view>
              
              <text class="blessing-text">{{ blessing.content }}</text>
              
              <image 
                v-if="blessing.image"
                :src="blessing.image"
                class="blessing-image"
                mode="aspectFill"
                @click.stop="previewImage(blessing.image)"
              />
              
              <view class="blessing-actions">
                <wish-button
                  v-if="isMyWish && !blessing.isThanked"
                  type="primary"
                  size="small"
                  text="感谢"
                  @click.stop="thankBlessing(blessing.id)"
                  class="thank-button"
                />
              </view>
            </view>
          </wish-card>
        </view>
      </view>
      
      <!-- 评论区域 -->
      <view class="comments-section" id="comments-section">
        <view class="section-header">
          <text class="section-title">评论 ({{ wishDetail.commentCount || 0 }})</text>
          <view class="comment-sort" @click="showSortModal = true">
            <text class="sort-text">{{ currentSortText }}</text>
            <image src="/static/icons/arrow-down.png" class="sort-icon" />
          </view>
        </view>
        
        <!-- 评论列表 -->
        <view class="comment-list">
          <view 
            v-for="comment in commentList"
            :key="comment.id"
            class="comment-item"
          >
            <view class="comment-main">
              <image :src="comment.user.avatar" class="comment-avatar" />
              <view class="comment-content">
                <view class="comment-header">
                  <text class="comment-user">{{ comment.user.nickname }}</text>
                  <text class="comment-time">{{ formatTime(comment.createdAt) }}</text>
                </view>
                
                <text class="comment-text">{{ comment.content }}</text>
                
                <view class="comment-actions">
                  <view class="action-item" @click="likeComment(comment.id)">
                    <image 
                      :src="comment.isLiked ? '/static/icons/thumb-up-filled.png' : '/static/icons/thumb-up.png'" 
                      class="action-icon"
                    />
                    <text class="action-text">{{ comment.likeCount || 0 }}</text>
                  </view>
                  
                  <view class="action-item" @click="replyComment(comment)">
                    <image src="/static/icons/reply.png" class="action-icon" />
                    <text class="action-text">回复</text>
                  </view>
                </view>
              </view>
            </view>
            
            <!-- 回复列表 -->
            <view v-if="comment.replies && comment.replies.length > 0" class="reply-list">
              <view 
                v-for="reply in comment.replies"
                :key="reply.id"
                class="reply-item"
              >
                <image :src="reply.user.avatar" class="reply-avatar" />
                <view class="reply-content">
                  <view class="reply-header">
                    <text class="reply-user">{{ reply.user.nickname }}</text>
                    <text v-if="reply.replyTo" class="reply-to">
                      回复 {{ reply.replyTo.nickname }}
                    </text>
                    <text class="reply-time">{{ formatTime(reply.createdAt) }}</text>
                  </view>
                  
                  <text class="reply-text">{{ reply.content }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        
        <!-- 加载更多评论 -->
        <view class="load-more-comments" v-if="hasMoreComments">
          <wish-loading v-if="loadingComments" type="dots" text="加载中..." />
          <text v-else class="load-text" @click="loadMoreComments">加载更多评论</text>
        </view>
      </view>
    </scroll-view>
    
    <!-- 底部评论输入框 -->
    <view class="comment-input-bar safe-area-bottom">
      <view class="input-wrapper">
        <wish-input
          v-model="commentContent"
          placeholder="写下你的评论..."
          :maxlength="200"
          @focus="showCommentModal = true"
          class="comment-input"
        />
        <wish-button
          type="primary"
          size="small"
          text="发送"
          :disabled="!commentContent.trim()"
          @click="submitComment"
          class="send-button"
        />
      </view>
    </view>
    
    <!-- 评论输入模态框 -->
    <wish-modal
      v-model:visible="showCommentModal"
      title="发表评论"
      position="bottom"
    >
      <view class="comment-modal">
        <wish-input
          v-model="commentContent"
          type="textarea"
          :placeholder="commentPlaceholder"
          :maxlength="200"
          show-word-limit
          :auto-height="true"
          class="modal-input"
        />
      </view>
      
      <template #footer>
        <view class="comment-actions">
          <wish-button
            type="ghost"
            text="取消"
            @click="cancelComment"
            class="comment-button"
          />
          <wish-button
            type="primary"
            text="发表"
            :loading="submittingComment"
            :disabled="!commentContent.trim()"
            @click="submitComment"
            class="comment-button"
          />
        </view>
      </template>
    </wish-modal>
    
    <!-- 排序选择模态框 -->
    <wish-modal
      v-model:visible="showSortModal"
      title="评论排序"
      position="bottom"
    >
      <view class="sort-options">
        <view 
          class="sort-option"
          :class="{ 'sort-option--active': commentSort === sort.value }"
          v-for="sort in sortOptions"
          :key="sort.value"
          @click="changeCommentSort(sort.value)"
        >
          <text class="sort-option-text">{{ sort.label }}</text>
          <image 
            v-if="commentSort === sort.value"
            src="/static/icons/check.png" 
            class="sort-check" 
          />
        </view>
      </view>
    </wish-modal>
    
    <!-- 更多操作模态框 -->
    <wish-modal
      v-model:visible="showMoreModal"
      title="更多操作"
      position="bottom"
    >
      <view class="more-actions">
        <view class="action-option" @click="editWish">
          <image src="/static/icons/edit.png" class="action-option-icon" />
          <text class="action-option-text">编辑心愿</text>
        </view>
        
        <view class="action-option" @click="deleteWish">
          <image src="/static/icons/delete.png" class="action-option-icon" />
          <text class="action-option-text">删除心愿</text>
        </view>
      </view>
    </wish-modal>
    
    <!-- 加载状态 -->
    <wish-loading 
      v-if="loading" 
      type="heart" 
      text="加载中..." 
      class="page-loading"
    />
  </view>
</template>

<script>
import { useUserStore, useWishStore, useSocialStore } from '@/store'
import { timeUtils, navigation, toast } from '@/utils'

export default {
  data() {
    return {
      wishId: '',
      wishDetail: null,
      loading: false,
      commentContent: '',
      commentList: [],
      commentSort: 'latest',
      loadingComments: false,
      submittingComment: false,
      showCommentModal: false,
      showSortModal: false,
      showMoreModal: false,
      replyingTo: null,
      sortOptions: [
        { value: 'latest', label: '最新' },
        { value: 'hot', label: '最热' },
        { value: 'earliest', label: '最早' }
      ]
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    wishStore() {
      return useWishStore()
    },
    
    socialStore() {
      return useSocialStore()
    },
    
    isMyWish() {
      return this.wishDetail && this.wishDetail.user.id === this.userStore.userId
    },
    
    hasMoreComments() {
      return this.socialStore.commentPagination.hasMore
    },
    
    currentSortText() {
      const sort = this.sortOptions.find(s => s.value === this.commentSort)
      return sort?.label || '最新'
    },
    
    commentPlaceholder() {
      if (this.replyingTo) {
        return `回复 ${this.replyingTo.user.nickname}...`
      }
      return '写下你的评论...'
    }
  },
  
  onLoad(options) {
    this.wishId = options.id
    this.initPage()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      this.loading = true
      try {
        await Promise.all([
          this.fetchWishDetail(),
          this.loadComments(true)
        ])
      } catch (error) {
        console.error('页面初始化失败:', error)
        toast.error('页面加载失败')
        navigation.navigateBack()
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 获取心愿详情
     */
    async fetchWishDetail() {
      try {
        this.wishDetail = await this.wishStore.fetchWishDetail(this.wishId)
      } catch (error) {
        throw error
      }
    },
    
    /**
     * 加载评论
     */
    async loadComments(refresh = false) {
      this.loadingComments = true
      try {
        const params = {
          targetId: this.wishId,
          targetType: 'wish',
          sortBy: this.commentSort
        }
        
        const result = await this.socialStore.fetchComments(params, refresh)
        this.commentList = result.list || []
      } catch (error) {
        console.error('加载评论失败:', error)
      } finally {
        this.loadingComments = false
      }
    },
    
    /**
     * 加载更多评论
     */
    async loadMoreComments() {
      if (!this.hasMoreComments || this.loadingComments) return
      
      const params = {
        targetId: this.wishId,
        targetType: 'wish',
        sortBy: this.commentSort
      }
      
      const result = await this.socialStore.fetchComments(params, false)
      this.commentList.push(...(result.list || []))
    },
    
    /**
     * 切换助力状态
     */
    async toggleSupport() {
      try {
        if (this.wishDetail.isSupported) {
          await this.wishStore.unsupportWish(this.wishId)
          this.wishDetail.supportCount = Math.max(0, (this.wishDetail.supportCount || 0) - 1)
        } else {
          await this.wishStore.supportWish(this.wishId)
          this.wishDetail.supportCount = (this.wishDetail.supportCount || 0) + 1
        }
        
        this.wishDetail.isSupported = !this.wishDetail.isSupported
      } catch (error) {
        console.error('助力操作失败:', error)
        toast.error('操作失败，请重试')
      }
    },
    
    /**
     * 提交评论
     */
    async submitComment() {
      if (!this.commentContent.trim()) {
        return
      }
      
      this.submittingComment = true
      try {
        const commentData = {
          targetId: this.wishId,
          targetType: 'wish',
          content: this.commentContent.trim(),
          replyTo: this.replyingTo?.id
        }
        
        const newComment = await this.socialStore.createComment(commentData)
        
        // 添加到评论列表
        if (this.replyingTo) {
          // 添加到回复列表
          const parentComment = this.commentList.find(c => c.id === this.replyingTo.id)
          if (parentComment) {
            parentComment.replies = parentComment.replies || []
            parentComment.replies.push(newComment)
          }
        } else {
          // 添加到主评论列表
          this.commentList.unshift(newComment)
        }
        
        // 更新评论数量
        this.wishDetail.commentCount = (this.wishDetail.commentCount || 0) + 1
        
        // 重置输入
        this.commentContent = ''
        this.replyingTo = null
        this.showCommentModal = false
        
        toast.success('评论发表成功')
      } catch (error) {
        console.error('发表评论失败:', error)
        toast.error('发表评论失败')
      } finally {
        this.submittingComment = false
      }
    },
    
    /**
     * 取消评论
     */
    cancelComment() {
      this.commentContent = ''
      this.replyingTo = null
      this.showCommentModal = false
    },
    
    /**
     * 回复评论
     */
    replyComment(comment) {
      this.replyingTo = comment
      this.showCommentModal = true
    },
    
    /**
     * 点赞评论
     */
    async likeComment(commentId) {
      try {
        const comment = this.findComment(commentId)
        if (!comment) return
        
        if (comment.isLiked) {
          await this.socialStore.unlikeComment(commentId)
          comment.likeCount = Math.max(0, (comment.likeCount || 0) - 1)
        } else {
          await this.socialStore.likeComment(commentId)
          comment.likeCount = (comment.likeCount || 0) + 1
        }
        
        comment.isLiked = !comment.isLiked
      } catch (error) {
        console.error('点赞操作失败:', error)
        toast.error('操作失败')
      }
    },
    
    /**
     * 查找评论
     */
    findComment(commentId) {
      for (const comment of this.commentList) {
        if (comment.id === commentId) {
          return comment
        }
        if (comment.replies) {
          const reply = comment.replies.find(r => r.id === commentId)
          if (reply) return reply
        }
      }
      return null
    },
    
    /**
     * 改变评论排序
     */
    async changeCommentSort(sort) {
      this.commentSort = sort
      this.showSortModal = false
      await this.loadComments(true)
    },
    
    /**
     * 感谢赐福
     */
    async thankBlessing(blessingId) {
      try {
        await this.socialStore.thankBlessing(blessingId)
        
        // 更新赐福状态
        const blessing = this.wishDetail.blessings.find(b => b.id === blessingId)
        if (blessing) {
          blessing.isThanked = true
        }
        
        toast.success('感谢已发送')
      } catch (error) {
        console.error('感谢失败:', error)
        toast.error('感谢失败')
      }
    },
    
    /**
     * 分享心愿
     */
    shareWish() {
      // TODO: 实现分享功能
      toast.info('分享功能开发中')
    },
    
    /**
     * 赐福心愿
     */
    blessWish() {
      navigation.navigateTo('/pages/guardian/bless-wish', { wishId: this.wishId })
    },
    
    /**
     * 编辑心愿
     */
    editWish() {
      this.showMoreModal = false
      navigation.navigateTo('/pages/wisher/edit-wish', { id: this.wishId })
    },
    
    /**
     * 删除心愿
     */
    deleteWish() {
      this.showMoreModal = false
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个心愿吗？删除后无法恢复。',
        confirmText: '删除',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            try {
              await this.wishStore.deleteWish(this.wishId)
              toast.success('删除成功')
              navigation.navigateBack()
            } catch (error) {
              console.error('删除失败:', error)
              toast.error('删除失败')
            }
          }
        }
      })
    },
    
    /**
     * 预览图片
     */
    previewImages(index) {
      uni.previewImage({
        urls: this.wishDetail.images,
        current: index
      })
    },
    
    previewImage(imageUrl) {
      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl
      })
    },
    
    /**
     * 滚动到评论区
     */
    scrollToComments() {
      uni.pageScrollTo({
        selector: '#comments-section',
        duration: 300
      })
    },
    
    /**
     * 显示更多操作
     */
    showMoreActions() {
      this.showMoreModal = true
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      return timeUtils.relativeTime(timestamp)
    },
    
    /**
     * 获取类型图标
     */
    getTypeIcon(type) {
      const typeMap = {
        personal: '/static/icons/personal-wish.png',
        family: '/static/icons/family-wish.png',
        career: '/static/icons/career-wish.png',
        health: '/static/icons/health-wish.png',
        other: '/static/icons/other-wish.png'
      }
      return typeMap[type] || '/static/icons/other-wish.png'
    },
    
    /**
     * 获取类型文本
     */
    getTypeText(type) {
      const typeMap = {
        personal: '个人',
        family: '家庭',
        career: '事业',
        health: '健康',
        other: '其他'
      }
      return typeMap[type] || '其他'
    },
    
    /**
     * 获取状态图标
     */
    getStatusIcon(status) {
      const statusMap = {
        pending: '/static/icons/pending.png',
        blessed: '/static/icons/blessed.png'
      }
      return statusMap[status] || '/static/icons/pending.png'
    },
    
    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        pending: '等待守护',
        blessed: '已赐福'
      }
      return statusMap[status] || '未知状态'
    },
    
    /**
     * 页面跳转
     */
    goBack() {
      navigation.navigateBack()
    },
    
    goToUserProfile() {
      if (!this.wishDetail.anonymous) {
        navigation.navigateTo('/pages/user/user-profile', { userId: this.wishDetail.user.id })
      }
    },
    
    goToBlessingDetail(blessingId) {
      navigation.navigateTo('/pages/guardian/blessing-detail', { id: blessingId })
    }
  }
}
</script>

<style lang="scss" scoped>
.wish-detail-page {
  min-height: 100vh;
  background-color: $wish-bg-primary;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $wish-spacing-md;
}

.navbar-back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
}

.navbar-title {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.navbar-action {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: $wish-spacing-xs;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 主要内容 */
.main-content {
  flex: 1;
  padding: $wish-spacing-md;
}

/* 心愿信息 */
.wish-info {
  margin-bottom: $wish-spacing-lg;
}

.wish-card {
  margin: 0;
}

.wish-content {
  padding: 0;
}

.wish-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-md;
}

.user-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-md;
}

.user-info {
  flex: 1;
}

.user-nickname {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.wish-time {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.wish-type {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.type-icon {
  width: 32rpx;
  height: 32rpx;
  margin-bottom: 4rpx;
}

.type-text {
  font-size: $wish-font-xs;
  color: $wish-text-secondary;
}

.wish-title {
  display: block;
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-text-primary;
  line-height: 1.4;
  margin-bottom: $wish-spacing-md;
}

.wish-desc {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-md;
}

.wish-images {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-sm;
  margin-bottom: $wish-spacing-md;
}

.wish-image {
  width: calc(50% - #{$wish-spacing-sm / 2});
  height: 300rpx;
  border-radius: $wish-radius-md;
}

.wish-tags {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-xs;
  margin-bottom: $wish-spacing-md;
}

.wish-tag {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
  background-color: rgba(232, 180, 160, 0.1);
  padding: $wish-spacing-xs $wish-spacing-sm;
  border-radius: $wish-radius-md;
}

.wish-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: $wish-spacing-md;
  border-top: 2rpx solid $wish-border-light;
}

.status-item {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: $wish-spacing-xs;
}

.status-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.blessed-info {
  display: flex;
  align-items: center;
}

.blessed-text {
  font-size: $wish-font-sm;
  color: $wish-color-success;
  font-weight: 500;
}

/* 互动数据 */
.interaction-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-lg;
  margin-bottom: $wish-spacing-lg;
  box-shadow: $wish-shadow-sm;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.stat-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: $wish-spacing-xs;
  transition: all 0.3s ease;

  &--active {
    filter: hue-rotate(320deg) saturate(1.5);
  }
}

.stat-text {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: 4rpx;
}

.stat-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

/* 赐福列表 */
.blessings-section {
  margin-bottom: $wish-spacing-lg;
}

.section-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-md;
}

.blessing-list {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.blessing-item {
  margin: 0;
}

.blessing-content {
  padding: 0;
}

.blessing-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-sm;
}

.guardian-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
}

.guardian-info {
  flex: 1;
}

.guardian-name {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: 4rpx;
}

.guardian-role {
  font-size: $wish-font-xs;
  color: $wish-color-secondary;
}

.blessing-time {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.blessing-text {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-primary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-sm;
}

.blessing-image {
  width: 100%;
  max-height: 400rpx;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
}

.blessing-actions {
  display: flex;
  justify-content: flex-end;
}

.thank-button {
  min-height: auto;
  padding: $wish-spacing-xs $wish-spacing-md;
}

/* 评论区域 */
.comments-section {
  margin-bottom: $wish-spacing-lg;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $wish-spacing-md;
}

.comment-sort {
  display: flex;
  align-items: center;
}

.sort-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin-right: $wish-spacing-xs;
}

.sort-icon {
  width: 16rpx;
  height: 16rpx;
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-md;
}

.comment-item {
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-md;
  box-shadow: $wish-shadow-sm;
}

.comment-main {
  display: flex;
  align-items: flex-start;
}

.comment-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-xs;
}

.comment-user {
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-right: $wish-spacing-sm;
}

.comment-time {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.comment-text {
  font-size: $wish-font-md;
  color: $wish-text-primary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-sm;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: $wish-spacing-lg;
}

.action-item {
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: $wish-spacing-xs;
}

.action-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

/* 回复列表 */
.reply-list {
  margin-top: $wish-spacing-md;
  padding-left: $wish-spacing-lg;
  border-left: 4rpx solid $wish-border-light;
}

.reply-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: $wish-spacing-md;

  &:last-child {
    margin-bottom: 0;
  }
}

.reply-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
}

.reply-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-xs;
}

.reply-user {
  font-size: $wish-font-sm;
  font-weight: 500;
  color: $wish-text-primary;
  margin-right: $wish-spacing-xs;
}

.reply-to {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
  margin-right: $wish-spacing-xs;
}

.reply-time {
  font-size: $wish-font-xs;
  color: $wish-text-secondary;
}

.reply-text {
  font-size: $wish-font-sm;
  color: $wish-text-primary;
  line-height: 1.6;
}

/* 加载更多评论 */
.load-more-comments {
  text-align: center;
  padding: $wish-spacing-lg;
}

.load-text {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
}

/* 底部评论输入框 */
.comment-input-bar {
  background-color: $wish-bg-secondary;
  border-top: 2rpx solid $wish-border-light;
  padding: $wish-spacing-md;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: $wish-spacing-sm;
}

.comment-input {
  flex: 1;
  margin-bottom: 0;
}

.send-button {
  min-height: auto;
  padding: $wish-spacing-sm $wish-spacing-md;
}

/* 评论模态框 */
.comment-modal {
  padding: $wish-spacing-md 0;
}

.modal-input {
  margin-bottom: 0;
}

.comment-actions {
  display: flex;
  gap: $wish-spacing-md;
}

.comment-button {
  flex: 1;
}

/* 排序选择模态框 */
.sort-options {
  padding: $wish-spacing-md 0;
}

.sort-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
  transition: background-color 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &--active {
    background-color: rgba(232, 180, 160, 0.1);
  }

  &:active {
    background-color: $wish-bg-primary;
  }
}

.sort-option-text {
  font-size: $wish-font-md;
  color: $wish-text-primary;
}

.sort-check {
  width: 24rpx;
  height: 24rpx;
}

/* 更多操作模态框 */
.more-actions {
  padding: $wish-spacing-md 0;
}

.action-option {
  display: flex;
  align-items: center;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
  transition: background-color 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    background-color: $wish-bg-primary;
  }
}

.action-option-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: $wish-spacing-md;
}

.action-option-text {
  font-size: $wish-font-md;
  color: $wish-text-primary;
}

/* 页面加载 */
.page-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}
</style>
