<template>
  <view class="proxy-test-page">
    <view class="header">
      <text class="title">代理配置测试</text>
      <text class="subtitle">测试Vite代理是否正常工作</text>
    </view>
    
    <view class="config-section">
      <view class="config-title">当前配置</view>
      <view class="config-item">
        <text class="label">环境:</text>
        <text class="value">{{ environment }}</text>
      </view>
      <view class="config-item">
        <text class="label">使用代理:</text>
        <text class="value">{{ useProxy ? '是' : '否' }}</text>
      </view>
      <view class="config-item">
        <text class="label">API基础URL:</text>
        <text class="value">{{ baseURL }}</text>
      </view>
      <view class="config-item">
        <text class="label">前端地址:</text>
        <text class="value">{{ frontendURL }}</text>
      </view>
    </view>
    
    <view class="test-section">
      <button class="test-btn" @click="runProxyTest" :disabled="testing">
        {{ testing ? '测试中...' : '开始代理测试' }}
      </button>
      
      <button class="performance-btn" @click="runPerformanceTest" :disabled="testing">
        {{ testing ? '测试中...' : '性能对比' }}
      </button>
      
      <button class="clear-btn" @click="clearResults" :disabled="testing">
        清除结果
      </button>
    </view>
    
    <view class="results-section" v-if="testReport">
      <view class="summary">
        <text class="summary-title">测试摘要</text>
        <view class="summary-stats">
          <text class="stat-item">总计: {{ testReport.summary.total }}</text>
          <text class="stat-item">成功: {{ testReport.summary.successful }}</text>
          <text class="stat-item">失败: {{ testReport.summary.failed }}</text>
          <text class="stat-item">成功率: {{ testReport.summary.successRate }}</text>
        </view>
        <view class="proxy-status" :class="{ working: testReport.summary.proxyWorking, failed: !testReport.summary.proxyWorking }">
          <text>代理状态: {{ testReport.summary.proxyWorking ? '✅ 正常工作' : '❌ 可能有问题' }}</text>
        </view>
      </view>
      
      <view class="results-list">
        <view 
          class="result-item" 
          v-for="(test, index) in testReport.tests" 
          :key="index"
          :class="{ success: test.result.success, failed: !test.result.success, auth: test.result.needsAuth }"
        >
          <view class="result-header">
            <text class="result-name">{{ test.name }}</text>
            <text class="result-status">
              {{ test.result.success ? '✅' : '❌' }}
              {{ test.result.needsAuth ? '🔐' : '' }}
            </text>
          </view>
          <view class="result-details" v-if="!test.result.success">
            <text class="error-text">{{ test.result.error }}</text>
          </view>
          <view class="result-details" v-else>
            <text class="success-text">
              {{ test.result.needsAuth ? '需要认证 (代理正常)' : '请求成功' }}
              {{ test.result.statusCode ? ` - HTTP ${test.result.statusCode}` : '' }}
            </text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="logs-section" v-if="logs.length > 0">
      <text class="logs-title">测试日志</text>
      <view class="logs-list">
        <text 
          class="log-item" 
          v-for="(log, index) in logs" 
          :key="index"
        >
          {{ log }}
        </text>
      </view>
    </view>
    
    <view class="help-section">
      <text class="help-title">代理配置说明</text>
      <view class="help-item">
        <text class="help-question">✅ 代理的优势</text>
        <text class="help-answer">• 避免CORS跨域问题<br/>• 开发环境更接近生产环境<br/>• 统一的API调用方式</text>
      </view>
      <view class="help-item">
        <text class="help-question">⚙️ 代理配置位置</text>
        <text class="help-answer">vite.config.js 中的 server.proxy 配置</text>
      </view>
      <view class="help-item">
        <text class="help-question">🔧 如果代理失败</text>
        <text class="help-answer">• 检查后端服务是否启动<br/>• 检查代理目标地址是否正确<br/>• 重启前端开发服务器</text>
      </view>
    </view>
  </view>
</template>

<script>
import { APP_CONFIG } from '@/config'
import { quickProxyTest, compareProxyPerformance } from '@/utils/proxy-test'

export default {
  name: 'ProxyTest',
  data() {
    return {
      testing: false,
      testReport: null,
      logs: [],
      environment: process.env.NODE_ENV || 'development',
      useProxy: APP_CONFIG.useProxy,
      baseURL: APP_CONFIG.baseURL,
      frontendURL: ''
    }
  },
  
  mounted() {
    this.frontendURL = window.location.origin
  },
  
  methods: {
    async runProxyTest() {
      this.testing = true
      this.logs = []
      this.testReport = null
      
      try {
        // 重写console.log来捕获日志
        const originalLog = console.log
        console.log = (...args) => {
          this.logs.push(args.join(' '))
          originalLog.apply(console, args)
        }
        
        // 运行代理测试
        this.testReport = await quickProxyTest()
        
        // 恢复console.log
        console.log = originalLog
        
      } catch (error) {
        console.error('代理测试失败:', error)
        uni.showToast({
          title: '代理测试失败',
          icon: 'error'
        })
      } finally {
        this.testing = false
      }
    },
    
    async runPerformanceTest() {
      this.testing = true
      
      try {
        // 重写console.log来捕获日志
        const originalLog = console.log
        console.log = (...args) => {
          this.logs.push(args.join(' '))
          originalLog.apply(console, args)
        }
        
        // 运行性能对比测试
        await compareProxyPerformance()
        
        // 恢复console.log
        console.log = originalLog
        
      } catch (error) {
        console.error('性能测试失败:', error)
        uni.showToast({
          title: '性能测试失败',
          icon: 'error'
        })
      } finally {
        this.testing = false
      }
    },
    
    clearResults() {
      this.testReport = null
      this.logs = []
    }
  }
}
</script>

<style lang="scss" scoped>
.proxy-test-page {
  padding: $wish-spacing-lg;
  background-color: $wish-bg-primary;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: $wish-spacing-lg;
  
  .title {
    font-size: $wish-font-xl;
    font-weight: 600;
    color: $wish-text-primary;
    display: block;
    margin-bottom: $wish-spacing-xs;
  }
  
  .subtitle {
    font-size: $wish-font-sm;
    color: $wish-text-secondary;
  }
}

.config-section {
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-lg;
  
  .config-title {
    font-size: $wish-font-lg;
    font-weight: 600;
    color: $wish-text-primary;
    margin-bottom: $wish-spacing-md;
  }
  
  .config-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: $wish-spacing-sm;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      font-weight: 500;
      color: $wish-text-primary;
    }
    
    .value {
      color: $wish-text-secondary;
      font-family: monospace;
      font-size: $wish-font-sm;
    }
  }
}

.test-section {
  display: flex;
  gap: $wish-spacing-sm;
  margin-bottom: $wish-spacing-lg;
  
  .test-btn, .performance-btn, .clear-btn {
    flex: 1;
    padding: $wish-spacing-md;
    border-radius: $wish-radius-md;
    border: none;
    font-size: $wish-font-sm;
    font-weight: 500;
  }
  
  .test-btn {
    background-color: $wish-primary;
    color: white;
    
    &:disabled {
      background-color: $wish-text-muted;
    }
  }
  
  .performance-btn {
    background-color: $wish-warning;
    color: white;
    
    &:disabled {
      background-color: $wish-text-muted;
    }
  }
  
  .clear-btn {
    background-color: $wish-bg-secondary;
    color: $wish-text-primary;
  }
}

.results-section {
  margin-bottom: $wish-spacing-lg;
}

.summary {
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-md;
  
  .summary-title {
    font-size: $wish-font-lg;
    font-weight: 600;
    color: $wish-text-primary;
    display: block;
    margin-bottom: $wish-spacing-sm;
  }
  
  .summary-stats {
    display: flex;
    gap: $wish-spacing-md;
    flex-wrap: wrap;
    margin-bottom: $wish-spacing-sm;
    
    .stat-item {
      font-size: $wish-font-sm;
      color: $wish-text-secondary;
    }
  }
  
  .proxy-status {
    font-weight: 500;
    
    &.working {
      color: $wish-success;
    }
    
    &.failed {
      color: $wish-error;
    }
  }
}

.results-list {
  .result-item {
    background-color: $wish-bg-secondary;
    padding: $wish-spacing-md;
    border-radius: $wish-radius-md;
    margin-bottom: $wish-spacing-sm;
    border-left: 4px solid transparent;
    
    &.success {
      border-left-color: $wish-success;
    }
    
    &.failed {
      border-left-color: $wish-error;
    }
    
    &.auth {
      border-left-color: $wish-warning;
    }
  }
  
  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $wish-spacing-xs;
    
    .result-name {
      font-weight: 500;
      color: $wish-text-primary;
    }
    
    .result-status {
      font-size: $wish-font-lg;
    }
  }
  
  .result-details {
    .error-text {
      color: $wish-error;
      font-size: $wish-font-sm;
    }
    
    .success-text {
      color: $wish-success;
      font-size: $wish-font-sm;
    }
  }
}

.logs-section {
  margin-bottom: $wish-spacing-lg;
  
  .logs-title {
    font-size: $wish-font-lg;
    font-weight: 600;
    color: $wish-text-primary;
    display: block;
    margin-bottom: $wish-spacing-sm;
  }
  
  .logs-list {
    background-color: #1a1a1a;
    padding: $wish-spacing-md;
    border-radius: $wish-radius-md;
    max-height: 400rpx;
    overflow-y: auto;
    
    .log-item {
      display: block;
      color: #00ff00;
      font-family: monospace;
      font-size: $wish-font-sm;
      line-height: 1.4;
      margin-bottom: $wish-spacing-xs;
    }
  }
}

.help-section {
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  
  .help-title {
    font-size: $wish-font-lg;
    font-weight: 600;
    color: $wish-text-primary;
    display: block;
    margin-bottom: $wish-spacing-md;
  }
  
  .help-item {
    margin-bottom: $wish-spacing-md;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .help-question {
      font-weight: 500;
      color: $wish-text-primary;
      display: block;
      margin-bottom: $wish-spacing-xs;
    }
    
    .help-answer {
      color: $wish-text-secondary;
      font-size: $wish-font-sm;
      line-height: 1.4;
    }
  }
}
</style>
