<template>
  <view class="api-test-page">
    <view class="header">
      <text class="title">API连接测试</text>
    </view>
    
    <view class="test-section">
      <button class="test-btn" @click="testConnection" :disabled="testing">
        {{ testing ? '测试中...' : '开始测试' }}
      </button>
      
      <button class="clear-btn" @click="clearResults" :disabled="testing">
        清除结果
      </button>
    </view>
    
    <view class="results-section" v-if="testResults.length > 0">
      <view class="summary">
        <text class="summary-title">测试摘要</text>
        <view class="summary-stats">
          <text>总计: {{ summary.total }}</text>
          <text>成功: {{ summary.successful }}</text>
          <text>失败: {{ summary.failed }}</text>
          <text>成功率: {{ summary.successRate }}</text>
        </view>
      </view>
      
      <view class="results-list">
        <view 
          class="result-item" 
          v-for="(result, index) in testResults" 
          :key="index"
          :class="{ success: result.success, failed: !result.success }"
        >
          <view class="result-header">
            <text class="result-name">{{ result.name }}</text>
            <text class="result-status">{{ result.success ? '✅' : '❌' }}</text>
          </view>
          <view class="result-details" v-if="!result.success">
            <text class="error-text">{{ result.error }}</text>
          </view>
          <view class="result-details" v-else>
            <text class="success-text">响应时间: {{ result.responseTime }}ms</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="logs-section" v-if="logs.length > 0">
      <text class="logs-title">测试日志</text>
      <view class="logs-list">
        <text 
          class="log-item" 
          v-for="(log, index) in logs" 
          :key="index"
        >
          {{ log }}
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import { apiTester } from '@/utils/api-test'

export default {
  name: 'APITest',
  data() {
    return {
      testing: false,
      testResults: [],
      logs: [],
      summary: {
        total: 0,
        successful: 0,
        failed: 0,
        successRate: '0%'
      }
    }
  },
  
  methods: {
    async testConnection() {
      this.testing = true
      this.logs = []
      this.testResults = []
      
      try {
        // 重写console.log来捕获日志
        const originalLog = console.log
        console.log = (...args) => {
          this.logs.push(args.join(' '))
          originalLog.apply(console, args)
        }
        
        // 运行测试
        const report = await apiTester.testBackendConnection()
        
        // 恢复console.log
        console.log = originalLog
        
        // 更新结果
        this.testResults = report.results
        this.summary = report.summary
        
      } catch (error) {
        console.error('测试失败:', error)
        uni.showToast({
          title: '测试失败',
          icon: 'error'
        })
      } finally {
        this.testing = false
      }
    },
    
    clearResults() {
      this.testResults = []
      this.logs = []
      this.summary = {
        total: 0,
        successful: 0,
        failed: 0,
        successRate: '0%'
      }
      apiTester.clear()
    }
  }
}
</script>

<style lang="scss" scoped>
.api-test-page {
  padding: $wish-spacing-lg;
  background-color: $wish-bg-primary;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: $wish-spacing-lg;
  
  .title {
    font-size: $wish-font-xl;
    font-weight: 600;
    color: $wish-text-primary;
  }
}

.test-section {
  display: flex;
  gap: $wish-spacing-md;
  margin-bottom: $wish-spacing-lg;
  
  .test-btn, .clear-btn {
    flex: 1;
    padding: $wish-spacing-md;
    border-radius: $wish-radius-md;
    border: none;
    font-size: $wish-font-md;
    font-weight: 500;
  }
  
  .test-btn {
    background-color: $wish-primary;
    color: white;
    
    &:disabled {
      background-color: $wish-text-muted;
    }
  }
  
  .clear-btn {
    background-color: $wish-bg-secondary;
    color: $wish-text-primary;
  }
}

.results-section {
  margin-bottom: $wish-spacing-lg;
}

.summary {
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-md;
  
  .summary-title {
    font-size: $wish-font-lg;
    font-weight: 600;
    color: $wish-text-primary;
    display: block;
    margin-bottom: $wish-spacing-sm;
  }
  
  .summary-stats {
    display: flex;
    gap: $wish-spacing-md;
    flex-wrap: wrap;
    
    text {
      font-size: $wish-font-sm;
      color: $wish-text-secondary;
    }
  }
}

.results-list {
  .result-item {
    background-color: $wish-bg-secondary;
    padding: $wish-spacing-md;
    border-radius: $wish-radius-md;
    margin-bottom: $wish-spacing-sm;
    border-left: 4px solid transparent;
    
    &.success {
      border-left-color: $wish-success;
    }
    
    &.failed {
      border-left-color: $wish-error;
    }
  }
  
  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $wish-spacing-xs;
    
    .result-name {
      font-weight: 500;
      color: $wish-text-primary;
    }
    
    .result-status {
      font-size: $wish-font-lg;
    }
  }
  
  .result-details {
    .error-text {
      color: $wish-error;
      font-size: $wish-font-sm;
    }
    
    .success-text {
      color: $wish-success;
      font-size: $wish-font-sm;
    }
  }
}

.logs-section {
  .logs-title {
    font-size: $wish-font-lg;
    font-weight: 600;
    color: $wish-text-primary;
    display: block;
    margin-bottom: $wish-spacing-sm;
  }
  
  .logs-list {
    background-color: #1a1a1a;
    padding: $wish-spacing-md;
    border-radius: $wish-radius-md;
    max-height: 400rpx;
    overflow-y: auto;
    
    .log-item {
      display: block;
      color: #00ff00;
      font-family: monospace;
      font-size: $wish-font-sm;
      line-height: 1.4;
      margin-bottom: $wish-spacing-xs;
    }
  }
}
</style>
