/**
 * 心愿路由
 * 处理心愿相关的API请求
 */

const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const { getModel } = require('../models');
const authMiddleware = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Cache } = require('../utils/redis');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * 获取心愿列表
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('type').optional().isIn(['health', 'career', 'love', 'family', 'study', 'wealth', 'travel', 'other']),
  query('category').optional().isIn(['personal', 'family', 'friends', 'society', 'world']),
  query('sortBy').optional().isIn(['createdAt', 'likes', 'blessings', 'views']),
  query('sortOrder').optional().isIn(['asc', 'desc'])
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const {
    page = 1,
    limit = 20,
    type,
    category,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    keyword
  } = req.query;

  // 生成缓存键
  const cacheKey = `wishes:list:${JSON.stringify(req.query)}`;
  
  // 尝试从缓存获取
  let wishes = await Cache.get(cacheKey);

  if (!wishes) {
    const Wish = getModel('Wish');
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      type,
      category,
      sortBy,
      sortOrder: sortOrder === 'asc' ? 1 : -1
    };

    if (keyword) {
      wishes = await Wish.searchWishes(keyword, options);
    } else {
      wishes = await Wish.getPublicWishes(options);
    }

    // 缓存结果
    await Cache.set(cacheKey, wishes, 180); // 3分钟缓存
  }

  res.json({
    wishes,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: wishes.length
    }
  });
}));

/**
 * 获取推荐心愿
 */
router.get('/recommended', authMiddleware.optional, catchAsync(async (req, res) => {
  const { limit = 20 } = req.query;
  const userId = req.user?.userId;

  const cacheKey = `wishes:recommended:${userId || 'anonymous'}:${limit}`;
  
  let wishes = await Cache.get(cacheKey);

  if (!wishes) {
    const Wish = getModel('Wish');
    wishes = await Wish.getRecommendedWishes(userId, parseInt(limit));
    await Cache.set(cacheKey, wishes, 300); // 5分钟缓存
  }

  res.json({
    wishes
  });
}));

/**
 * 获取心愿详情
 */
router.get('/:id', [
  param('id').isMongoId().withMessage('无效的心愿ID')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const { id } = req.params;
  const Wish = getModel('Wish');

  const wish = await Wish.findByPk(id, {
    include: [{
      model: getModel('User'),
      as: 'creator',
      attributes: ['id', 'username', 'nickname', 'avatar', 'level']
    }]
  });

  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  // 检查访问权限
  if (wish.visibility === 'private' && 
      (!req.user || wish.creator._id.toString() !== req.user.userId.toString())) {
    throw new AppError('无权访问此心愿', 403, 'ACCESS_DENIED');
  }

  // 增加浏览量（异步执行，不影响响应）
  wish.addView().catch(error => {
    logger.error('增加浏览量失败:', error);
  });

  res.json({
    wish
  });
}));

/**
 * 创建心愿
 */
router.post('/', authMiddleware.required, [
  body('title').trim().isLength({ min: 1, max: 100 }).withMessage('标题长度必须在1-100字符之间'),
  body('content').trim().isLength({ min: 1, max: 1000 }).withMessage('内容长度必须在1-1000字符之间'),
  body('type').isIn(['health', 'career', 'love', 'family', 'study', 'wealth', 'travel', 'other']).withMessage('无效的心愿类型'),
  body('category').optional().isIn(['personal', 'family', 'friends', 'society', 'world']),
  body('visibility').optional().isIn(['public', 'friends', 'private']),
  body('tags').optional().isArray().withMessage('标签必须是数组'),
  body('deadline').optional().isISO8601().withMessage('截止日期格式无效')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const {
    title,
    content,
    type,
    category = 'personal',
    visibility = 'public',
    tags = [],
    images = [],
    deadline,
    location
  } = req.body;

  // 创建心愿
  const Wish = getModel('Wish');
  const wish = await Wish.create({
    title,
    content,
    type,
    category,
    visibility,
    tags: tags.slice(0, 5), // 最多5个标签
    images: images.slice(0, 3), // 最多3张图片
    deadline: deadline ? new Date(deadline) : undefined,
    province: location?.province || '',
    city: location?.city || '',
    district: location?.district || '',
    creator_id: req.user.userId,
    source: req.get('User-Agent')?.includes('MiniProgram') ? 'miniprogram' : 'mobile',
    ip: req.ip,
    user_agent: req.get('User-Agent')
  });

  // 更新用户统计
  const User = getModel('User');
  await User.increment('wishes_created', { where: { id: req.user.userId } });

  // 奖励心愿力
  const user = await User.findByPk(req.user.userId);
  await user.addPoints('wishPower', 5);

  // 清除相关缓存
  await Cache.delPattern('wishes:list:*');
  await Cache.delPattern('wishes:recommended:*');

  logger.info(`用户 ${req.user.username} 创建心愿: ${title}`);

  res.status(201).json({
    message: '心愿创建成功',
    wish: {
      id: wish._id,
      title: wish.title,
      type: wish.type,
      createdAt: wish.createdAt
    },
    reward: {
      wishPower: 5
    }
  });
}));

/**
 * 更新心愿
 */
router.put('/:id', authMiddleware.required, [
  param('id').isMongoId().withMessage('无效的心愿ID'),
  body('title').optional().trim().isLength({ min: 1, max: 100 }),
  body('content').optional().trim().isLength({ min: 1, max: 1000 }),
  body('visibility').optional().isIn(['public', 'friends', 'private']),
  body('tags').optional().isArray(),
  body('deadline').optional().isISO8601()
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const { id } = req.params;
  const updates = req.body;
  const Wish = getModel('Wish');

  const wish = await Wish.findByPk(id);

  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  // 检查权限
  if (wish.creator_id !== req.user.userId &&
      !req.user.roles.includes('admin')) {
    throw new AppError('无权修改此心愿', 403, 'ACCESS_DENIED');
  }

  // 更新心愿
  await wish.update(updates);

  // 清除相关缓存
  await Cache.delPattern('wishes:list:*');
  await Cache.del(`wish:${id}`);

  logger.info(`用户 ${req.user.username} 更新心愿: ${id}`);

  res.json({
    message: '心愿更新成功',
    wish
  });
}));

/**
 * 删除心愿
 */
router.delete('/:id', authMiddleware.required, [
  param('id').isMongoId().withMessage('无效的心愿ID')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const { id } = req.params;
  const Wish = getModel('Wish');

  const wish = await Wish.findByPk(id);

  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  // 检查权限
  if (wish.creator_id !== req.user.userId &&
      !req.user.roles.includes('admin')) {
    throw new AppError('无权删除此心愿', 403, 'ACCESS_DENIED');
  }

  // 软删除
  await wish.update({ status: 'deleted' });

  // 清除相关缓存
  await Cache.delPattern('wishes:list:*');
  await Cache.del(`wish:${id}`);

  logger.info(`用户 ${req.user.username} 删除心愿: ${id}`);

  res.json({
    message: '心愿删除成功'
  });
}));

/**
 * 点赞/取消点赞心愿
 */
router.post('/:id/like', authMiddleware.required, [
  param('id').isMongoId().withMessage('无效的心愿ID')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const { id } = req.params;
  const Wish = getModel('Wish');

  const wish = await Wish.findByPk(id);

  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  const result = await wish.toggleLike(req.user.userId);

  // 清除相关缓存
  await Cache.del(`wish:${id}`);

  res.json({
    message: result.action === 'liked' ? '点赞成功' : '取消点赞成功',
    action: result.action,
    likesCount: result.count
  });
}));

module.exports = router;
