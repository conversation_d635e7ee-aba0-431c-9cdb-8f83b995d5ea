/**
 * 心愿路由
 * 处理心愿相关的API请求
 */

const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const { getModel } = require('../models');
const authMiddleware = require('../middleware/auth');
const { AppError, catchAsync, handleValidationErrors } = require('../middleware/errorHandler');
const { Cache } = require('../utils/redis');
const logger = require('../utils/logger');
const config = require('../config');

const router = express.Router();

/**
 * 心愿模块根路由
 */
router.get('/', authMiddleware.optional, (req, res) => {
  res.json({
    module: '心愿模块',
    endpoints: {
      list: 'GET /api/wishes',
      recommended: 'GET /api/wishes/recommended',
      detail: 'GET /api/wishes/:id',
      create: 'POST /api/wishes',
      update: 'PUT /api/wishes/:id',
      delete: 'DELETE /api/wishes/:id',
      like: 'POST /api/wishes/:id/like'
    }
  });
});

/**
 * 获取心愿列表
 */
router.get('/', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('type').optional().isIn(['health', 'career', 'love', 'family', 'study', 'wealth', 'travel', 'other']),
  query('category').optional().isIn(['personal', 'family', 'friends', 'society', 'world']),
  query('sortBy').optional().isIn(['created_at', 'likes', 'blessings', 'views']),
  query('sortOrder').optional().isIn(['asc', 'desc']),
  query('keyword').optional().isLength({ min: 1, max: 50 }).withMessage('搜索关键词长度1-50字符')
], handleValidationErrors, authMiddleware.optional, catchAsync(async (req, res) => {
  const {
    page = 1,
    limit = 20,
    type,
    category,
    sortBy = 'created_at',
    sortOrder = 'desc',
    keyword
  } = req.query;

  // 构建缓存键
  const cacheKey = `wishes:list:${JSON.stringify(req.query)}`;
  
  // 尝试从缓存获取
  let result = await Cache.get(cacheKey);
  
  if (!result) {
    const Wish = getModel('Wish');
    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      type,
      category,
      sortBy,
      sortOrder: sortOrder.toUpperCase()
    };

    if (keyword) {
      result = await Wish.searchWishes(keyword, options);
    } else {
      result = await Wish.getPublicWishes(options);
    }

    // 缓存结果
    await Cache.set(cacheKey, result, config.cache.ttl.wish);
  }

  res.json({
    wishes: result.rows || result,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: result.count || result.length,
      pages: Math.ceil((result.count || result.length) / limit)
    }
  });
}));

/**
 * 获取推荐心愿
 */
router.get('/recommended', [
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('数量限制必须在1-50之间')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { limit = 20 } = req.query;
  const userId = req.user.userId;

  const cacheKey = `wishes:recommended:${userId}:${limit}`;
  let wishes = await Cache.get(cacheKey);
  
  if (!wishes) {
    const Wish = getModel('Wish');
    wishes = await Wish.getRecommendedWishes(userId, parseInt(limit));
    await Cache.set(cacheKey, wishes, 300); // 5分钟缓存
  }

  res.json({
    wishes
  });
}));

/**
 * 获取心愿详情
 */
router.get('/:id', [
  param('id').isInt().withMessage('心愿ID必须是整数')
], handleValidationErrors, authMiddleware.optional, catchAsync(async (req, res) => {
  const { id } = req.params;
  const Wish = getModel('Wish');
  
  const wish = await Wish.findByPk(id, {
    include: [{
      model: getModel('User'),
      as: 'creator',
      attributes: ['id', 'username', 'nickname', 'avatar', 'level']
    }]
  });

  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  // 检查可见性权限
  if (wish.visibility === 'private' && 
      (!req.user || req.user.userId !== wish.creator_id)) {
    throw new AppError('无权查看此心愿', 403, 'ACCESS_DENIED');
  }

  // 增加浏览量
  await wish.addView();

  res.json({
    wish
  });
}));

/**
 * 创建心愿
 */
router.post('/', [
  body('title')
    .isLength({ min: 1, max: 100 })
    .withMessage('心愿标题长度必须在1-100字符之间'),
  body('content')
    .isLength({ min: 1, max: 1000 })
    .withMessage('心愿内容长度必须在1-1000字符之间'),
  body('type')
    .isIn(['health', 'career', 'love', 'family', 'study', 'wealth', 'travel', 'other'])
    .withMessage('心愿类型无效'),
  body('category')
    .optional()
    .isIn(['personal', 'family', 'friends', 'society', 'world'])
    .withMessage('心愿分类无效'),
  body('visibility')
    .optional()
    .isIn(['public', 'friends', 'private'])
    .withMessage('可见性设置无效'),
  body('tags')
    .optional()
    .isArray({ max: 5 })
    .withMessage('标签最多5个'),
  body('images')
    .optional()
    .isArray({ max: 3 })
    .withMessage('图片最多3张'),
  body('deadline')
    .optional()
    .isISO8601()
    .withMessage('截止日期格式无效'),
  body('location.province').optional().isLength({ max: 50 }),
  body('location.city').optional().isLength({ max: 50 }),
  body('location.district').optional().isLength({ max: 50 })
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const {
    title,
    content,
    type,
    category = 'personal',
    visibility = 'public',
    tags = [],
    images = [],
    deadline,
    location = {}
  } = req.body;

  // 创建心愿
  const Wish = getModel('Wish');
  const wish = await Wish.create({
    title,
    content,
    type,
    category,
    visibility,
    tags: tags.slice(0, 5), // 最多5个标签
    images: images.slice(0, 3), // 最多3张图片
    deadline: deadline ? new Date(deadline) : null,
    province: location.province || '',
    city: location.city || '',
    district: location.district || '',
    creator_id: req.user.userId,
    source: req.get('User-Agent')?.includes('MiniProgram') ? 'miniprogram' : 'mobile',
    ip: req.ip,
    user_agent: req.get('User-Agent')
  });

  // 更新用户统计
  const User = getModel('User');
  await User.increment('wishes_created', { where: { id: req.user.userId } });

  // 奖励心愿力
  const user = await User.findByPk(req.user.userId);
  await user.addPoints('wishPower', config.business.wishPower.shareWish);

  // 清除相关缓存
  await Cache.delPattern('wishes:*');

  logger.info(`用户 ${req.user.username} 创建心愿: ${title}`);

  res.status(201).json({
    message: '心愿创建成功',
    wish: {
      id: wish.id,
      title: wish.title,
      type: wish.type,
      category: wish.category,
      visibility: wish.visibility
    },
    reward: {
      wishPower: config.business.wishPower.shareWish
    }
  });
}));

/**
 * 更新心愿
 */
router.put('/:id', [
  param('id').isInt().withMessage('心愿ID必须是整数'),
  body('title').optional().isLength({ min: 1, max: 100 }),
  body('content').optional().isLength({ min: 1, max: 1000 }),
  body('type').optional().isIn(['health', 'career', 'love', 'family', 'study', 'wealth', 'travel', 'other']),
  body('category').optional().isIn(['personal', 'family', 'friends', 'society', 'world']),
  body('visibility').optional().isIn(['public', 'friends', 'private']),
  body('tags').optional().isArray({ max: 5 }),
  body('images').optional().isArray({ max: 3 }),
  body('deadline').optional().isISO8601()
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { id } = req.params;
  const updates = req.body;
  const Wish = getModel('Wish');

  const wish = await Wish.findByPk(id);
  
  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  // 检查权限
  if (wish.creator_id !== req.user.userId && 
      !req.user.roles.includes('admin')) {
    throw new AppError('无权修改此心愿', 403, 'ACCESS_DENIED');
  }

  // 更新心愿
  await wish.update(updates);

  // 清除相关缓存
  await Cache.delPattern('wishes:*');

  logger.info(`用户 ${req.user.username} 更新心愿: ${wish.title}`);

  res.json({
    message: '心愿更新成功',
    wish
  });
}));

/**
 * 删除心愿
 */
router.delete('/:id', [
  param('id').isInt().withMessage('心愿ID必须是整数')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { id } = req.params;
  const Wish = getModel('Wish');

  const wish = await Wish.findByPk(id);
  
  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  // 检查权限
  if (wish.creator_id !== req.user.userId && 
      !req.user.roles.includes('admin')) {
    throw new AppError('无权删除此心愿', 403, 'ACCESS_DENIED');
  }

  // 软删除
  await wish.update({ status: 'deleted' });

  // 清除相关缓存
  await Cache.delPattern('wishes:*');

  logger.info(`用户 ${req.user.username} 删除心愿: ${wish.title}`);

  res.json({
    message: '心愿删除成功'
  });
}));

/**
 * 点赞/取消点赞心愿
 */
router.post('/:id/like', [
  param('id').isInt().withMessage('心愿ID必须是整数')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { id } = req.params;
  const Wish = getModel('Wish');

  const wish = await Wish.findByPk(id);
  
  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  const result = await wish.toggleLike(req.user.userId);

  // 清除相关缓存
  await Cache.delPattern('wishes:*');

  res.json({
    message: result.action === 'liked' ? '点赞成功' : '取消点赞成功',
    action: result.action,
    count: result.count
  });
}));

module.exports = router;
