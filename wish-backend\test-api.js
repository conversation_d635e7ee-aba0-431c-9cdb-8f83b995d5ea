/**
 * API测试脚本
 * 用于测试后端API是否正常工作
 */

const http = require('http');

function testEndpoint(path, method = 'GET') {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: result
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: data
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function runTests() {
  console.log('🚀 开始测试愿境后端API...\n');

  const endpoints = [
    '/health',
    '/api',
    '/api/auth',
    '/api/users',
    '/api/wishes',
    '/api/guardian',
    '/api/social',
    '/api/chat',
    '/api/upload'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`测试 ${endpoint}...`);
      const result = await testEndpoint(endpoint);
      
      if (result.status === 200) {
        console.log(`✅ ${endpoint} - 状态: ${result.status}`);
        if (typeof result.data === 'object') {
          console.log(`   响应: ${JSON.stringify(result.data, null, 2).substring(0, 200)}...`);
        }
      } else {
        console.log(`❌ ${endpoint} - 状态: ${result.status}`);
        console.log(`   错误: ${JSON.stringify(result.data, null, 2)}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint} - 连接错误: ${error.message}`);
    }
    console.log('');
  }

  console.log('✨ API测试完成！');
}

// 运行测试
runTests().catch(console.error);
