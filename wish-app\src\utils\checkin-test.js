/**
 * 签到功能测试工具
 * 用于测试签到状态和签到功能
 */

import { systemAPI } from '@/api'
import { APP_CONFIG } from '@/config'

export class CheckinTest {
  constructor() {
    this.baseURL = APP_CONFIG.baseURL
  }

  // 测试获取签到状态
  async testGetCheckinStatus() {
    try {
      console.log('🧪 测试获取签到状态...')
      
      const response = await systemAPI.getCheckInStatus()
      
      console.log('✅ 获取签到状态成功:', response)
      return { 
        success: true, 
        data: response,
        message: '获取签到状态成功'
      }
    } catch (error) {
      console.log('❌ 获取签到状态失败:', error.message)
      return { 
        success: false, 
        error: error.message,
        needsAuth: error.message.includes('认证') || error.message.includes('登录')
      }
    }
  }

  // 测试每日签到
  async testDailyCheckin() {
    try {
      console.log('🧪 测试每日签到...')
      
      const response = await systemAPI.dailyCheckIn()
      
      console.log('✅ 每日签到成功:', response)
      return { 
        success: true, 
        data: response,
        message: '每日签到成功'
      }
    } catch (error) {
      console.log('❌ 每日签到失败:', error.message)
      return { 
        success: false, 
        error: error.message,
        alreadyCheckedIn: error.message.includes('已签到'),
        needsAuth: error.message.includes('认证') || error.message.includes('登录')
      }
    }
  }

  // 测试签到流程
  async testCheckinFlow() {
    console.log('🚀 开始测试签到功能...\n')
    
    const results = {
      getStatus: await this.testGetCheckinStatus(),
      checkin: await this.testDailyCheckin(),
      getStatusAfter: null
    }
    
    // 如果签到成功，再次获取状态验证
    if (results.checkin.success) {
      console.log('\n🔄 签到后重新获取状态...')
      results.getStatusAfter = await this.testGetCheckinStatus()
    }
    
    return this.generateReport(results)
  }

  // 生成测试报告
  generateReport(results) {
    const tests = [
      { name: '获取签到状态', result: results.getStatus },
      { name: '每日签到', result: results.checkin }
    ]
    
    if (results.getStatusAfter) {
      tests.push({ name: '签到后状态', result: results.getStatusAfter })
    }
    
    const successful = tests.filter(test => test.result.success).length
    const total = tests.length
    const successRate = ((successful / total) * 100).toFixed(1)
    
    const report = {
      summary: {
        total,
        successful,
        failed: total - successful,
        successRate: successRate + '%',
        checkinWorking: results.checkin.success || results.checkin.alreadyCheckedIn
      },
      tests,
      details: {
        needsAuth: tests.some(test => test.result.needsAuth),
        alreadyCheckedIn: results.checkin.alreadyCheckedIn,
        statusBefore: results.getStatus.data,
        statusAfter: results.getStatusAfter?.data
      },
      timestamp: new Date().toISOString()
    }

    console.log('\n📊 签到功能测试报告:')
    console.log(`总计: ${total} | 成功: ${successful} | 失败: ${total - successful} | 成功率: ${report.summary.successRate}`)
    console.log(`签到功能: ${report.summary.checkinWorking ? '✅ 正常工作' : '❌ 可能有问题'}`)
    
    if (report.details.needsAuth) {
      console.log('🔐 需要用户认证才能使用签到功能')
    }
    
    if (report.details.alreadyCheckedIn) {
      console.log('ℹ️  今日已签到，这是正常的')
    }
    
    if (report.summary.failed > 0) {
      console.log('\n❌ 失败的测试:')
      tests.filter(test => !test.result.success).forEach(test => {
        console.log(`  - ${test.name}: ${test.result.error}`)
      })
    }
    
    // 显示签到状态对比
    if (results.getStatus.success && results.getStatusAfter?.success) {
      console.log('\n📈 签到前后状态对比:')
      const before = results.getStatus.data
      const after = results.getStatusAfter.data
      
      console.log(`签到状态: ${before.isCheckedIn ? '已签到' : '未签到'} → ${after.isCheckedIn ? '已签到' : '未签到'}`)
      console.log(`连续天数: ${before.consecutiveDays || 0} → ${after.consecutiveDays || 0}`)
      console.log(`心愿力: ${before.totalWishPower || 0} → ${after.totalWishPower || 0}`)
    }

    return report
  }

  // 模拟签到状态（用于测试UI）
  getMockCheckinStatus(isCheckedIn = false) {
    return {
      isCheckedIn,
      consecutiveDays: isCheckedIn ? 3 : 2,
      lastCheckInDate: isCheckedIn ? new Date().toDateString() : null,
      nextReward: isCheckedIn ? 20 : 10, // 连续签到奖励翻倍
      totalWishPower: 150
    }
  }

  // 模拟签到结果（用于测试UI）
  getMockCheckinResult() {
    return {
      message: '签到成功',
      reward: {
        wishPower: 10
      },
      consecutiveDays: 4,
      totalWishPower: 160
    }
  }
}

// 创建全局测试实例
export const checkinTest = new CheckinTest()

// 快速测试函数
export async function quickCheckinTest() {
  return await checkinTest.testCheckinFlow()
}

// 获取模拟数据
export function getMockCheckinData() {
  return {
    status: checkinTest.getMockCheckinStatus(),
    result: checkinTest.getMockCheckinResult()
  }
}

export default CheckinTest
