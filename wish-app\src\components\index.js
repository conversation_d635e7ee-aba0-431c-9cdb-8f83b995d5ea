/**
 * 愿境组件库
 * 统一导出所有组件，方便全局注册和使用
 */

// 基础组件
import WishButton from './WishButton.vue'
import WishCard from './WishCard.vue'
import WishInput from './WishInput.vue'
import WishLoading from './WishLoading.vue'
import WishModal from './WishModal.vue'

// 组件列表
const components = [
  WishButton,
  WishCard,
  WishInput,
  WishLoading,
  WishModal
]

/**
 * 全局注册组件
 */
export function registerComponents(app) {
  components.forEach(component => {
    app.component(component.name, component)
  })
}

// 单独导出组件
export {
  WishButton,
  WishCard,
  WishInput,
  WishLoading,
  WishModal
}

// 默认导出
export default {
  install: registerComponents
}
