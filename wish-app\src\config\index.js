/**
 * 愿境应用配置文件
 * 包含应用的基础配置、API配置、主题配置等
 */

// 环境配置
const ENV = {
  development: {
    baseURL: 'http://localhost:3000/api',
    wsURL: 'ws://localhost:3000',
    debug: true
  },
  production: {
    baseURL: 'https://api.wishrealm.com/api',
    wsURL: 'wss://api.wishrealm.com',
    debug: false
  }
}

// 当前环境
const currentEnv = process.env.NODE_ENV === 'production' ? 'production' : 'development'

// 应用基础配置
export const APP_CONFIG = {
  name: '愿境',
  version: '1.0.0',
  description: '以祈愿守护为核心的精神社交平台',
  ...ENV[currentEnv]
}

// API接口配置
export const API_CONFIG = {
  timeout: 10000,
  retryTimes: 3,
  endpoints: {
    // 用户相关
    auth: {
      login: '/auth/login',
      register: '/auth/register',
      logout: '/auth/logout',
      refresh: '/auth/refresh',
      profile: '/auth/profile'
    },
    // 祈愿相关
    wish: {
      create: '/wish/create',
      list: '/wish/list',
      detail: '/wish/detail',
      delete: '/wish/delete',
      support: '/wish/support'
    },
    // 守护相关
    guardian: {
      bless: '/guardian/bless',
      list: '/guardian/list',
      feedback: '/guardian/feedback'
    },
    // 用户系统
    user: {
      profile: '/user/profile',
      update: '/user/update',
      switch: '/user/switch-role'
    },
    // 社交功能
    social: {
      comment: '/social/comment',
      message: '/social/message',
      leaderboard: '/social/leaderboard'
    }
  }
}

// 存储键名配置
export const STORAGE_KEYS = {
  TOKEN: 'wish_token',
  USER_INFO: 'wish_user_info',
  USER_ROLE: 'wish_user_role',
  DAILY_RITUAL: 'wish_daily_ritual',
  THEME: 'wish_theme',
  SETTINGS: 'wish_settings'
}

// 用户角色配置
export const USER_ROLES = {
  WISHER: 'wisher',      // 祈愿者
  GUARDIAN: 'guardian'   // 守护者
}

// 数值系统配置
export const GAME_CONFIG = {
  // 心愿力相关
  wishPower: {
    dailyRitual: 10,      // 每日仪式获得
    newUserBonus: 50,     // 新用户奖励
    publishWish: -5,      // 发布心愿消耗
    supportWish: -1       // 助力他人消耗
  },
  // 功德值相关
  meritPoints: {
    blessWish: 5,         // 回应心愿获得
    receiveThanks: 10,    // 收到感谢获得
    createTemple: -100    // 创建神殿消耗
  }
}

// 页面路径配置
export const ROUTES = {
  // 主要页面
  HOME: '/pages/index/index',
  LOGIN: '/pages/auth/login',
  REGISTER: '/pages/auth/register',
  PROFILE: '/pages/user/profile',
  
  // 祈愿者页面
  DAILY_RITUAL: '/pages/wisher/daily-ritual',
  POST_WISH: '/pages/wisher/post-wish',
  WISH_PLAZA: '/pages/wisher/wish-plaza',
  MY_WISHES: '/pages/wisher/my-wishes',
  
  // 守护者页面
  LISTEN_WISHES: '/pages/guardian/listen-wishes',
  BLESS_WISH: '/pages/guardian/bless-wish',
  MY_BLESSINGS: '/pages/guardian/my-blessings',
  
  // 社交页面
  LEADERBOARD: '/pages/social/leaderboard',
  MESSAGES: '/pages/social/messages',
  COMMENTS: '/pages/social/comments'
}

// 主题配置
export const THEME_CONFIG = {
  // 主色调 - 温暖治愈的色彩
  colors: {
    primary: '#E8B4A0',      // 温暖的粉橙色
    secondary: '#D4A574',     // 柔和的金色
    accent: '#B8C5D1',       // 静谧的蓝灰色
    background: '#F9F7F4',   // 温暖的米白色
    surface: '#FFFFFF',      // 纯白色
    text: {
      primary: '#4A4A4A',    // 深灰色文字
      secondary: '#8A8A8A',  // 中灰色文字
      disabled: '#CCCCCC'    // 浅灰色文字
    },
    status: {
      success: '#A8D8A8',    // 柔和的绿色
      warning: '#F4D03F',    // 柔和的黄色
      error: '#E8A4A4',      // 柔和的红色
      info: '#A4C4E8'        // 柔和的蓝色
    }
  },
  // 圆角配置
  borderRadius: {
    small: '8rpx',
    medium: '16rpx',
    large: '24rpx',
    round: '50%'
  },
  // 阴影配置
  shadows: {
    light: '0 2rpx 8rpx rgba(0,0,0,0.1)',
    medium: '0 4rpx 16rpx rgba(0,0,0,0.15)',
    heavy: '0 8rpx 32rpx rgba(0,0,0,0.2)'
  },
  // 动画配置
  animations: {
    duration: {
      fast: '200ms',
      normal: '300ms',
      slow: '500ms'
    },
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
  }
}

// 常量配置
export const CONSTANTS = {
  // 内容限制
  WISH_TITLE_MAX_LENGTH: 50,
  WISH_CONTENT_MAX_LENGTH: 500,
  COMMENT_MAX_LENGTH: 200,
  NICKNAME_MAX_LENGTH: 20,
  
  // 分页配置
  PAGE_SIZE: 20,
  
  // 文件上传
  MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: ['jpg', 'jpeg', 'png', 'gif'],
  
  // 标签配置
  WISH_TAGS: ['学业', '健康', '情感', '工作', '家庭', '财富', '平安', '其他']
}
