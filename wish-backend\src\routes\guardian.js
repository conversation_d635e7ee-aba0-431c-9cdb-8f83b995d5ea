/**
 * 守护路由
 * 处理守护者功能相关的API请求
 */

const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const { getModel } = require('../models');
const authMiddleware = require('../middleware/auth');
const { AppError, catchAsync, handleValidationErrors } = require('../middleware/errorHandler');
const { Cache } = require('../utils/redis');
const logger = require('../utils/logger');
const config = require('../config');

const router = express.Router();

/**
 * 守护模块根路由
 */
router.get('/', (req, res) => {
  res.json({
    module: '守护模块',
    endpoints: {
      bless: 'POST /api/guardian/bless/:wishId',
      blessings: 'GET /api/guardian/blessings/:wishId',
      thank: 'POST /api/guardian/blessings/:blessingId/thank',
      myBlessings: 'GET /api/guardian/my-blessings',
      receivedBlessings: 'GET /api/guardian/received-blessings',
      stats: 'GET /api/guardian/stats'
    }
  });
});

/**
 * 为心愿赐福
 */
router.post('/bless/:wishId', [
  param('wishId').isInt().withMessage('心愿ID必须是整数'),
  body('content')
    .isLength({ min: 1, max: 500 })
    .withMessage('赐福内容长度必须在1-500字符之间'),
  body('type')
    .optional()
    .isIn(['blessing', 'prayer', 'encouragement', 'advice'])
    .withMessage('赐福类型无效'),
  body('images')
    .optional()
    .isArray({ max: 3 })
    .withMessage('图片最多3张'),
  body('isAnonymous')
    .optional()
    .isBoolean()
    .withMessage('匿名设置必须是布尔值')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { wishId } = req.params;
  const {
    content,
    type = 'blessing',
    images = [],
    isAnonymous = false
  } = req.body;

  const Wish = getModel('Wish');
  const Blessing = getModel('Blessing');
  const User = getModel('User');

  // 检查心愿是否存在
  const wish = await Wish.findByPk(wishId, {
    include: [{
      model: User,
      as: 'creator',
      attributes: ['id', 'username', 'nickname', 'email']
    }]
  });

  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  // 检查心愿状态
  if (wish.status !== 'active') {
    throw new AppError('心愿已关闭，无法赐福', 400, 'WISH_NOT_ACTIVE');
  }

  // 检查是否为自己的心愿
  if (wish.creator_id === req.user.userId) {
    throw new AppError('不能为自己的心愿赐福', 400, 'CANNOT_BLESS_OWN_WISH');
  }

  // 检查是否已经赐福过
  const existingBlessing = await Blessing.findOne({
    where: {
      wish_id: wishId,
      guardian_id: req.user.userId
    }
  });

  if (existingBlessing) {
    throw new AppError('您已经为此心愿赐福过了', 400, 'ALREADY_BLESSED');
  }

  // 创建赐福
  const blessing = await Blessing.create({
    wish_id: wishId,
    guardian_id: req.user.userId,
    content,
    type,
    images: images.slice(0, 3),
    is_anonymous: isAnonymous
  });

  // 更新心愿的赐福数量
  await wish.increment('blessings');

  // 更新用户统计
  await User.increment('wishes_blessed', { where: { id: req.user.userId } });
  await User.increment('blessings_received', { where: { id: wish.creator_id } });

  // 奖励守护者功德值
  const guardian = await User.findByPk(req.user.userId);
  let meritReward = config.business.merit.blessWish;
  
  // 额外奖励
  if (images.length > 0) {
    meritReward += config.business.merit.blessWithImage;
  }
  if (content.length > 100) {
    meritReward += config.business.merit.blessWithLongText;
  }
  
  await guardian.addPoints('merit', meritReward);

  // 奖励心愿创建者心愿力
  const creator = await User.findByPk(wish.creator_id);
  await creator.addPoints('wishPower', config.business.wishPower.receiveBlessing);

  // 清除相关缓存
  await Cache.delPattern('wishes:*');
  await Cache.delPattern('blessings:*');

  // TODO: 发送通知给心愿创建者

  logger.info(`用户 ${req.user.username} 为心愿 ${wish.title} 赐福`);

  res.status(201).json({
    message: '赐福成功',
    blessing: {
      id: blessing.id,
      content: blessing.content,
      type: blessing.type,
      isAnonymous: blessing.is_anonymous,
      createdAt: blessing.created_at
    },
    reward: {
      merit: meritReward
    }
  });
}));

/**
 * 获取心愿的赐福列表
 */
router.get('/blessings/:wishId', [
  param('wishId').isInt().withMessage('心愿ID必须是整数'),
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须在1-50之间')
], handleValidationErrors, authMiddleware.optional, catchAsync(async (req, res) => {
  const { wishId } = req.params;
  const { page = 1, limit = 20 } = req.query;

  const Blessing = getModel('Blessing');
  const User = getModel('User');

  // 检查心愿是否存在
  const Wish = getModel('Wish');
  const wish = await Wish.findByPk(wishId);
  if (!wish) {
    throw new AppError('心愿不存在', 404, 'WISH_NOT_FOUND');
  }

  const result = await Blessing.findAndCountAll({
    where: { wish_id: wishId },
    order: [['created_at', 'DESC']],
    offset: (page - 1) * limit,
    limit: parseInt(limit),
    include: [{
      model: User,
      as: 'guardian',
      attributes: ['id', 'username', 'nickname', 'avatar', 'level']
    }]
  });

  // 处理匿名赐福
  const blessings = result.rows.map(blessing => {
    const blessingData = blessing.toJSON();
    if (blessing.is_anonymous) {
      blessingData.guardian = {
        id: null,
        username: '匿名守护者',
        nickname: '匿名守护者',
        avatar: '',
        level: 0
      };
    }
    return blessingData;
  });

  res.json({
    blessings,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: result.count,
      pages: Math.ceil(result.count / limit)
    }
  });
}));

/**
 * 感谢赐福
 */
router.post('/blessings/:blessingId/thank', [
  param('blessingId').isInt().withMessage('赐福ID必须是整数'),
  body('message')
    .optional()
    .isLength({ max: 200 })
    .withMessage('感谢消息最多200字符')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { blessingId } = req.params;
  const { message = '' } = req.body;

  const Blessing = getModel('Blessing');
  const User = getModel('User');

  const blessing = await Blessing.findByPk(blessingId, {
    include: [{
      model: getModel('Wish'),
      attributes: ['id', 'creator_id', 'title']
    }]
  });

  if (!blessing) {
    throw new AppError('赐福不存在', 404, 'BLESSING_NOT_FOUND');
  }

  // 检查权限（只有心愿创建者可以感谢）
  if (blessing.Wish.creator_id !== req.user.userId) {
    throw new AppError('只有心愿创建者可以感谢赐福', 403, 'ACCESS_DENIED');
  }

  // 检查是否已经感谢过
  if (blessing.thanked) {
    throw new AppError('已经感谢过此赐福', 400, 'ALREADY_THANKED');
  }

  // 更新赐福状态
  await blessing.update({
    thanked: true,
    thanked_at: new Date(),
    thank_message: message
  });

  // 奖励守护者功德值
  const guardian = await User.findByPk(blessing.guardian_id);
  await guardian.addPoints('merit', config.business.merit.receiveThanks);

  // TODO: 发送通知给守护者

  logger.info(`用户 ${req.user.username} 感谢赐福 ${blessingId}`);

  res.json({
    message: '感谢成功',
    reward: {
      guardianMerit: config.business.merit.receiveThanks
    }
  });
}));

/**
 * 获取用户的赐福历史
 */
router.get('/my-blessings', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须在1-50之间'),
  query('type').optional().isIn(['blessing', 'prayer', 'encouragement', 'advice'])
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { page = 1, limit = 20, type } = req.query;

  const Blessing = getModel('Blessing');
  const User = getModel('User');
  const Wish = getModel('Wish');

  const where = { guardian_id: req.user.userId };
  if (type) where.type = type;

  const result = await Blessing.findAndCountAll({
    where,
    order: [['created_at', 'DESC']],
    offset: (page - 1) * limit,
    limit: parseInt(limit),
    include: [
      {
        model: Wish,
        attributes: ['id', 'title', 'type', 'status'],
        include: [{
          model: User,
          as: 'creator',
          attributes: ['id', 'username', 'nickname', 'avatar']
        }]
      }
    ]
  });

  res.json({
    blessings: result.rows,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: result.count,
      pages: Math.ceil(result.count / limit)
    }
  });
}));

/**
 * 获取收到的赐福
 */
router.get('/received-blessings', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须在1-50之间')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { page = 1, limit = 20 } = req.query;

  const Blessing = getModel('Blessing');
  const User = getModel('User');
  const Wish = getModel('Wish');

  const result = await Blessing.findAndCountAll({
    order: [['created_at', 'DESC']],
    offset: (page - 1) * limit,
    limit: parseInt(limit),
    include: [
      {
        model: Wish,
        where: { creator_id: req.user.userId },
        attributes: ['id', 'title', 'type']
      },
      {
        model: User,
        as: 'guardian',
        attributes: ['id', 'username', 'nickname', 'avatar', 'level']
      }
    ]
  });

  // 处理匿名赐福
  const blessings = result.rows.map(blessing => {
    const blessingData = blessing.toJSON();
    if (blessing.is_anonymous) {
      blessingData.guardian = {
        id: null,
        username: '匿名守护者',
        nickname: '匿名守护者',
        avatar: '',
        level: 0
      };
    }
    return blessingData;
  });

  res.json({
    blessings,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: result.count,
      pages: Math.ceil(result.count / limit)
    }
  });
}));

/**
 * 获取守护者统计
 */
router.get('/stats', authMiddleware.required, catchAsync(async (req, res) => {
  const userId = req.user.userId;
  const cacheKey = `guardian:stats:${userId}`;

  let stats = await Cache.get(cacheKey);

  if (!stats) {
    const Blessing = getModel('Blessing');
    const User = getModel('User');

    const user = await User.findByPk(userId);
    
    const [
      totalBlessings,
      thankedBlessings,
      blessingsByType
    ] = await Promise.all([
      Blessing.count({ where: { guardian_id: userId } }),
      Blessing.count({ where: { guardian_id: userId, thanked: true } }),
      Blessing.findAll({
        where: { guardian_id: userId },
        attributes: [
          'type',
          [require('sequelize').fn('COUNT', require('sequelize').col('id')), 'count']
        ],
        group: ['type']
      })
    ]);

    stats = {
      totalBlessings,
      thankedBlessings,
      thankRate: totalBlessings > 0 ? (thankedBlessings / totalBlessings * 100).toFixed(1) : 0,
      meritPoints: user.merit_points,
      blessingsByType: blessingsByType.reduce((acc, item) => {
        acc[item.type] = parseInt(item.dataValues.count);
        return acc;
      }, {})
    };

    await Cache.set(cacheKey, stats, 300); // 5分钟缓存
  }

  res.json({
    stats
  });
}));

module.exports = router;
