/**
 * 认证中间件
 * 处理JWT令牌验证和用户权限检查
 */

const jwt = require('jsonwebtoken');
const User = require('../models/User');
const config = require('../config');
const logger = require('../utils/logger');

/**
 * 验证JWT令牌
 */
function verifyToken(token) {
  try {
    return jwt.verify(token, config.jwt.secret, {
      issuer: config.jwt.issuer,
      audience: config.jwt.audience
    });
  } catch (error) {
    throw new Error('无效的令牌');
  }
}

/**
 * 从请求中提取令牌
 */
function extractToken(req) {
  // 从Authorization头部提取
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // 从查询参数提取（用于某些特殊场景）
  if (req.query.token) {
    return req.query.token;
  }
  
  return null;
}

/**
 * 必需认证中间件
 * 要求用户必须登录
 */
const required = async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      return res.status(401).json({
        error: '访问令牌缺失',
        code: 'TOKEN_MISSING'
      });
    }

    // 验证令牌
    const decoded = verifyToken(token);
    
    // 检查令牌类型
    if (decoded.type === 'refresh') {
      return res.status(401).json({
        error: '无效的令牌类型',
        code: 'INVALID_TOKEN_TYPE'
      });
    }

    // 查找用户
    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user) {
      return res.status(401).json({
        error: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }

    // 检查用户状态
    if (user.status !== 'active') {
      return res.status(401).json({
        error: '账户已被禁用',
        code: 'ACCOUNT_DISABLED'
      });
    }

    // 检查账户是否被锁定
    if (user.isLocked) {
      return res.status(401).json({
        error: '账户已被锁定',
        code: 'ACCOUNT_LOCKED'
      });
    }

    // 检查密码是否在令牌签发后被修改
    const tokenIssuedAt = new Date(decoded.iat * 1000);
    if (user.security.passwordChangedAt > tokenIssuedAt) {
      return res.status(401).json({
        error: '令牌已失效，请重新登录',
        code: 'TOKEN_EXPIRED_PASSWORD_CHANGED'
      });
    }

    // 将用户信息添加到请求对象
    req.user = {
      userId: user._id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      profile: user.profile,
      points: user.points,
      level: user.level
    };

    next();

  } catch (error) {
    logger.error('认证中间件错误:', error);
    
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: '无效的访问令牌',
        code: 'INVALID_TOKEN'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: '访问令牌已过期',
        code: 'TOKEN_EXPIRED'
      });
    }

    res.status(500).json({
      error: '认证失败',
      code: 'AUTH_ERROR'
    });
  }
};

/**
 * 可选认证中间件
 * 如果有令牌则验证，没有则跳过
 */
const optional = async (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      // 没有令牌，继续执行
      req.user = null;
      return next();
    }

    // 有令牌，尝试验证
    const decoded = verifyToken(token);
    
    if (decoded.type === 'refresh') {
      req.user = null;
      return next();
    }

    const user = await User.findById(decoded.userId).select('-password');
    
    if (!user || user.status !== 'active' || user.isLocked) {
      req.user = null;
      return next();
    }

    // 检查密码是否在令牌签发后被修改
    const tokenIssuedAt = new Date(decoded.iat * 1000);
    if (user.security.passwordChangedAt > tokenIssuedAt) {
      req.user = null;
      return next();
    }

    req.user = {
      userId: user._id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      profile: user.profile,
      points: user.points,
      level: user.level
    };

    next();

  } catch (error) {
    // 可选认证失败时不返回错误，继续执行
    req.user = null;
    next();
  }
};

/**
 * 角色权限检查中间件
 */
const requireRole = (roles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: '需要登录',
        code: 'LOGIN_REQUIRED'
      });
    }

    const userRoles = req.user.roles || [];
    const hasRole = roles.some(role => userRoles.includes(role));

    if (!hasRole) {
      return res.status(403).json({
        error: '权限不足',
        code: 'INSUFFICIENT_PERMISSIONS',
        required: roles,
        current: userRoles
      });
    }

    next();
  };
};

/**
 * 管理员权限检查
 */
const requireAdmin = requireRole(['admin']);

/**
 * 版主权限检查
 */
const requireModerator = requireRole(['admin', 'moderator']);

/**
 * 守护者权限检查
 */
const requireGuardian = requireRole(['admin', 'moderator', 'guardian']);

/**
 * 邮箱验证检查中间件
 */
const requireEmailVerified = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: '需要登录',
        code: 'LOGIN_REQUIRED'
      });
    }

    const user = await User.findById(req.user.userId);
    
    if (!user.verification.email.verified) {
      return res.status(403).json({
        error: '需要验证邮箱',
        code: 'EMAIL_VERIFICATION_REQUIRED'
      });
    }

    next();

  } catch (error) {
    logger.error('邮箱验证检查失败:', error);
    res.status(500).json({
      error: '验证检查失败',
      code: 'VERIFICATION_CHECK_ERROR'
    });
  }
};

/**
 * 手机验证检查中间件
 */
const requirePhoneVerified = async (req, res, next) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        error: '需要登录',
        code: 'LOGIN_REQUIRED'
      });
    }

    const user = await User.findById(req.user.userId);
    
    if (!user.verification.phone.verified) {
      return res.status(403).json({
        error: '需要验证手机号',
        code: 'PHONE_VERIFICATION_REQUIRED'
      });
    }

    next();

  } catch (error) {
    logger.error('手机验证检查失败:', error);
    res.status(500).json({
      error: '验证检查失败',
      code: 'VERIFICATION_CHECK_ERROR'
    });
  }
};

/**
 * 用户等级检查中间件
 */
const requireLevel = (minLevel) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: '需要登录',
        code: 'LOGIN_REQUIRED'
      });
    }

    if (req.user.level.current < minLevel) {
      return res.status(403).json({
        error: `需要达到${minLevel}级`,
        code: 'INSUFFICIENT_LEVEL',
        required: minLevel,
        current: req.user.level.current
      });
    }

    next();
  };
};

/**
 * 资源所有者检查中间件
 */
const requireOwnership = (getResourceOwnerId) => {
  return async (req, res, next) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          error: '需要登录',
          code: 'LOGIN_REQUIRED'
        });
      }

      const ownerId = await getResourceOwnerId(req);
      
      if (!ownerId) {
        return res.status(404).json({
          error: '资源不存在',
          code: 'RESOURCE_NOT_FOUND'
        });
      }

      // 管理员可以访问所有资源
      if (req.user.roles.includes('admin')) {
        return next();
      }

      // 检查是否为资源所有者
      if (ownerId.toString() !== req.user.userId.toString()) {
        return res.status(403).json({
          error: '无权访问此资源',
          code: 'ACCESS_DENIED'
        });
      }

      next();

    } catch (error) {
      logger.error('所有权检查失败:', error);
      res.status(500).json({
        error: '权限检查失败',
        code: 'OWNERSHIP_CHECK_ERROR'
      });
    }
  };
};

module.exports = {
  required,
  optional,
  requireRole,
  requireAdmin,
  requireModerator,
  requireGuardian,
  requireEmailVerified,
  requirePhoneVerified,
  requireLevel,
  requireOwnership,
  verifyToken,
  extractToken
};
