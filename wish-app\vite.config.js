import { defineConfig } from 'vite'
import uni from '@dcloudio/vite-plugin-uni'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    uni(),
  ],
  css: {
    preprocessorOptions: {
      scss: {
        api: 'modern-compiler', // 使用现代 Sass API
        silenceDeprecations: ['legacy-js-api', 'import'], // 静默 legacy API 和 import 警告
      }
    }
  },
  server: {
    host: '0.0.0.0', // 允许外部访问
    port: 5173, // 开发服务器端口
    open: true, // 自动打开浏览器
    cors: true, // 启用CORS
    proxy: {
      // 代理所有 /api 请求到后端服务器
      '/api': {
        target: 'http://localhost:3000', // 后端服务器地址
        changeOrigin: true, // 改变请求头中的origin字段
        secure: false, // 如果是https接口，需要配置这个参数
        ws: true, // 支持websocket
        rewrite: (path) => path, // 不重写路径，保持 /api 前缀
        configure: (proxy, options) => {
          // 配置代理选项
          proxy.on('error', (err, req, res) => {
            console.log('代理错误:', err)
          })
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('代理请求:', req.method, req.url)
          })
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('代理响应:', proxyRes.statusCode, req.url)
          })
        }
      },
      // 代理健康检查接口
      '/health': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false
      },
      // 代理上传文件接口
      '/uploads': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false
      },
      // 代理Socket.IO连接
      '/socket.io': {
        target: 'http://localhost:3000',
        changeOrigin: true,
        secure: false,
        ws: true // 启用WebSocket代理
      }
    }
  }
})
