<!--
  愿境每日仪式页面
  用户每日签到和心灵仪式体验
-->
<template>
  <view class="daily-ritual-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar safe-area-top">
      <view class="navbar-content">
        <view class="navbar-back" @click="goBack">
          <image src="/static/icons/arrow-left.png" class="back-icon" />
        </view>
        <text class="navbar-title">每日仪式</text>
        <view class="navbar-placeholder"></view>
      </view>
    </view>
    
    <!-- 主要内容 -->
    <view class="ritual-content">
      <!-- 仪式状态 -->
      <view class="ritual-status">
        <view class="status-circle" :class="{ 'status-circle--completed': isCompleted }">
          <view class="status-inner">
            <image 
              v-if="isCompleted"
              src="/static/icons/check-large.png" 
              class="status-icon completed" 
            />
            <image 
              v-else
              src="/static/icons/ritual-large.png" 
              class="status-icon pending" 
            />
          </view>
        </view>
        
        <text class="status-text">{{ statusText }}</text>
        <text class="status-desc">{{ statusDesc }}</text>
      </view>
      
      <!-- 连续签到天数 -->
      <view class="streak-info">
        <view class="streak-card">
          <text class="streak-label">连续签到</text>
          <text class="streak-number">{{ checkInStatus.streak }}</text>
          <text class="streak-unit">天</text>
        </view>
        
        <view class="reward-info">
          <text class="reward-text">今日可获得</text>
          <view class="reward-item">
            <image src="/static/icons/wish-power.png" class="reward-icon" />
            <text class="reward-value">+{{ todayReward }}</text>
          </view>
        </view>
      </view>
      
      <!-- 仪式步骤 -->
      <view class="ritual-steps" v-if="!isCompleted">
        <text class="steps-title">开始今日的心灵仪式</text>
        
        <view class="step-list">
          <view 
            class="step-item"
            :class="{ 'step-item--active': currentStep >= index + 1 }"
            v-for="(step, index) in ritualSteps"
            :key="index"
          >
            <view class="step-number">{{ index + 1 }}</view>
            <view class="step-content">
              <text class="step-title">{{ step.title }}</text>
              <text class="step-desc">{{ step.desc }}</text>
            </view>
          </view>
        </view>
        
        <!-- 开始仪式按钮 -->
        <wish-button
          type="primary"
          size="large"
          :text="ritualButtonText"
          :loading="ritualLoading"
          @click="startRitual"
          class="ritual-button"
        />
      </view>
      
      <!-- 已完成状态 -->
      <view class="completed-info" v-else>
        <text class="completed-title">今日仪式已完成</text>
        <text class="completed-desc">感谢你的坚持，明天再来吧</text>
        
        <view class="completed-rewards">
          <text class="rewards-title">今日收获</text>
          <view class="reward-list">
            <view class="reward-item">
              <image src="/static/icons/wish-power.png" class="reward-icon" />
              <text class="reward-text">心愿力 +{{ todayReward }}</text>
            </view>
            <view class="reward-item" v-if="bonusReward > 0">
              <image src="/static/icons/bonus.png" class="reward-icon" />
              <text class="reward-text">连续奖励 +{{ bonusReward }}</text>
            </view>
          </view>
        </view>
        
        <!-- 继续探索按钮 -->
        <wish-button
          type="secondary"
          size="large"
          text="继续探索愿境"
          @click="goToHome"
          class="explore-button"
        />
      </view>
    </view>
    
    <!-- 仪式进行中的模态框 -->
    <wish-modal
      v-model:visible="showRitualModal"
      :show-close="false"
      :mask-closable="false"
      position="center"
    >
      <view class="ritual-modal">
        <view class="ritual-animation">
          <view class="breathing-circle">
            <view class="circle-inner"></view>
          </view>
        </view>
        
        <text class="ritual-guide">{{ currentGuideText }}</text>
        
        <view class="ritual-progress">
          <view class="progress-bar">
            <view 
              class="progress-fill" 
              :style="{ width: ritualProgress + '%' }"
            ></view>
          </view>
          <text class="progress-text">{{ Math.round(ritualProgress) }}%</text>
        </view>
      </view>
    </wish-modal>
  </view>
</template>

<script>
import { useUserStore, useSystemStore } from '@/store'
import { navigation, toast } from '@/utils'

export default {
  data() {
    return {
      currentStep: 0,
      ritualLoading: false,
      showRitualModal: false,
      ritualProgress: 0,
      currentGuideText: '',
      ritualSteps: [
        {
          title: '静心准备',
          desc: '找一个安静的地方，放松身心'
        },
        {
          title: '深呼吸',
          desc: '缓慢深呼吸，感受内心的平静'
        },
        {
          title: '许下心愿',
          desc: '在心中默念今天的愿望'
        },
        {
          title: '感恩时刻',
          desc: '感谢生活中的美好瞬间'
        }
      ],
      guideTexts: [
        '请找一个安静舒适的地方...',
        '深深吸气...慢慢呼气...',
        '在心中许下今天的愿望...',
        '感谢生活中的每一份美好...',
        '仪式即将完成...'
      ]
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    systemStore() {
      return useSystemStore()
    },
    
    checkInStatus() {
      return this.systemStore.checkInStatus
    },
    
    isCompleted() {
      return this.checkInStatus.isCheckedIn
    },
    
    statusText() {
      return this.isCompleted ? '今日仪式已完成' : '开始今日仪式'
    },
    
    statusDesc() {
      if (this.isCompleted) {
        return `连续签到 ${this.checkInStatus.streak} 天`
      }
      return '通过每日仪式获得心愿力'
    },
    
    todayReward() {
      return this.systemStore.checkInReward
    },
    
    bonusReward() {
      const streak = this.checkInStatus.streak
      if (streak >= 7) return 20
      if (streak >= 3) return 10
      return 0
    },
    
    ritualButtonText() {
      if (this.currentStep === 0) return '开始仪式'
      return `继续 (${this.currentStep}/${this.ritualSteps.length})`
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      try {
        await this.systemStore.fetchCheckInStatus()
      } catch (error) {
        console.error('获取签到状态失败:', error)
      }
    },
    
    /**
     * 开始仪式
     */
    async startRitual() {
      if (this.isCompleted) {
        return
      }
      
      this.ritualLoading = true
      this.showRitualModal = true
      
      try {
        // 执行仪式步骤
        await this.performRitualSteps()
        
        // 完成签到
        await this.systemStore.dailyCheckIn()
        
        // 更新用户心愿力
        this.userStore.userStats.wishPower += this.todayReward + this.bonusReward
        
        toast.success('仪式完成！')
        
      } catch (error) {
        console.error('仪式执行失败:', error)
        toast.error('仪式执行失败，请重试')
      } finally {
        this.ritualLoading = false
        this.showRitualModal = false
        this.resetRitualState()
      }
    },
    
    /**
     * 执行仪式步骤
     */
    async performRitualSteps() {
      const stepDuration = 3000 // 每步3秒
      const totalSteps = this.ritualSteps.length
      
      for (let i = 0; i < totalSteps; i++) {
        this.currentStep = i + 1
        this.currentGuideText = this.guideTexts[i]
        
        // 进度动画
        await this.animateProgress(i * (100 / totalSteps), (i + 1) * (100 / totalSteps), stepDuration)
        
        // 等待步骤完成
        await this.sleep(500)
      }
      
      // 最后的完成提示
      this.currentGuideText = this.guideTexts[totalSteps]
      await this.sleep(1000)
    },
    
    /**
     * 进度动画
     */
    animateProgress(from, to, duration) {
      return new Promise((resolve) => {
        const startTime = Date.now()
        const animate = () => {
          const elapsed = Date.now() - startTime
          const progress = Math.min(elapsed / duration, 1)
          
          this.ritualProgress = from + (to - from) * progress
          
          if (progress < 1) {
            requestAnimationFrame(animate)
          } else {
            resolve()
          }
        }
        animate()
      })
    },
    
    /**
     * 延时函数
     */
    sleep(ms) {
      return new Promise(resolve => setTimeout(resolve, ms))
    },
    
    /**
     * 重置仪式状态
     */
    resetRitualState() {
      this.currentStep = 0
      this.ritualProgress = 0
      this.currentGuideText = ''
    },
    
    /**
     * 页面跳转
     */
    goBack() {
      navigation.navigateBack()
    },
    
    goToHome() {
      navigation.switchTab('/pages/index/index')
    }
  }
}
</script>

<style lang="scss" scoped>
.daily-ritual-page {
  min-height: 100vh;
  background: linear-gradient(180deg, $wish-bg-primary 0%, rgba(232, 180, 160, 0.05) 100%);
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: transparent;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $wish-spacing-md;
}

.navbar-back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
}

.navbar-title {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
}

.navbar-placeholder {
  width: 48rpx;
}

/* 仪式内容 */
.ritual-content {
  padding: $wish-spacing-xl $wish-spacing-md;
  text-align: center;
}

/* 仪式状态 */
.ritual-status {
  margin-bottom: $wish-spacing-xxl;
}

.status-circle {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(232, 180, 160, 0.2) 0%, rgba(212, 165, 116, 0.2) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto $wish-spacing-lg;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: -4rpx;
    left: -4rpx;
    right: -4rpx;
    bottom: -4rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, $wish-color-primary 0%, $wish-color-secondary 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &--completed::before {
    opacity: 0.3;
  }
}

.status-inner {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background-color: $wish-bg-secondary;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.status-icon {
  width: 80rpx;
  height: 80rpx;

  &.pending {
    animation: pulse 2s ease-in-out infinite;
  }

  &.completed {
    animation: bounce 0.6s ease-out;
  }
}

.status-text {
  display: block;
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.status-desc {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
}

/* 连续签到信息 */
.streak-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-lg;
  margin-bottom: $wish-spacing-xxl;
  box-shadow: $wish-shadow-sm;
}

.streak-card {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.streak-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.streak-number {
  font-size: 48rpx;
  font-weight: 600;
  color: $wish-color-primary;
  line-height: 1;
}

.streak-unit {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.reward-info {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.reward-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.reward-item {
  display: flex;
  align-items: center;
}

.reward-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: $wish-spacing-xs;
}

.reward-value {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-color-secondary;
}

/* 仪式步骤 */
.ritual-steps {
  text-align: left;
}

.steps-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  text-align: center;
  margin-bottom: $wish-spacing-lg;
}

.step-list {
  margin-bottom: $wish-spacing-xl;
}

.step-item {
  display: flex;
  align-items: flex-start;
  padding: $wish-spacing-md;
  margin-bottom: $wish-spacing-sm;
  border-radius: $wish-radius-md;
  transition: all 0.3s ease;

  &--active {
    background-color: rgba(232, 180, 160, 0.1);

    .step-number {
      background-color: $wish-color-primary;
      color: $wish-text-inverse;
    }
  }
}

.step-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  background-color: $wish-border-medium;
  color: $wish-text-secondary;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: $wish-font-sm;
  font-weight: 600;
  margin-right: $wish-spacing-md;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.step-content {
  flex: 1;
}

.step-title {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.step-desc {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  line-height: 1.6;
}

.ritual-button {
  width: 100%;
}

/* 已完成状态 */
.completed-info {
  text-align: center;
}

.completed-title {
  display: block;
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-color-success;
  margin-bottom: $wish-spacing-xs;
}

.completed-desc {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xl;
}

.completed-rewards {
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-lg;
  margin-bottom: $wish-spacing-xl;
  box-shadow: $wish-shadow-sm;
}

.rewards-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-md;
}

.reward-list {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.explore-button {
  width: 100%;
}

/* 仪式模态框 */
.ritual-modal {
  text-align: center;
  padding: $wish-spacing-xl 0;
}

.ritual-animation {
  margin-bottom: $wish-spacing-xl;
}

.breathing-circle {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, $wish-color-primary 0%, $wish-color-secondary 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  animation: breathe 4s ease-in-out infinite;
}

.circle-inner {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  background-color: $wish-bg-secondary;
  animation: breathe-inner 4s ease-in-out infinite;
}

.ritual-guide {
  display: block;
  font-size: $wish-font-lg;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xl;
  line-height: 1.6;
}

.ritual-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-bar {
  width: 300rpx;
  height: 8rpx;
  background-color: $wish-border-light;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: $wish-spacing-sm;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, $wish-color-primary 0%, $wish-color-secondary 100%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

/* 动画定义 */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes bounce {
  0% {
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes breathe {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes breathe-inner {
  0%, 100% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(0.95);
    opacity: 1;
  }
}
</style>
