/**
 * 愿境应用后端主入口文件
 * 基于 Express + MySQL + Socket.IO
 */

const express = require('express');
const http = require('http');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const morgan = require('morgan');
const rateLimit = require('express-rate-limit');
const { Server } = require('socket.io');

// 导入配置和工具
const config = require('./config');
const logger = require('./utils/logger');
const { connectDatabase, syncDatabase } = require('./database/connection');
const { connectRedis } = require('./utils/redis');
const { initModels } = require('./models');
const { initSocketHandlers } = require('./socket');

// 导入中间件
const { errorHandler, notFoundHandler } = require('./middleware/errorHandler');
const authMiddleware = require('./middleware/auth');

// 导入路由
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/user');
const wishRoutes = require('./routes/wish');
const guardianRoutes = require('./routes/guardian');
const socialRoutes = require('./routes/social');
const chatRoutes = require('./routes/chat');
const uploadRoutes = require('./routes/upload');

// 创建Express应用
const app = express();
const server = http.createServer(app);

// 创建Socket.IO实例
let io = null;
if (config.socket.enabled) {
  io = new Server(server, {
    cors: config.socket.cors,
    pingTimeout: config.socket.pingTimeout,
    pingInterval: config.socket.pingInterval
  });
}

// 基础中间件
app.use(helmet({
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

// CORS配置 - 必须在其他中间件之前
app.use(cors(config.cors));

// 处理预检请求
app.options('*', cors(config.cors));

app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 日志中间件
if (config.env === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// 速率限制
const limiter = rateLimit(config.rateLimit);
app.use('/api/', limiter);

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.env,
    version: require('../package.json').version
  });
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/wishes', wishRoutes);
app.use('/api/guardian', guardianRoutes);
app.use('/api/social', socialRoutes);
app.use('/api/chat', chatRoutes);
app.use('/api/upload', uploadRoutes);

// API文档路由
app.get('/api', (req, res) => {
  res.json({
    name: '愿境应用API',
    version: require('../package.json').version,
    description: '愿境应用后端API服务',
    endpoints: {
      auth: '/api/auth',
      users: '/api/users',
      wishes: '/api/wishes',
      guardian: '/api/guardian',
      social: '/api/social',
      chat: '/api/chat',
      upload: '/api/upload'
    },
    documentation: 'https://github.com/wish-team/wish-backend#api-documentation'
  });
});

// 错误处理中间件
app.use(notFoundHandler);
app.use(errorHandler);

// 启动服务器
async function startServer() {
  try {
    logger.info('正在启动愿境应用后端服务...');

    // 连接数据库
    await connectDatabase();
    logger.info('MySQL数据库连接成功');

    // 初始化模型
    initModels();
    logger.info('数据库模型初始化完成');

    // 同步数据库表结构（开发环境）
    if (config.env === 'development') {
      await syncDatabase();
      logger.info('数据库表结构同步完成');
    }

    // 连接Redis（可选）
    if (config.redis.enabled) {
      await connectRedis();
      logger.info('Redis连接成功');
    } else {
      logger.info('Redis未启用，使用内存缓存');
    }

    // 初始化Socket.IO处理器
    if (config.socket.enabled && io) {
      initSocketHandlers(io);
      logger.info('Socket.IO实时通信服务启动成功');
    }

    // 启动HTTP服务器
    const port = config.port;
    server.listen(port, () => {
      logger.info(`🚀 愿境应用后端服务启动成功！`);
      logger.info(`📍 服务地址: http://localhost:${port}`);
      logger.info(`🌍 运行环境: ${config.env}`);
      logger.info(`💾 数据库: MySQL`);
      logger.info(`🔄 缓存: ${config.redis.enabled ? 'Redis' : '内存缓存'}`);
      logger.info(`⚡ 实时通信: ${config.socket.enabled ? 'Socket.IO' : '禁用'}`);
      logger.info(`📚 API文档: http://localhost:${port}/api`);
      logger.info(`❤️  健康检查: http://localhost:${port}/health`);
    });

  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

async function gracefulShutdown(signal) {
  logger.info(`收到 ${signal} 信号，开始优雅关闭服务器...`);
  
  server.close(() => {
    logger.info('HTTP服务器已关闭');
    
    // 关闭数据库连接
    const { disconnectDatabase } = require('./database/connection');
    disconnectDatabase().then(() => {
      logger.info('数据库连接已关闭');
      process.exit(0);
    }).catch((error) => {
      logger.error('关闭数据库连接时出错:', error);
      process.exit(1);
    });
  });
}

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动服务器
if (require.main === module) {
  startServer();
}

module.exports = { app, server, io };
