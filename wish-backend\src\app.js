/**
 * 愿境应用后端主入口文件
 * 配置Express应用和中间件
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const { createServer } = require('http');
const { Server } = require('socket.io');

// 导入配置和工具
const config = require('./config');
const logger = require('./utils/logger');
const { connectDatabase } = require('./database/connection');
const { connectRedis } = require('./utils/redis');
const errorHandler = require('./middleware/errorHandler');
const authMiddleware = require('./middleware/auth');

// 导入路由
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/user');
const wishRoutes = require('./routes/wish');
const guardianRoutes = require('./routes/guardian');
const socialRoutes = require('./routes/social');
const safetyRoutes = require('./routes/safety');
const uploadRoutes = require('./routes/upload');

// 创建Express应用
const app = express();
const server = createServer(app);

// 创建Socket.IO实例
const io = new Server(server, {
  cors: {
    origin: config.cors.origin,
    methods: ['GET', 'POST'],
    credentials: true
  }
});

// 基础中间件
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
}));

app.use(cors({
  origin: config.cors.origin,
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
}));

app.use(compression());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 请求日志
if (config.env !== 'test') {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// 速率限制
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: config.rateLimit.max, // 限制每个IP的请求数
  message: {
    error: '请求过于频繁，请稍后再试',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

app.use('/api/', limiter);

// 健康检查
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
    environment: config.env
  });
});

// API路由
app.use('/api/auth', authRoutes);
app.use('/api/users', authMiddleware.optional, userRoutes);
app.use('/api/wishes', authMiddleware.optional, wishRoutes);
app.use('/api/guardian', authMiddleware.required, guardianRoutes);
app.use('/api/social', authMiddleware.optional, socialRoutes);
app.use('/api/safety', authMiddleware.optional, safetyRoutes);
app.use('/api/upload', authMiddleware.required, uploadRoutes);

// 静态文件服务
app.use('/uploads', express.static('uploads'));

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    error: '接口不存在',
    code: 'NOT_FOUND',
    path: req.originalUrl
  });
});

// 错误处理中间件
app.use(errorHandler);

// Socket.IO连接处理
io.on('connection', (socket) => {
  logger.info(`用户连接: ${socket.id}`);

  // 用户加入房间
  socket.on('join', (data) => {
    const { userId, type } = data;
    const room = `${type}_${userId}`;
    socket.join(room);
    logger.info(`用户 ${userId} 加入房间 ${room}`);
  });

  // 处理私信
  socket.on('private_message', (data) => {
    const { toUserId, message } = data;
    const room = `user_${toUserId}`;
    io.to(room).emit('new_message', message);
  });

  // 处理心愿更新通知
  socket.on('wish_update', (data) => {
    const { wishId, update } = data;
    io.emit('wish_updated', { wishId, update });
  });

  // 处理赐福通知
  socket.on('blessing_notification', (data) => {
    const { userId, blessing } = data;
    const room = `user_${userId}`;
    io.to(room).emit('new_blessing', blessing);
  });

  // 断开连接
  socket.on('disconnect', () => {
    logger.info(`用户断开连接: ${socket.id}`);
  });
});

// 启动服务器
async function startServer() {
  try {
    // 连接数据库
    await connectDatabase();
    logger.info('数据库连接成功');

    // 连接Redis
    await connectRedis();
    logger.info('Redis连接成功');

    // 启动HTTP服务器
    const port = config.port || 3000;
    server.listen(port, () => {
      logger.info(`服务器启动成功，端口: ${port}`);
      logger.info(`环境: ${config.env}`);
      logger.info(`API文档: http://localhost:${port}/api-docs`);
    });

  } catch (error) {
    logger.error('服务器启动失败:', error);
    process.exit(1);
  }
}

// 优雅关闭
process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号，开始优雅关闭...');
  server.close(() => {
    logger.info('HTTP服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号，开始优雅关闭...');
  server.close(() => {
    logger.info('HTTP服务器已关闭');
    process.exit(0);
  });
});

// 未捕获的异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 导出应用实例（用于测试）
module.exports = { app, server, io };

// 如果直接运行此文件，启动服务器
if (require.main === module) {
  startServer();
}
