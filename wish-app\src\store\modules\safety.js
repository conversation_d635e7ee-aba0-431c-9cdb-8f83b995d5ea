/**
 * 安全模块状态管理
 * 处理内容审核、举报、安全相关功能
 */

import { defineStore } from 'pinia'
import { safetyAPI } from '@/api'

export const useSafetyStore = defineStore('safety', {
  state: () => ({
    // 举报列表
    reportList: [],
    
    // 审核统计
    moderationStats: {
      pending: 0,
      approved: 0,
      rejected: 0,
      today: 0
    },
    
    // 内容过滤配置
    filterConfig: {
      enabled: true,
      strictMode: false,
      autoFilter: true
    },
    
    // 敏感词库
    wordLibrary: {
      sensitiveWords: [],
      bannedWords: [],
      spamPatterns: []
    },
    
    // 分页信息
    pagination: {
      current: 1,
      pageSize: 20,
      hasMore: true
    },
    
    // 加载状态
    loading: {
      reports: false,
      submit: false,
      process: false
    }
  }),

  getters: {
    /**
     * 待处理举报数量
     */
    pendingReportsCount: (state) => {
      return state.moderationStats.pending || 0
    },

    /**
     * 今日处理数量
     */
    todayProcessedCount: (state) => {
      return state.moderationStats.today || 0
    },

    /**
     * 处理率
     */
    processRate: (state) => {
      const total = state.moderationStats.approved + state.moderationStats.rejected
      const pending = state.moderationStats.pending
      if (total + pending === 0) return 100
      return Math.round((total / (total + pending)) * 100)
    }
  },

  actions: {
    /**
     * 提交举报
     */
    async submitReport(reportData) {
      this.loading.submit = true
      try {
        const result = await safetyAPI.submitReport(reportData)
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.submit = false
      }
    },

    /**
     * 获取举报对象信息
     */
    async getReportTargetInfo(type, targetId) {
      try {
        const targetInfo = await safetyAPI.getReportTargetInfo(type, targetId)
        return targetInfo
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取举报列表
     */
    async fetchReports(params = {}, refresh = false) {
      this.loading.reports = true
      try {
        if (refresh) {
          this.pagination.current = 1
          this.pagination.hasMore = true
        }

        const requestParams = {
          ...params,
          page: this.pagination.current,
          pageSize: this.pagination.pageSize
        }

        const result = await safetyAPI.getReports(requestParams)

        if (refresh) {
          this.reportList = result.list || []
        } else {
          this.reportList.push(...(result.list || []))
        }

        this.pagination.current = result.pagination?.current || this.pagination.current
        this.pagination.hasMore = result.pagination?.hasMore || false

        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.reports = false
      }
    },

    /**
     * 获取举报详情
     */
    async fetchReportDetail(reportId) {
      try {
        const reportDetail = await safetyAPI.getReportDetail(reportId)
        return reportDetail
      } catch (error) {
        throw error
      }
    },

    /**
     * 处理举报
     */
    async processReport(reportId, action, reason = '') {
      this.loading.process = true
      try {
        const result = await safetyAPI.processReport(reportId, {
          action, // approved, rejected
          reason
        })

        // 更新本地列表中的状态
        const report = this.reportList.find(r => r.id === reportId)
        if (report) {
          report.status = action
          report.processedAt = new Date().toISOString()
          report.processReason = reason
        }

        // 更新统计数据
        await this.fetchModerationStats()

        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.process = false
      }
    },

    /**
     * 批量处理举报
     */
    async batchProcessReports(reportIds, action, reason = '') {
      this.loading.process = true
      try {
        const result = await safetyAPI.batchProcessReports({
          reportIds,
          action,
          reason
        })

        // 更新本地列表
        reportIds.forEach(reportId => {
          const report = this.reportList.find(r => r.id === reportId)
          if (report) {
            report.status = action
            report.processedAt = new Date().toISOString()
            report.processReason = reason
          }
        })

        // 更新统计数据
        await this.fetchModerationStats()

        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.process = false
      }
    },

    /**
     * 获取审核统计
     */
    async fetchModerationStats() {
      try {
        const stats = await safetyAPI.getModerationStats()
        this.moderationStats = stats
        return stats
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取审核统计（别名方法）
     */
    async getModerationStats() {
      return this.fetchModerationStats()
    },

    /**
     * 检测内容安全性
     */
    async checkContentSafety(content, type = 'text') {
      try {
        const result = await safetyAPI.checkContentSafety({
          content,
          type
        })
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取敏感词库
     */
    async fetchWordLibrary() {
      try {
        const library = await safetyAPI.getWordLibrary()
        this.wordLibrary = library
        return library
      } catch (error) {
        throw error
      }
    },

    /**
     * 更新敏感词库
     */
    async updateWordLibrary(library) {
      try {
        const result = await safetyAPI.updateWordLibrary(library)
        this.wordLibrary = { ...this.wordLibrary, ...library }
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取过滤配置
     */
    async fetchFilterConfig() {
      try {
        const config = await safetyAPI.getFilterConfig()
        this.filterConfig = config
        return config
      } catch (error) {
        throw error
      }
    },

    /**
     * 更新过滤配置
     */
    async updateFilterConfig(config) {
      try {
        const result = await safetyAPI.updateFilterConfig(config)
        this.filterConfig = { ...this.filterConfig, ...config }
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 拉黑用户
     */
    async blockUser(userId, reason = '') {
      try {
        const result = await safetyAPI.blockUser(userId, reason)
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 解除拉黑
     */
    async unblockUser(userId) {
      try {
        const result = await safetyAPI.unblockUser(userId)
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取拉黑列表
     */
    async fetchBlockedUsers(params = {}) {
      try {
        const result = await safetyAPI.getBlockedUsers(params)
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 删除违规内容
     */
    async deleteViolationContent(contentType, contentId, reason = '') {
      try {
        const result = await safetyAPI.deleteViolationContent({
          contentType,
          contentId,
          reason
        })
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取违规记录
     */
    async fetchViolationRecords(params = {}) {
      try {
        const result = await safetyAPI.getViolationRecords(params)
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 清空举报列表
     */
    clearReportList() {
      this.reportList = []
      this.pagination.current = 1
      this.pagination.hasMore = true
    },

    /**
     * 重置加载状态
     */
    resetLoadingState() {
      this.loading = {
        reports: false,
        submit: false,
        process: false
      }
    }
  }
})
