{"level":"info","message":"正在启动愿境应用后端服务...","service":"wish-backend","timestamp":"2025-07-17 19:02:23"}
{"level":"error","message":"数据库连接失败: Unknown database 'wish_db'","name":"SequelizeConnectionError","original":{"code":"ER_BAD_DB_ERROR","errno":1049,"sqlMessage":"Unknown database 'wish_db'","sqlState":"42000"},"parent":{"code":"ER_BAD_DB_ERROR","errno":1049,"sqlMessage":"Unknown database 'wish_db'","sqlState":"42000"},"service":"wish-backend","stack":"SequelizeConnectionError: Unknown database 'wish_db'\n    at ConnectionManager.connect (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\sequelize\\lib\\dialects\\mysql\\connection-manager.js:102:17)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async ConnectionManager._connect (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:222:24)\n    at async D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:174:32\n    at async ConnectionManager.getConnection (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\sequelize\\lib\\dialects\\abstract\\connection-manager.js:197:7)\n    at async D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\sequelize\\lib\\sequelize.js:305:26\n    at async Sequelize.authenticate (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\sequelize\\lib\\sequelize.js:457:5)\n    at async connectDatabase (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\database\\connection.js:45:5)\n    at async startServer (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\app.js:126:5)","timestamp":"2025-07-17 19:02:23"}
{"level":"info","message":"正在启动愿境应用后端服务...","service":"wish-backend","timestamp":"2025-07-17 19:03:15"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 19:03:15"}
{"level":"info","message":"正在启动愿境应用后端服务...","service":"wish-backend","timestamp":"2025-07-17 19:06:40"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 19:06:40"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 19:06:40"}
{"level":"info","message":"数据库模型初始化完成","service":"wish-backend","timestamp":"2025-07-17 19:06:40"}
{"level":"info","message":"数据库模型同步完成","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"level":"info","message":"数据库表结构同步完成","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"level":"info","message":"Redis未启用，使用内存缓存","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"level":"info","message":"Socket.IO实时通信服务启动成功","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"level":"info","message":"🚀 愿境应用后端服务启动成功！","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"level":"info","message":"📍 服务地址: http://localhost:3000","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"level":"info","message":"🌍 运行环境: development","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"level":"info","message":"💾 数据库: MySQL","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"level":"info","message":"🔄 缓存: 内存缓存","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"level":"info","message":"⚡ 实时通信: Socket.IO","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"level":"info","message":"📚 API文档: http://localhost:3000/api","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"level":"info","message":"❤️  健康检查: http://localhost:3000/health","service":"wish-backend","timestamp":"2025-07-17 19:06:43"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /favicon.ico 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /favicon.ico 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at logger (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\morgan\\index.js:144:5)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9","timestamp":"2025-07-17 19:06:55","url":"/favicon.ico","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/auth 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/auth 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:07:02","url":"/api/auth","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/auth 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/auth 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:07:02","url":"/api/auth","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"收到 SIGINT 信号，开始优雅关闭服务器...","service":"wish-backend","timestamp":"2025-07-17 19:11:30"}
{"level":"info","message":"正在启动愿境应用后端服务...","service":"wish-backend","timestamp":"2025-07-17 19:11:35"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"数据库模型初始化完成","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"数据库模型同步完成","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"数据库表结构同步完成","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"Redis未启用，使用内存缓存","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"Socket.IO实时通信服务启动成功","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"🚀 愿境应用后端服务启动成功！","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"📍 服务地址: http://localhost:3000","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"🌍 运行环境: development","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"💾 数据库: MySQL","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"🔄 缓存: 内存缓存","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"⚡ 实时通信: Socket.IO","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"📚 API文档: http://localhost:3000/api","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"level":"info","message":"❤️  健康检查: http://localhost:3000/health","service":"wish-backend","timestamp":"2025-07-17 19:11:36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/social 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/social 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:11:53","url":"/api/social","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/social 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/social 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:11:54","url":"/api/social","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/chat 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/chat 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:11:56","url":"/api/chat","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/chat 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/chat 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:11:56","url":"/api/chat","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/upload 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/upload 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:11:58","url":"/api/upload","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/upload 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/upload 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:11:58","url":"/api/upload","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/guardian 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/guardian 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:12:03","url":"/api/guardian","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/guardian 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/guardian 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:12:03","url":"/api/guardian","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/social 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/social 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:12:12","url":"/api/social","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/social 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/social 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:12:12","url":"/api/social","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/chat 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/chat 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:12:21","url":"/api/chat","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/chat 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/chat 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:12:21","url":"/api/chat","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/upload 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/upload 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:12:28","url":"/api/upload","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/upload 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/upload 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:646:15\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:265:14)\n    at Function.handle (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-17 19:12:28","url":"/api/upload","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 19:15:02"}
{"level":"info","message":"开始创建测试数据...","service":"wish-backend","timestamp":"2025-07-17 19:15:02"}
{"level":"info","message":"创建了 5 个测试用户","service":"wish-backend","timestamp":"2025-07-17 19:15:03"}
{"level":"info","message":"创建了 20 个测试心愿","service":"wish-backend","timestamp":"2025-07-17 19:15:04"}
{"level":"info","message":"创建了测试赐福数据","service":"wish-backend","timestamp":"2025-07-17 19:15:04"}
{"level":"info","message":"创建了测试消息数据","service":"wish-backend","timestamp":"2025-07-17 19:15:05"}
{"level":"info","message":"创建了测试关注关系","service":"wish-backend","timestamp":"2025-07-17 19:15:06"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/system/config 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/system/config 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-17 19:25:39","url":"/api/system/config","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/system/config 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/system/config 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-17 19:25:40","url":"/api/system/config","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/system/checkin-status 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/system/checkin-status 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-17 19:25:45","url":"/api/system/checkin-status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/system/checkin-status 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/system/checkin-status 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-17 19:25:45","url":"/api/system/checkin-status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/system/checkin-status 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/system/checkin-status 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-17 19:26:22","url":"/api/system/checkin-status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 路径 /api/system/checkin-status 不存在","method":"GET","service":"wish-backend","stack":"Error: 路径 /api/system/checkin-status 不存在\n    at notFoundHandler (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:135:17)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:807:7\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-rate-limit\\dist\\index.cjs:691:5","timestamp":"2025-07-17 19:26:22","url":"/api/system/checkin-status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"收到 SIGINT 信号，开始优雅关闭服务器...","service":"wish-backend","timestamp":"2025-07-17 20:07:44"}
{"level":"info","message":"正在启动愿境应用后端服务...","service":"wish-backend","timestamp":"2025-07-17 20:08:01"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"数据库模型初始化完成","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"数据库模型同步完成","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"数据库表结构同步完成","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"Redis未启用，使用内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"Socket.IO实时通信服务启动成功","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"🚀 愿境应用后端服务启动成功！","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"📍 服务地址: http://localhost:3000","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"🌍 运行环境: development","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"💾 数据库: MySQL","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"🔄 缓存: 内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"⚡ 实时通信: Socket.IO","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"📚 API文档: http://localhost:3000/api","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"level":"info","message":"❤️  健康检查: http://localhost:3000/health","service":"wish-backend","timestamp":"2025-07-17 20:08:02"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 参数验证失败","method":"GET","service":"wish-backend","stack":"Error: 参数验证失败\n    at handleValidationErrors (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:161:19)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-17 20:15:45","url":"/api/users/checkin-status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 参数验证失败","method":"GET","service":"wish-backend","stack":"Error: 参数验证失败\n    at handleValidationErrors (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:161:19)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-17 20:17:46","url":"/api/users/checkin-status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 参数验证失败","method":"GET","service":"wish-backend","stack":"Error: 参数验证失败\n    at handleValidationErrors (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:161:19)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-17 20:20:45","url":"/api/users/checkin-status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 参数验证失败","method":"GET","service":"wish-backend","stack":"Error: 参数验证失败\n    at handleValidationErrors (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:161:19)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-17 20:20:45","url":"/api/users/undefined/stats","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 参数验证失败","method":"GET","service":"wish-backend","stack":"Error: 参数验证失败\n    at handleValidationErrors (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:161:19)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-17 20:20:45","url":"/api/users/checkin-status","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 参数验证失败","method":"GET","service":"wish-backend","stack":"Error: 参数验证失败\n    at handleValidationErrors (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:161:19)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-17 20:20:49","url":"/api/users/checkin-status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 参数验证失败","method":"GET","service":"wish-backend","stack":"Error: 参数验证失败\n    at handleValidationErrors (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:161:19)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-17 20:20:51","url":"/api/users/checkin-status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"level":"info","message":"收到 SIGINT 信号，开始优雅关闭服务器...","service":"wish-backend","timestamp":"2025-07-17 20:21:05"}
{"level":"info","message":"正在启动愿境应用后端服务...","service":"wish-backend","timestamp":"2025-07-17 20:21:15"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:21:15"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:21:15"}
{"level":"info","message":"数据库模型初始化完成","service":"wish-backend","timestamp":"2025-07-17 20:21:15"}
{"level":"info","message":"数据库模型同步完成","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"level":"info","message":"数据库表结构同步完成","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"level":"info","message":"Redis未启用，使用内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"level":"info","message":"Socket.IO实时通信服务启动成功","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"level":"info","message":"🚀 愿境应用后端服务启动成功！","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"level":"info","message":"📍 服务地址: http://localhost:3000","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"level":"info","message":"🌍 运行环境: development","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"level":"info","message":"💾 数据库: MySQL","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"level":"info","message":"🔄 缓存: 内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"level":"info","message":"⚡ 实时通信: Socket.IO","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"level":"info","message":"📚 API文档: http://localhost:3000/api","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"level":"info","message":"❤️  健康检查: http://localhost:3000/health","service":"wish-backend","timestamp":"2025-07-17 20:21:16"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 参数验证失败","method":"GET","service":"wish-backend","stack":"Error: 参数验证失败\n    at handleValidationErrors (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:161:19)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-17 20:21:20","url":"/api/users/checkin-status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"level":"info","message":"收到 SIGINT 信号，开始优雅关闭服务器...","service":"wish-backend","timestamp":"2025-07-17 20:25:58"}
{"level":"info","message":"正在启动愿境应用后端服务...","service":"wish-backend","timestamp":"2025-07-17 20:25:59"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:25:59"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:25:59"}
{"level":"info","message":"数据库模型初始化完成","service":"wish-backend","timestamp":"2025-07-17 20:25:59"}
{"level":"info","message":"数据库模型同步完成","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"数据库表结构同步完成","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"Redis未启用，使用内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"Socket.IO实时通信服务启动成功","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"🚀 愿境应用后端服务启动成功！","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"📍 服务地址: http://localhost:3000","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"🌍 运行环境: development","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"💾 数据库: MySQL","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"🔄 缓存: 内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"⚡ 实时通信: Socket.IO","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"📚 API文档: http://localhost:3000/api","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"❤️  健康检查: http://localhost:3000/health","service":"wish-backend","timestamp":"2025-07-17 20:26:00"}
{"level":"info","message":"正在启动愿境应用后端服务...","service":"wish-backend","timestamp":"2025-07-17 20:26:05"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:26:05"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:26:05"}
{"level":"info","message":"数据库模型初始化完成","service":"wish-backend","timestamp":"2025-07-17 20:26:05"}
{"level":"info","message":"数据库模型同步完成","service":"wish-backend","timestamp":"2025-07-17 20:26:06"}
{"ip":"::1","level":"error","message":"错误处理中间件捕获错误: 参数验证失败","method":"GET","service":"wish-backend","stack":"Error: 参数验证失败\n    at handleValidationErrors (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\src\\middleware\\errorHandler.js:161:19)\n    at Layer.handle [as handle_request] (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at middleware (D:\\代码\\datedu-hw\\wish-dev\\wish-backend\\node_modules\\express-validator\\lib\\middlewares\\check.js:16:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-17 20:26:33","url":"/api/users/checkin-status","userAgent":"Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1"}
{"level":"info","message":"收到 SIGINT 信号，开始优雅关闭服务器...","service":"wish-backend","timestamp":"2025-07-17 20:26:40"}
{"level":"info","message":"正在启动愿境应用后端服务...","service":"wish-backend","timestamp":"2025-07-17 20:29:16"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"数据库模型初始化完成","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"数据库模型同步完成","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"数据库表结构同步完成","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"Redis未启用，使用内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"Socket.IO实时通信服务启动成功","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"🚀 愿境应用后端服务启动成功！","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"📍 服务地址: http://localhost:3000","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"🌍 运行环境: development","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"💾 数据库: MySQL","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"🔄 缓存: 内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"⚡ 实时通信: Socket.IO","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"📚 API文档: http://localhost:3000/api","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"❤️  健康检查: http://localhost:3000/health","service":"wish-backend","timestamp":"2025-07-17 20:29:17"}
{"level":"info","message":"正在启动愿境应用后端服务...","service":"wish-backend","timestamp":"2025-07-17 20:30:29"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"数据库模型初始化完成","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"数据库模型同步完成","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"数据库表结构同步完成","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"Redis未启用，使用内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"Socket.IO实时通信服务启动成功","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"🚀 愿境应用后端服务启动成功！","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"📍 服务地址: http://localhost:3000","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"🌍 运行环境: development","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"💾 数据库: MySQL","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"🔄 缓存: 内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"⚡ 实时通信: Socket.IO","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"📚 API文档: http://localhost:3000/api","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"❤️  健康检查: http://localhost:3000/health","service":"wish-backend","timestamp":"2025-07-17 20:30:30"}
{"level":"info","message":"正在启动愿境应用后端服务...","service":"wish-backend","timestamp":"2025-07-17 20:31:30"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:31:30"}
{"level":"info","message":"MySQL数据库连接成功","service":"wish-backend","timestamp":"2025-07-17 20:31:30"}
{"level":"info","message":"数据库模型初始化完成","service":"wish-backend","timestamp":"2025-07-17 20:31:30"}
{"level":"info","message":"数据库模型同步完成","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
{"level":"info","message":"数据库表结构同步完成","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
{"level":"info","message":"Redis未启用，使用内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
{"level":"info","message":"Socket.IO实时通信服务启动成功","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
{"level":"info","message":"🚀 愿境应用后端服务启动成功！","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
{"level":"info","message":"📍 服务地址: http://localhost:3000","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
{"level":"info","message":"🌍 运行环境: development","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
{"level":"info","message":"💾 数据库: MySQL","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
{"level":"info","message":"🔄 缓存: 内存缓存","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
{"level":"info","message":"⚡ 实时通信: Socket.IO","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
{"level":"info","message":"📚 API文档: http://localhost:3000/api","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
{"level":"info","message":"❤️  健康检查: http://localhost:3000/health","service":"wish-backend","timestamp":"2025-07-17 20:31:31"}
