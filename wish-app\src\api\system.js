/**
 * 系统相关API
 */

import request from './request'

export const systemAPI = {
  // 每日签到
  dailyCheckIn() {
    return request.post('/users/checkin')
  },
  
  // 获取签到状态
  getCheckInStatus() {
    return request.get('/users/checkin-status')
  },
  
  // 举报内容
  reportContent(data) {
    return request.post('/system/report', data)
  },
  
  // 获取系统通知
  getNotifications(params = {}) {
    const defaultParams = {
      page: 1,
      pageSize: 20,
      type: 'all'
    }
    return request.get('/system/notifications', { ...defaultParams, ...params })
  },
  
  // 获取应用配置
  getAppConfig() {
    // 返回模拟的应用配置，避免调用不存在的后端接口
    return Promise.resolve({
      data: {
        version: '1.0.0',
        updateRequired: false,
        updateUrl: '',
        features: {
          chat: true,
          guardian: true,
          social: true
        },
        maintenance: false
      }
    })
  },
  
  // 获取标签列表
  getTags() {
    // 返回模拟的标签列表
    return Promise.resolve({
      data: [
        { id: 1, name: '健康', color: '#4CAF50' },
        { id: 2, name: '事业', color: '#2196F3' },
        { id: 3, name: '爱情', color: '#E91E63' },
        { id: 4, name: '家庭', color: '#FF9800' },
        { id: 5, name: '学习', color: '#9C27B0' },
        { id: 6, name: '财富', color: '#FFC107' },
        { id: 7, name: '旅行', color: '#00BCD4' },
        { id: 8, name: '其他', color: '#607D8B' }
      ]
    })
  },
  
  // 意见反馈
  submitFeedback(data) {
    return request.post('/system/feedback', data)
  },

  // 检查应用更新
  checkAppUpdate() {
    // 返回模拟的更新检查结果
    return Promise.resolve({
      data: {
        hasUpdate: false,
        version: '1.0.0',
        updateUrl: '',
        updateDescription: '',
        forceUpdate: false
      }
    })
  },

  // 获取系统状态
  getSystemStatus() {
    return Promise.resolve({
      data: {
        status: 'normal',
        maintenance: false,
        message: ''
      }
    })
  }
}

export default systemAPI
