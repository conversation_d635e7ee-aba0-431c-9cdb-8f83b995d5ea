/**
 * 社交功能状态管理
 * 管理排行榜、评论、私信等社交功能
 */

import { defineStore } from 'pinia'
import { socialAPI } from '@/api'

export const useSocialStore = defineStore('social', {
  state: () => ({
    // 排行榜数据
    leaderboard: {
      merit: {
        total: [],
        daily: [],
        weekly: [],
        monthly: []
      },
      wishPower: {
        total: [],
        daily: [],
        weekly: [],
        monthly: []
      }
    },
    // 评论列表
    comments: {},
    // 消息列表
    messages: [],
    // 对话列表
    conversations: [],
    // 未读消息数
    unreadCount: 0,
    // 加载状态
    loading: {
      leaderboard: false,
      comments: false,
      messages: false,
      send: false
    }
  }),

  getters: {
    // 获取指定类型和周期的排行榜
    getLeaderboard: (state) => (type, period) => {
      return state.leaderboard[type]?.[period] || []
    },
    
    // 获取指定目标的评论
    getComments: (state) => (targetId, targetType) => {
      const key = `${targetType}_${targetId}`
      return state.comments[key] || []
    },
    
    // 获取系统消息
    systemMessages: (state) => {
      return state.messages.filter(msg => msg.type === 'system')
    },
    
    // 获取用户消息
    userMessages: (state) => {
      return state.messages.filter(msg => msg.type === 'user')
    },
    
    // 获取未读消息
    unreadMessages: (state) => {
      return state.messages.filter(msg => !msg.isRead)
    }
  },

  actions: {
    /**
     * 获取排行榜
     */
    async fetchLeaderboard(type = 'merit', period = 'total') {
      this.loading.leaderboard = true
      try {
        const result = await socialAPI.getLeaderboard(type, period)
        
        if (!this.leaderboard[type]) {
          this.leaderboard[type] = {}
        }
        this.leaderboard[type][period] = result
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.leaderboard = false
      }
    },

    /**
     * 获取所有排行榜数据
     */
    async fetchAllLeaderboards() {
      const types = ['merit', 'wishPower']
      const periods = ['total', 'daily', 'weekly', 'monthly']
      
      const promises = []
      types.forEach(type => {
        periods.forEach(period => {
          promises.push(this.fetchLeaderboard(type, period))
        })
      })
      
      try {
        await Promise.all(promises)
      } catch (error) {
        console.error('获取排行榜失败:', error)
      }
    },

    /**
     * 获取评论列表
     */
    async fetchComments(targetId, targetType, params = {}) {
      this.loading.comments = true
      try {
        const result = await socialAPI.getComments(targetId, targetType, params)
        
        const key = `${targetType}_${targetId}`
        this.comments[key] = result.list
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.comments = false
      }
    },

    /**
     * 发表评论
     */
    async createComment(commentData) {
      try {
        const result = await socialAPI.createComment(commentData)
        
        // 添加到对应的评论列表
        const key = `${commentData.targetType}_${commentData.targetId}`
        if (this.comments[key]) {
          this.comments[key].unshift(result)
        }
        
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 删除评论
     */
    async deleteComment(commentId, targetId, targetType) {
      try {
        await socialAPI.deleteComment(commentId)
        
        // 从评论列表中移除
        const key = `${targetType}_${targetId}`
        if (this.comments[key]) {
          this.comments[key] = this.comments[key].filter(
            comment => comment.id !== commentId
          )
        }
        
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取消息列表
     */
    async fetchMessages(params = {}) {
      this.loading.messages = true
      try {
        const result = await socialAPI.getMessages(params)
        this.messages = result.list
        
        // 计算未读消息数
        this.unreadCount = result.list.filter(msg => !msg.isRead).length
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.messages = false
      }
    },

    /**
     * 获取对话列表
     */
    async fetchConversations(params = {}) {
      this.loading.messages = true
      try {
        const result = await socialAPI.getConversations(params)
        this.conversations = result.list
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.messages = false
      }
    },

    /**
     * 发送消息
     */
    async sendMessage(messageData) {
      this.loading.send = true
      try {
        const result = await socialAPI.sendMessage(messageData)
        
        // 添加到消息列表
        this.messages.unshift(result)
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.send = false
      }
    },

    /**
     * 标记消息已读
     */
    async markMessageRead(messageId) {
      try {
        await socialAPI.markMessageRead(messageId)
        
        // 更新本地消息状态
        const message = this.messages.find(msg => msg.id === messageId)
        if (message) {
          message.isRead = true
          this.unreadCount = Math.max(0, this.unreadCount - 1)
        }
        
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 批量标记消息已读
     */
    async markAllMessagesRead() {
      try {
        const unreadMessages = this.messages.filter(msg => !msg.isRead)
        
        const promises = unreadMessages.map(msg => 
          this.markMessageRead(msg.id)
        )
        
        await Promise.all(promises)
        
        this.unreadCount = 0
        
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 清空评论缓存
     */
    clearComments(targetId, targetType) {
      const key = `${targetType}_${targetId}`
      delete this.comments[key]
    },

    /**
     * 清空消息列表
     */
    clearMessages() {
      this.messages = []
      this.conversations = []
      this.unreadCount = 0
    }
  }
})
