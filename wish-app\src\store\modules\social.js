/**
 * 社交功能状态管理
 * 管理排行榜、评论、私信等社交功能
 */

import { defineStore } from 'pinia'
import { socialAPI } from '@/api'

export const useSocialStore = defineStore('social', {
  state: () => ({
    // 排行榜数据
    leaderboard: {
      merit: {
        total: [],
        daily: [],
        weekly: [],
        monthly: []
      },
      wishPower: {
        total: [],
        daily: [],
        weekly: [],
        monthly: []
      }
    },
    // 评论列表
    comments: {},
    // 评论分页信息
    commentPagination: {
      current: 1,
      pageSize: 20,
      hasMore: true
    },
    // 消息列表
    messages: [],
    // 对话列表
    conversations: [],
    // 消息分页信息
    messagePagination: {
      current: 1,
      pageSize: 20,
      hasMore: true
    },
    // 未读消息数
    unreadCount: 0,
    // 加载状态
    loading: {
      leaderboard: false,
      comments: false,
      messages: false,
      send: false
    }
  }),

  getters: {
    // 获取指定类型和周期的排行榜
    getLeaderboard: (state) => (type, period) => {
      return state.leaderboard[type]?.[period] || []
    },
    
    // 获取指定目标的评论
    getComments: (state) => (targetId, targetType) => {
      const key = `${targetType}_${targetId}`
      return state.comments[key] || []
    },
    
    // 获取系统消息
    systemMessages: (state) => {
      return state.messages.filter(msg => msg.type === 'system')
    },
    
    // 获取用户消息
    userMessages: (state) => {
      return state.messages.filter(msg => msg.type === 'user')
    },
    
    // 获取未读消息
    unreadMessages: (state) => {
      return state.messages.filter(msg => !msg.isRead)
    }
  },

  actions: {
    /**
     * 获取排行榜
     */
    async fetchLeaderboard(type = 'merit', period = 'total') {
      this.loading.leaderboard = true
      try {
        const result = await socialAPI.getLeaderboard(type, period)
        
        if (!this.leaderboard[type]) {
          this.leaderboard[type] = {}
        }
        this.leaderboard[type][period] = result
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.leaderboard = false
      }
    },

    /**
     * 获取所有排行榜数据
     */
    async fetchAllLeaderboards() {
      const types = ['merit', 'wishPower']
      const periods = ['total', 'daily', 'weekly', 'monthly']
      
      const promises = []
      types.forEach(type => {
        periods.forEach(period => {
          promises.push(this.fetchLeaderboard(type, period))
        })
      })
      
      try {
        await Promise.all(promises)
      } catch (error) {
        console.error('获取排行榜失败:', error)
      }
    },

    /**
     * 获取评论列表
     */
    async fetchComments(targetId, targetType, params = {}) {
      this.loading.comments = true
      try {
        const result = await socialAPI.getComments(targetId, targetType, params)
        
        const key = `${targetType}_${targetId}`
        this.comments[key] = result.list
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.comments = false
      }
    },

    /**
     * 发表评论
     */
    async createComment(commentData) {
      try {
        const result = await socialAPI.createComment(commentData)
        
        // 添加到对应的评论列表
        const key = `${commentData.targetType}_${commentData.targetId}`
        if (this.comments[key]) {
          this.comments[key].unshift(result)
        }
        
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 点赞评论
     */
    async likeComment(commentId) {
      try {
        await socialAPI.likeComment(commentId)
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 取消点赞评论
     */
    async unlikeComment(commentId) {
      try {
        await socialAPI.unlikeComment(commentId)
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 删除评论
     */
    async deleteComment(commentId, targetId, targetType) {
      try {
        await socialAPI.deleteComment(commentId)

        // 从评论列表中移除
        const key = `${targetType}_${targetId}`
        if (this.comments[key]) {
          this.comments[key] = this.comments[key].filter(
            comment => comment.id !== commentId
          )
        }

        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 感谢赐福
     */
    async thankBlessing(blessingId, feedback = '') {
      try {
        await socialAPI.thankBlessing(blessingId, feedback)
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取消息列表
     */
    async fetchMessages(params = {}) {
      this.loading.messages = true
      try {
        const result = await socialAPI.getMessages(params)
        this.messages = result.list
        
        // 计算未读消息数
        this.unreadCount = result.list.filter(msg => !msg.isRead).length
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.messages = false
      }
    },

    /**
     * 获取对话列表
     */
    async fetchConversations(params = {}, refresh = false) {
      this.loading.messages = true
      try {
        if (refresh) {
          this.pagination.current = 1
          this.pagination.hasMore = true
        }

        const requestParams = {
          ...params,
          page: this.pagination.current,
          pageSize: this.pagination.pageSize
        }

        const result = await socialAPI.getConversations(requestParams)

        if (refresh) {
          this.conversations = result.list || []
        } else {
          this.conversations.push(...(result.list || []))
        }

        this.pagination.current = result.pagination?.current || this.pagination.current
        this.pagination.hasMore = result.pagination?.hasMore || false

        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.messages = false
      }
    },

    /**
     * 创建对话
     */
    async createConversation(userId) {
      try {
        const conversation = await socialAPI.createConversation(userId)

        // 添加到对话列表
        const existingIndex = this.conversations.findIndex(c => c.user.id === userId)
        if (existingIndex > -1) {
          this.conversations.splice(existingIndex, 1)
        }
        this.conversations.unshift(conversation)

        return conversation
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取消息列表
     */
    async fetchMessages(params = {}, refresh = false) {
      this.loading.messages = true
      try {
        if (refresh) {
          this.messagePagination.current = 1
          this.messagePagination.hasMore = true
        }

        const requestParams = {
          ...params,
          page: this.messagePagination.current,
          pageSize: this.messagePagination.pageSize
        }

        const result = await socialAPI.getMessages(requestParams)

        if (refresh) {
          this.messages = result.list || []
        } else {
          this.messages.unshift(...(result.list || []))
        }

        this.messagePagination.current = result.pagination?.current || this.messagePagination.current
        this.messagePagination.hasMore = result.pagination?.hasMore || false

        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.messages = false
      }
    },

    /**
     * 发送消息
     */
    async sendMessage(messageData) {
      this.loading.send = true
      try {
        const result = await socialAPI.sendMessage(messageData)

        // 添加到消息列表
        this.messages.push(result)

        // 更新对话列表中的最后一条消息
        const conversation = this.conversations.find(c => c.id === messageData.conversationId)
        if (conversation) {
          conversation.lastMessage = result
          conversation.lastMessageTime = result.createdAt

          // 移动到列表顶部
          const index = this.conversations.indexOf(conversation)
          this.conversations.splice(index, 1)
          this.conversations.unshift(conversation)
        }

        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.send = false
      }
    },

    /**
     * 上传消息图片
     */
    async uploadMessageImage(filePath) {
      try {
        const result = await socialAPI.uploadMessageImage(filePath)
        return result.url
      } catch (error) {
        throw error
      }
    },

    /**
     * 搜索用户
     */
    async searchUsers(keyword) {
      try {
        const result = await socialAPI.searchUsers(keyword)
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取用户信息
     */
    async getUserInfo(userId) {
      try {
        const userInfo = await socialAPI.getUserInfo(userId)
        return userInfo
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取消息数量统计
     */
    async getMessageCounts() {
      try {
        const counts = await socialAPI.getMessageCounts()
        return counts
      } catch (error) {
        throw error
      }
    },

    /**
     * 置顶/取消置顶对话
     */
    async toggleConversationPin(conversationId) {
      try {
        await socialAPI.toggleConversationPin(conversationId)
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 免打扰/取消免打扰对话
     */
    async toggleConversationMute(conversationId) {
      try {
        await socialAPI.toggleConversationMute(conversationId)
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 标记对话为已读
     */
    async markConversationRead(conversationId) {
      try {
        await socialAPI.markConversationRead(conversationId)
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 标记消息为已读
     */
    async markMessagesAsRead(conversationId) {
      try {
        await socialAPI.markMessagesAsRead(conversationId)
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 删除对话
     */
    async deleteConversation(conversationId) {
      try {
        await socialAPI.deleteConversation(conversationId)

        // 从对话列表中移除
        this.conversations = this.conversations.filter(c => c.id !== conversationId)

        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 清空消息
     */
    async clearMessages(conversationId) {
      try {
        await socialAPI.clearMessages(conversationId)

        // 清空本地消息列表
        this.messages = []

        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 拉黑用户
     */
    async blockUser(userId) {
      try {
        await socialAPI.blockUser(userId)
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 标记消息已读
     */
    async markMessageRead(messageId) {
      try {
        await socialAPI.markMessageRead(messageId)
        
        // 更新本地消息状态
        const message = this.messages.find(msg => msg.id === messageId)
        if (message) {
          message.isRead = true
          this.unreadCount = Math.max(0, this.unreadCount - 1)
        }
        
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 批量标记消息已读
     */
    async markAllMessagesRead() {
      try {
        const unreadMessages = this.messages.filter(msg => !msg.isRead)
        
        const promises = unreadMessages.map(msg => 
          this.markMessageRead(msg.id)
        )
        
        await Promise.all(promises)
        
        this.unreadCount = 0
        
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 清空评论缓存
     */
    clearComments(targetId, targetType) {
      const key = `${targetType}_${targetId}`
      delete this.comments[key]
    },

    /**
     * 清空消息列表
     */
    clearMessages() {
      this.messages = []
      this.conversations = []
      this.unreadCount = 0
    }
  }
})
