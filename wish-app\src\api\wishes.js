/**
 * 心愿相关API
 */

import request from './request'
import { API_CONFIG } from '@/config'

const { endpoints } = API_CONFIG

export const wishesAPI = {
  // 获取心愿列表
  getWishList(params = {}) {
    const defaultParams = {
      page: 1,
      limit: 20,
      type: '',
      category: '',
      sortBy: 'created_at',
      sortOrder: 'desc',
      keyword: ''
    }
    return request.get(endpoints.wishes.list, { ...defaultParams, ...params })
  },

  // 获取推荐心愿
  getRecommendedWishes(limit = 20) {
    return request.get(endpoints.wishes.recommended, { limit })
  },

  // 获取心愿详情
  getWishDetail(wishId) {
    return request.get(`${endpoints.wishes.detail}/${wishId}`)
  },

  // 创建心愿
  createWish(data) {
    return request.post(endpoints.wishes.create, data)
  },

  // 更新心愿
  updateWish(wishId, data) {
    return request.put(`${endpoints.wishes.update}/${wishId}`, data)
  },

  // 删除心愿
  deleteWish(wishId) {
    return request.delete(`${endpoints.wishes.delete}/${wishId}`)
  },

  // 点赞/取消点赞心愿
  toggleLike(wishId) {
    return request.post(`${endpoints.wishes.like}/${wishId}/like`)
  }
}

export default wishesAPI
