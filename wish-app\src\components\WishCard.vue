<!--
  愿境卡片组件
  用于展示心愿、赐福等内容的卡片容器
-->
<template>
  <view 
    class="wish-card"
    :class="[
      `wish-card--${shadow}`,
      {
        'wish-card--hover': hover,
        'wish-card--clickable': clickable
      }
    ]"
    @click="handleClick"
  >
    <!-- 卡片头部 -->
    <view v-if="$slots.header" class="wish-card__header">
      <slot name="header"></slot>
    </view>
    
    <!-- 卡片内容 -->
    <view class="wish-card__body">
      <slot></slot>
    </view>
    
    <!-- 卡片底部 -->
    <view v-if="$slots.footer" class="wish-card__footer">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script>
export default {
  name: 'WishCard',
  props: {
    // 阴影程度
    shadow: {
      type: String,
      default: 'light',
      validator: (value) => ['none', 'light', 'medium', 'heavy'].includes(value)
    },
    // 是否可点击
    clickable: {
      type: Boolean,
      default: false
    },
    // 悬停效果
    hover: {
      type: Boolean,
      default: false
    }
  },
  
  methods: {
    handleClick(event) {
      if (this.clickable) {
        this.$emit('click', event)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.wish-card {
  background-color: $wish-bg-card;
  border-radius: $wish-radius-lg;
  overflow: hidden;
  transition: all 0.3s ease;
  
  // 阴影样式
  &--none {
    box-shadow: none;
  }
  
  &--light {
    box-shadow: $wish-shadow-sm;
  }
  
  &--medium {
    box-shadow: $wish-shadow-md;
  }
  
  &--heavy {
    box-shadow: $wish-shadow-lg;
  }
  
  // 可点击样式
  &--clickable {
    cursor: pointer;
    
    &:active {
      transform: scale(0.98);
    }
  }
  
  // 悬停效果
  &--hover:hover {
    transform: translateY(-4rpx);
    box-shadow: $wish-shadow-lg;
  }
}

.wish-card__header {
  padding: $wish-spacing-md $wish-spacing-md 0;
  border-bottom: 2rpx solid $wish-border-light;
  margin-bottom: $wish-spacing-md;
}

.wish-card__body {
  padding: $wish-spacing-md;
}

.wish-card__footer {
  padding: 0 $wish-spacing-md $wish-spacing-md;
  border-top: 2rpx solid $wish-border-light;
  margin-top: $wish-spacing-md;
  padding-top: $wish-spacing-md;
}
</style>
