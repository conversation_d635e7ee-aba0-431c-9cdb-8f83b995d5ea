/**
 * 内容过滤工具类
 * 提供关键词过滤、敏感词检测等功能
 */

class ContentFilter {
  constructor() {
    // 敏感词库
    this.sensitiveWords = new Set()
    // 违禁词库
    this.bannedWords = new Set()
    // 垃圾信息模式
    this.spamPatterns = []
    // 初始化词库
    this.initWordLibrary()
  }

  /**
   * 初始化词库
   */
  initWordLibrary() {
    // 基础敏感词（示例，实际应用中应从服务器获取）
    const basicSensitiveWords = [
      // 政治敏感词
      '政治敏感词1', '政治敏感词2',
      // 色情词汇
      '色情词汇1', '色情词汇2',
      // 暴力词汇
      '暴力词汇1', '暴力词汇2',
      // 赌博词汇
      '赌博', '博彩', '彩票',
      // 诈骗词汇
      '诈骗', '骗钱', '传销'
    ]

    // 违禁词（完全禁止）
    const basicBannedWords = [
      '违法', '犯罪', '毒品'
    ]

    // 垃圾信息模式
    const basicSpamPatterns = [
      /微信.*加.*好友/gi,
      /QQ.*群.*\d+/gi,
      /联系.*电话.*\d+/gi,
      /代.*办.*证.*件/gi,
      /贷.*款.*无.*抵.*押/gi
    ]

    this.sensitiveWords = new Set(basicSensitiveWords)
    this.bannedWords = new Set(basicBannedWords)
    this.spamPatterns = basicSpamPatterns
  }

  /**
   * 检测文本是否包含敏感内容
   * @param {string} text - 待检测文本
   * @returns {Object} 检测结果
   */
  detectSensitiveContent(text) {
    if (!text || typeof text !== 'string') {
      return {
        isSafe: true,
        level: 'safe',
        issues: []
      }
    }

    const issues = []
    let level = 'safe'

    // 检测违禁词
    const bannedWordsFound = this.findBannedWords(text)
    if (bannedWordsFound.length > 0) {
      issues.push({
        type: 'banned',
        words: bannedWordsFound,
        severity: 'high'
      })
      level = 'banned'
    }

    // 检测敏感词
    const sensitiveWordsFound = this.findSensitiveWords(text)
    if (sensitiveWordsFound.length > 0) {
      issues.push({
        type: 'sensitive',
        words: sensitiveWordsFound,
        severity: 'medium'
      })
      if (level === 'safe') {
        level = 'sensitive'
      }
    }

    // 检测垃圾信息
    const spamDetected = this.detectSpam(text)
    if (spamDetected.length > 0) {
      issues.push({
        type: 'spam',
        patterns: spamDetected,
        severity: 'medium'
      })
      if (level === 'safe') {
        level = 'spam'
      }
    }

    return {
      isSafe: level === 'safe',
      level,
      issues,
      originalText: text
    }
  }

  /**
   * 查找违禁词
   * @param {string} text - 文本
   * @returns {Array} 找到的违禁词
   */
  findBannedWords(text) {
    const found = []
    const lowerText = text.toLowerCase()

    for (const word of this.bannedWords) {
      if (lowerText.includes(word.toLowerCase())) {
        found.push(word)
      }
    }

    return found
  }

  /**
   * 查找敏感词
   * @param {string} text - 文本
   * @returns {Array} 找到的敏感词
   */
  findSensitiveWords(text) {
    const found = []
    const lowerText = text.toLowerCase()

    for (const word of this.sensitiveWords) {
      if (lowerText.includes(word.toLowerCase())) {
        found.push(word)
      }
    }

    return found
  }

  /**
   * 检测垃圾信息
   * @param {string} text - 文本
   * @returns {Array} 匹配的垃圾信息模式
   */
  detectSpam(text) {
    const found = []

    for (const pattern of this.spamPatterns) {
      if (pattern.test(text)) {
        found.push(pattern.source)
      }
    }

    return found
  }

  /**
   * 过滤敏感内容
   * @param {string} text - 原始文本
   * @param {string} replacement - 替换字符，默认为*
   * @returns {string} 过滤后的文本
   */
  filterSensitiveContent(text, replacement = '*') {
    if (!text || typeof text !== 'string') {
      return text
    }

    let filteredText = text

    // 替换违禁词
    for (const word of this.bannedWords) {
      const regex = new RegExp(word, 'gi')
      filteredText = filteredText.replace(regex, replacement.repeat(word.length))
    }

    // 替换敏感词
    for (const word of this.sensitiveWords) {
      const regex = new RegExp(word, 'gi')
      filteredText = filteredText.replace(regex, replacement.repeat(word.length))
    }

    return filteredText
  }

  /**
   * 检测文本风险等级
   * @param {string} text - 文本
   * @returns {string} 风险等级：safe, low, medium, high
   */
  assessRiskLevel(text) {
    const detection = this.detectSensitiveContent(text)

    if (detection.level === 'banned') {
      return 'high'
    }

    if (detection.issues.length === 0) {
      return 'safe'
    }

    const hasHighSeverity = detection.issues.some(issue => issue.severity === 'high')
    if (hasHighSeverity) {
      return 'high'
    }

    const mediumSeverityCount = detection.issues.filter(issue => issue.severity === 'medium').length
    if (mediumSeverityCount >= 2) {
      return 'medium'
    }

    return 'low'
  }

  /**
   * 更新词库
   * @param {Object} wordLibrary - 新的词库
   */
  updateWordLibrary(wordLibrary) {
    if (wordLibrary.sensitiveWords) {
      this.sensitiveWords = new Set(wordLibrary.sensitiveWords)
    }

    if (wordLibrary.bannedWords) {
      this.bannedWords = new Set(wordLibrary.bannedWords)
    }

    if (wordLibrary.spamPatterns) {
      this.spamPatterns = wordLibrary.spamPatterns.map(pattern => new RegExp(pattern, 'gi'))
    }
  }

  /**
   * 添加敏感词
   * @param {Array|string} words - 要添加的敏感词
   */
  addSensitiveWords(words) {
    const wordsArray = Array.isArray(words) ? words : [words]
    wordsArray.forEach(word => this.sensitiveWords.add(word))
  }

  /**
   * 添加违禁词
   * @param {Array|string} words - 要添加的违禁词
   */
  addBannedWords(words) {
    const wordsArray = Array.isArray(words) ? words : [words]
    wordsArray.forEach(word => this.bannedWords.add(word))
  }

  /**
   * 移除敏感词
   * @param {Array|string} words - 要移除的敏感词
   */
  removeSensitiveWords(words) {
    const wordsArray = Array.isArray(words) ? words : [words]
    wordsArray.forEach(word => this.sensitiveWords.delete(word))
  }

  /**
   * 移除违禁词
   * @param {Array|string} words - 要移除的违禁词
   */
  removeBannedWords(words) {
    const wordsArray = Array.isArray(words) ? words : [words]
    wordsArray.forEach(word => this.bannedWords.delete(word))
  }

  /**
   * 获取词库统计
   * @returns {Object} 词库统计信息
   */
  getLibraryStats() {
    return {
      sensitiveWordsCount: this.sensitiveWords.size,
      bannedWordsCount: this.bannedWords.size,
      spamPatternsCount: this.spamPatterns.length
    }
  }
}

// 创建全局实例
const contentFilter = new ContentFilter()

export default contentFilter

/**
 * 快捷方法：检测内容是否安全
 * @param {string} text - 待检测文本
 * @returns {boolean} 是否安全
 */
export const isContentSafe = (text) => {
  return contentFilter.detectSensitiveContent(text).isSafe
}

/**
 * 快捷方法：过滤敏感内容
 * @param {string} text - 原始文本
 * @param {string} replacement - 替换字符
 * @returns {string} 过滤后的文本
 */
export const filterContent = (text, replacement = '*') => {
  return contentFilter.filterSensitiveContent(text, replacement)
}

/**
 * 快捷方法：评估风险等级
 * @param {string} text - 文本
 * @returns {string} 风险等级
 */
export const assessContentRisk = (text) => {
  return contentFilter.assessRiskLevel(text)
}
