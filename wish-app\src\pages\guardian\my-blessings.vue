<!--
  愿境我的赐福页面
  展示守护者发出的所有赐福和收到的反馈
-->
<template>
  <view class="my-blessings-page">
    <!-- 统计信息 -->
    <view class="stats-header">
      <view class="stat-item">
        <text class="stat-number">{{ totalBlessings }}</text>
        <text class="stat-label">总赐福</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-number">{{ thankedBlessings }}</text>
        <text class="stat-label">收到感谢</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-number">{{ userStore.userStats.meritPoints }}</text>
        <text class="stat-label">功德值</text>
      </view>
    </view>
    
    <!-- 状态筛选 -->
    <view class="status-filter">
      <view 
        class="filter-item"
        :class="{ 'filter-item--active': statusFilter === status.value }"
        v-for="status in statusOptions"
        :key="status.value"
        @click="changeStatusFilter(status.value)"
      >
        <text class="filter-text">{{ status.label }}</text>
      </view>
    </view>
    
    <!-- 赐福列表 -->
    <scroll-view 
      class="blessing-list"
      scroll-y
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="blessing-items">
        <wish-card
          v-for="blessing in myBlessingList"
          :key="blessing.id"
          class="blessing-item"
          shadow="light"
          hover
          clickable
          @click="goToBlessingDetail(blessing.id)"
        >
          <view class="blessing-content">
            <!-- 赐福状态标识 -->
            <view class="blessing-status-badge">
              <view 
                class="status-dot"
                :class="`status-dot--${blessing.isThanked ? 'thanked' : 'pending'}`"
              ></view>
              <text class="status-text">
                {{ blessing.isThanked ? '已感谢' : '等待反馈' }}
              </text>
            </view>
            
            <!-- 目标心愿信息 -->
            <view class="target-wish">
              <text class="wish-label">赐福心愿：</text>
              <text class="wish-title">{{ blessing.wish.title }}</text>
            </view>
            
            <!-- 赐福内容 */
            <view class="blessing-main">
              <text class="blessing-text">{{ blessing.content }}</text>
              
              <image 
                v-if="blessing.image"
                :src="blessing.image"
                class="blessing-image"
                mode="aspectFill"
                @click.stop="previewImage(blessing.image)"
              />
            </view>
            
            <!-- 反馈信息 */
            <view v-if="blessing.isThanked && blessing.feedback" class="feedback-section">
              <view class="feedback-header">
                <image src="/static/icons/thank.png" class="thank-icon" />
                <text class="feedback-label">收到感谢</text>
                <text class="feedback-time">{{ formatTime(blessing.thankedAt) }}</text>
              </view>
              
              <text class="feedback-content">{{ blessing.feedback }}</text>
            </view>
            
            <!-- 赐福数据 */
            <view class="blessing-footer">
              <view class="blessing-time">
                <text class="time-text">{{ formatTime(blessing.createdAt) }}</text>
              </view>
              
              <view class="blessing-actions">
                <view class="merit-earned">
                  <image src="/static/icons/merit-small.png" class="merit-icon" />
                  <text class="merit-text">+{{ blessing.meritEarned || 10 }}</text>
                </view>
                
                <wish-button
                  v-if="!blessing.isThanked"
                  type="ghost"
                  size="small"
                  text="查看心愿"
                  @click.stop="goToWishDetail(blessing.wish.id)"
                  class="action-button"
                />
              </view>
            </view>
          </view>
        </wish-card>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <wish-loading v-if="loading" type="dots" text="加载中..." />
        <text v-else class="load-text" @click="loadMore">点击加载更多</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-else-if="myBlessingList.length > 0">
        <text class="no-more-text">没有更多赐福了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && myBlessingList.length === 0">
        <image src="/static/icons/empty-blessings.png" class="empty-icon" />
        <text class="empty-text">{{ getEmptyText() }}</text>
        <text class="empty-desc">{{ getEmptyDesc() }}</text>
        <wish-button
          type="primary"
          text="去聆听心愿"
          @click="goToListenWishes"
          class="empty-button"
        />
      </view>
    </scroll-view>
    
    <!-- 感谢统计模态框 -->
    <wish-modal
      v-model:visible="showStatsModal"
      title="赐福统计"
      position="bottom"
    >
      <view class="stats-content">
        <view class="stats-chart">
          <view class="chart-item">
            <view class="chart-bar">
              <view 
                class="bar-fill bar-fill--total"
                :style="{ height: (totalBlessings / Math.max(totalBlessings, 1)) * 200 + 'rpx' }"
              ></view>
            </view>
            <text class="chart-label">总赐福</text>
            <text class="chart-value">{{ totalBlessings }}</text>
          </view>
          
          <view class="chart-item">
            <view class="chart-bar">
              <view 
                class="bar-fill bar-fill--thanked"
                :style="{ height: (thankedBlessings / Math.max(totalBlessings, 1)) * 200 + 'rpx' }"
              ></view>
            </view>
            <text class="chart-label">已感谢</text>
            <text class="chart-value">{{ thankedBlessings }}</text>
          </view>
          
          <view class="chart-item">
            <view class="chart-bar">
              <view 
                class="bar-fill bar-fill--pending"
                :style="{ height: ((totalBlessings - thankedBlessings) / Math.max(totalBlessings, 1)) * 200 + 'rpx' }"
              ></view>
            </view>
            <text class="chart-label">等待中</text>
            <text class="chart-value">{{ totalBlessings - thankedBlessings }}</text>
          </view>
        </view>
        
        <view class="stats-summary">
          <text class="summary-text">
            感谢率：{{ totalBlessings > 0 ? Math.round((thankedBlessings / totalBlessings) * 100) : 0 }}%
          </text>
          <text class="summary-desc">
            你的赐福为 {{ thankedBlessings }} 个祈愿者带来了温暖
          </text>
        </view>
      </view>
    </wish-modal>
  </view>
</template>

<script>
import { useUserStore, useGuardianStore } from '@/store'
import { timeUtils, navigation, toast } from '@/utils'

export default {
  data() {
    return {
      statusFilter: 'all',
      loading: false,
      refreshing: false,
      showStatsModal: false,
      statusOptions: [
        { value: 'all', label: '全部' },
        { value: 'pending', label: '等待反馈' },
        { value: 'thanked', label: '已感谢' }
      ]
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    guardianStore() {
      return useGuardianStore()
    },
    
    myBlessingList() {
      return this.guardianStore.myBlessingList
    },
    
    hasMore() {
      return this.guardianStore.pagination.hasMore
    },
    
    totalBlessings() {
      return this.myBlessingList.length
    },
    
    thankedBlessings() {
      return this.guardianStore.thankedBlessings.length
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.refreshData()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      await this.loadMyBlessings(true)
    },
    
    /**
     * 刷新数据
     */
    async refreshData() {
      await this.loadMyBlessings(true)
    },
    
    /**
     * 加载我的赐福
     */
    async loadMyBlessings(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      try {
        const params = {
          isThanked: this.statusFilter === 'all' ? undefined : this.statusFilter === 'thanked'
        }
        
        await this.guardianStore.fetchMyBlessings(params, refresh)
      } catch (error) {
        console.error('加载我的赐福失败:', error)
        toast.error('加载失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 加载更多
     */
    async loadMore() {
      if (!this.hasMore || this.loading) return
      await this.loadMyBlessings(false)
    },
    
    /**
     * 下拉刷新
     */
    async onRefresh() {
      this.refreshing = true
      try {
        await this.loadMyBlessings(true)
      } finally {
        this.refreshing = false
      }
    },
    
    /**
     * 改变状态筛选
     */
    async changeStatusFilter(status) {
      if (this.statusFilter === status) return
      
      this.statusFilter = status
      await this.loadMyBlessings(true)
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      return timeUtils.relativeTime(timestamp)
    },
    
    /**
     * 获取空状态文本
     */
    getEmptyText() {
      if (this.statusFilter === 'thanked') {
        return '还没有收到感谢的赐福'
      } else if (this.statusFilter === 'pending') {
        return '没有等待反馈的赐福'
      }
      return '还没有发出过赐福'
    },
    
    /**
     * 获取空状态描述
     */
    getEmptyDesc() {
      if (this.statusFilter === 'all') {
        return '去聆听心愿，成为第一个守护者'
      }
      return '去发出更多赐福，传递温暖力量'
    },
    
    /**
     * 预览图片
     */
    previewImage(imageUrl) {
      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl
      })
    },
    
    /**
     * 页面跳转
     */
    goToBlessingDetail(blessingId) {
      navigation.navigateTo('/pages/guardian/blessing-detail', { id: blessingId })
    },
    
    goToWishDetail(wishId) {
      navigation.navigateTo('/pages/wisher/wish-detail', { id: wishId })
    },
    
    goToListenWishes() {
      navigation.navigateTo('/pages/guardian/listen-wishes')
    }
  }
}
</script>

<style lang="scss" scoped>
.my-blessings-page {
  height: 100vh;
  background-color: $wish-bg-primary;
  display: flex;
  flex-direction: column;
}

/* 统计信息 */
.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-lg;
  border-bottom: 2rpx solid $wish-border-light;
  position: relative;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: $wish-font-xxl;
  font-weight: 600;
  color: $wish-color-secondary;
  margin-bottom: $wish-spacing-xs;
}

.stat-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background-color: $wish-border-light;
}

/* 状态筛选 */
.status-filter {
  display: flex;
  align-items: center;
  padding: $wish-spacing-sm $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.filter-item {
  padding: $wish-spacing-xs $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-right: $wish-spacing-md;
  transition: all 0.3s ease;

  &--active {
    background-color: $wish-color-secondary;

    .filter-text {
      color: $wish-text-inverse;
    }
  }
}

.filter-text {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

/* 赐福列表 */
.blessing-list {
  flex: 1;
  padding: $wish-spacing-sm;
}

.blessing-items {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.blessing-item {
  margin: 0;
}

.blessing-content {
  padding: 0;
  position: relative;
}

/* 赐福状态标识 */
.blessing-status-badge {
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  right: 0;
  padding: $wish-spacing-xs $wish-spacing-sm;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 0 $wish-radius-lg 0 $wish-radius-md;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-xs;

  &--pending {
    background-color: $wish-color-warning;
  }

  &--thanked {
    background-color: $wish-color-success;
  }
}

.status-text {
  font-size: $wish-font-xs;
  color: $wish-text-secondary;
}

/* 目标心愿信息 */
.target-wish {
  margin-bottom: $wish-spacing-sm;
  margin-top: $wish-spacing-lg;
}

.wish-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin-right: $wish-spacing-xs;
}

.wish-title {
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  @extend .ellipsis;
}

/* 赐福内容 */
.blessing-main {
  margin-bottom: $wish-spacing-sm;
}

.blessing-text {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-primary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-sm;
}

.blessing-image {
  width: 100%;
  max-height: 300rpx;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
}

/* 反馈信息 */
.feedback-section {
  background-color: rgba(168, 216, 168, 0.1);
  border-radius: $wish-radius-md;
  padding: $wish-spacing-md;
  margin-bottom: $wish-spacing-sm;
}

.feedback-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-xs;
}

.thank-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: $wish-spacing-xs;
}

.feedback-label {
  font-size: $wish-font-sm;
  color: $wish-color-success;
  font-weight: 500;
  flex: 1;
}

.feedback-time {
  font-size: $wish-font-xs;
  color: $wish-text-disabled;
}

.feedback-content {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  line-height: 1.6;
}

/* 赐福底部 */
.blessing-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: $wish-spacing-sm;
  border-top: 2rpx solid $wish-border-light;
}

.blessing-time {
  flex: 1;
}

.time-text {
  font-size: $wish-font-sm;
  color: $wish-text-disabled;
}

.blessing-actions {
  display: flex;
  align-items: center;
  gap: $wish-spacing-md;
}

.merit-earned {
  display: flex;
  align-items: center;
}

.merit-icon {
  width: 20rpx;
  height: 20rpx;
  margin-right: $wish-spacing-xs;
}

.merit-text {
  font-size: $wish-font-sm;
  color: $wish-color-secondary;
  font-weight: 500;
}

.action-button {
  min-height: auto;
  padding: $wish-spacing-xs $wish-spacing-sm;
}

/* 加载状态 */
.load-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.load-text {
  font-size: $wish-font-sm;
  color: $wish-color-secondary;
}

.no-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.no-more-text {
  font-size: $wish-font-sm;
  color: $wish-text-disabled;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $wish-spacing-xxl;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: $wish-spacing-lg;
  opacity: 0.6;
}

.empty-text {
  font-size: $wish-font-lg;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.empty-desc {
  font-size: $wish-font-md;
  color: $wish-text-disabled;
  margin-bottom: $wish-spacing-xl;
}

.empty-button {
  width: 300rpx;
}

/* 统计模态框 */
.stats-content {
  padding: $wish-spacing-lg 0;
}

.stats-chart {
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  height: 300rpx;
  margin-bottom: $wish-spacing-xl;
}

.chart-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.chart-bar {
  width: 60rpx;
  height: 200rpx;
  background-color: $wish-border-light;
  border-radius: $wish-radius-sm;
  display: flex;
  align-items: flex-end;
  margin-bottom: $wish-spacing-sm;
}

.bar-fill {
  width: 100%;
  border-radius: $wish-radius-sm;
  transition: height 0.6s ease;

  &--total {
    background-color: $wish-color-secondary;
  }

  &--thanked {
    background-color: $wish-color-success;
  }

  &--pending {
    background-color: $wish-color-warning;
  }
}

.chart-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.chart-value {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
}

.stats-summary {
  text-align: center;
  background-color: rgba(212, 165, 116, 0.1);
  border-radius: $wish-radius-md;
  padding: $wish-spacing-md;
}

.summary-text {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-color-secondary;
  margin-bottom: $wish-spacing-xs;
}

.summary-desc {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
}
</style>
