/**
 * 调试工具
 * 帮助诊断API连接问题
 */

import { APP_CONFIG } from '@/config'

export class DebugHelper {
  static log(message, data = null) {
    if (APP_CONFIG.debug) {
      console.log(`[DEBUG] ${message}`, data || '')
    }
  }

  static error(message, error = null) {
    if (APP_CONFIG.debug) {
      console.error(`[ERROR] ${message}`, error || '')
    }
  }

  static warn(message, data = null) {
    if (APP_CONFIG.debug) {
      console.warn(`[WARN] ${message}`, data || '')
    }
  }

  // 测试网络连接
  static async testNetworkConnection() {
    try {
      const response = await uni.request({
        url: 'https://www.baidu.com',
        method: 'GET',
        timeout: 5000
      })
      
      this.log('网络连接正常', response.statusCode)
      return true
    } catch (error) {
      this.error('网络连接失败', error)
      return false
    }
  }

  // 测试后端服务连接
  static async testBackendConnection() {
    const baseURL = APP_CONFIG.baseURL
    
    try {
      this.log('测试后端连接', baseURL)
      
      const response = await uni.request({
        url: `${baseURL.replace('/api', '')}/health`,
        method: 'GET',
        timeout: 10000
      })
      
      this.log('后端连接成功', {
        status: response.statusCode,
        data: response.data
      })
      
      return {
        success: true,
        status: response.statusCode,
        data: response.data
      }
    } catch (error) {
      this.error('后端连接失败', error)
      
      return {
        success: false,
        error: error.message || error.toString()
      }
    }
  }

  // 测试API端点
  static async testAPIEndpoint(endpoint, method = 'GET', data = null) {
    const baseURL = APP_CONFIG.baseURL
    const fullURL = `${baseURL}${endpoint}`
    
    try {
      this.log(`测试API端点: ${method} ${fullURL}`)
      
      const requestConfig = {
        url: fullURL,
        method: method.toUpperCase(),
        timeout: 10000,
        header: {
          'Content-Type': 'application/json'
        }
      }
      
      if (data && (method.toUpperCase() === 'POST' || method.toUpperCase() === 'PUT')) {
        requestConfig.data = data
      }
      
      const response = await uni.request(requestConfig)
      
      this.log(`API端点响应: ${response.statusCode}`, response.data)
      
      return {
        success: response.statusCode >= 200 && response.statusCode < 300,
        status: response.statusCode,
        data: response.data
      }
    } catch (error) {
      this.error(`API端点失败: ${fullURL}`, error)
      
      return {
        success: false,
        error: error.message || error.toString()
      }
    }
  }

  // 生成诊断报告
  static async generateDiagnosticReport() {
    const report = {
      timestamp: new Date().toISOString(),
      config: {
        baseURL: APP_CONFIG.baseURL,
        debug: APP_CONFIG.debug,
        version: APP_CONFIG.version
      },
      tests: {}
    }

    // 测试网络连接
    report.tests.network = await this.testNetworkConnection()

    // 测试后端连接
    report.tests.backend = await this.testBackendConnection()

    // 测试基础API端点
    const endpoints = [
      { path: '', name: 'API根路径' },
      { path: '/auth', name: '认证模块' },
      { path: '/wishes', name: '心愿模块' },
      { path: '/users', name: '用户模块' }
    ]

    report.tests.endpoints = {}
    
    for (const endpoint of endpoints) {
      report.tests.endpoints[endpoint.name] = await this.testAPIEndpoint(endpoint.path)
    }

    this.log('诊断报告生成完成', report)
    return report
  }

  // 显示诊断报告
  static displayReport(report) {
    console.log('='.repeat(50))
    console.log('🔍 API连接诊断报告')
    console.log('='.repeat(50))
    
    console.log(`📅 时间: ${report.timestamp}`)
    console.log(`🔧 配置: ${JSON.stringify(report.config, null, 2)}`)
    
    console.log('\n📊 测试结果:')
    console.log(`🌐 网络连接: ${report.tests.network ? '✅ 正常' : '❌ 失败'}`)
    console.log(`🖥️  后端连接: ${report.tests.backend.success ? '✅ 正常' : '❌ 失败'}`)
    
    if (!report.tests.backend.success) {
      console.log(`   错误: ${report.tests.backend.error}`)
    }
    
    console.log('\n🔌 API端点测试:')
    Object.keys(report.tests.endpoints).forEach(name => {
      const result = report.tests.endpoints[name]
      console.log(`   ${name}: ${result.success ? '✅' : '❌'} (${result.status || 'N/A'})`)
      if (!result.success && result.error) {
        console.log(`     错误: ${result.error}`)
      }
    })
    
    console.log('='.repeat(50))
  }
}

// 快速诊断函数
export async function quickDiagnose() {
  const report = await DebugHelper.generateDiagnosticReport()
  DebugHelper.displayReport(report)
  return report
}

export default DebugHelper
