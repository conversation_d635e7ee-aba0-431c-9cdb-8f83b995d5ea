<!--
  愿境注册页面
  用户注册功能实现
-->
<template>
  <view class="register-page">
    <!-- 背景装饰 -->
    <view class="background-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
    </view>
    
    <!-- 主要内容 -->
    <view class="register-content">
      <!-- 头部 -->
      <view class="register-header">
        <text class="page-title">注册账号</text>
        <text class="page-subtitle">加入愿境，开始你的心愿之旅</text>
      </view>
      
      <!-- 注册表单 -->
      <view class="register-form">
        <!-- 手机号 -->
        <wish-input
          v-model="formData.phone"
          type="text"
          placeholder="请输入手机号"
          prefix-icon="/static/icons/phone.png"
          :error="errors.phone"
          :error-message="errors.phone"
          @input="clearError('phone')"
        />
        
        <!-- 验证码 -->
        <view class="verify-code-group">
          <wish-input
            v-model="formData.verifyCode"
            type="text"
            placeholder="请输入验证码"
            prefix-icon="/static/icons/code.png"
            :error="errors.verifyCode"
            :error-message="errors.verifyCode"
            @input="clearError('verifyCode')"
            class="verify-input"
          />
          <wish-button
            :type="canSendCode ? 'primary' : 'ghost'"
            size="medium"
            :text="codeButtonText"
            :disabled="!canSendCode || sendingCode"
            :loading="sendingCode"
            @click="sendVerifyCode"
            class="code-button"
          />
        </view>
        
        <!-- 昵称 -->
        <wish-input
          v-model="formData.nickname"
          type="text"
          placeholder="请输入昵称"
          prefix-icon="/static/icons/user.png"
          :error="errors.nickname"
          :error-message="errors.nickname"
          :maxlength="20"
          show-word-limit
          @input="clearError('nickname')"
        />
        
        <!-- 密码 -->
        <wish-input
          v-model="formData.password"
          type="password"
          placeholder="请输入密码（6-20位）"
          prefix-icon="/static/icons/password.png"
          :error="errors.password"
          :error-message="errors.password"
          @input="clearError('password')"
        />
        
        <!-- 确认密码 -->
        <wish-input
          v-model="formData.confirmPassword"
          type="password"
          placeholder="请再次输入密码"
          prefix-icon="/static/icons/password.png"
          :error="errors.confirmPassword"
          :error-message="errors.confirmPassword"
          @input="clearError('confirmPassword')"
        />
        
        <!-- 用户协议 -->
        <view class="agreement-check">
          <view 
            class="checkbox"
            :class="{ 'checkbox--checked': agreedToTerms }"
            @click="toggleAgreement"
          >
            <image 
              v-if="agreedToTerms"
              src="/static/icons/check.png" 
              class="checkbox-icon" 
            />
          </view>
          <text class="agreement-text">
            我已阅读并同意
            <text class="agreement-link" @click="showUserAgreement">《用户协议》</text>
            和
            <text class="agreement-link" @click="showPrivacyPolicy">《隐私政策》</text>
          </text>
        </view>
        
        <!-- 注册按钮 -->
        <wish-button
          type="primary"
          size="large"
          text="注册"
          :loading="loading"
          :disabled="!canRegister"
          @click="handleRegister"
          class="register-button"
        />
        
        <!-- 登录链接 -->
        <view class="login-link">
          <text class="login-text">已有账号？</text>
          <text class="login-action" @click="goToLogin">立即登录</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { useUserStore } from '@/store'
import { authAPI } from '@/api'
import { validator, navigation, toast } from '@/utils'

export default {
  data() {
    return {
      formData: {
        phone: '',
        verifyCode: '',
        nickname: '',
        password: '',
        confirmPassword: ''
      },
      errors: {},
      loading: false,
      sendingCode: false,
      agreedToTerms: false,
      countdown: 0,
      countdownTimer: null
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    canSendCode() {
      return validator.isPhone(this.formData.phone) && this.countdown === 0
    },
    
    codeButtonText() {
      return this.countdown > 0 ? `${this.countdown}s后重发` : '发送验证码'
    },
    
    canRegister() {
      return this.agreedToTerms && 
             this.formData.phone && 
             this.formData.verifyCode && 
             this.formData.nickname && 
             this.formData.password && 
             this.formData.confirmPassword
    }
  },
  
  onUnload() {
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  },
  
  methods: {
    /**
     * 表单验证
     */
    validateForm() {
      const errors = {}
      
      // 验证手机号
      if (!this.formData.phone) {
        errors.phone = '请输入手机号'
      } else if (!validator.isPhone(this.formData.phone)) {
        errors.phone = '请输入正确的手机号'
      }
      
      // 验证验证码
      if (!this.formData.verifyCode) {
        errors.verifyCode = '请输入验证码'
      } else if (this.formData.verifyCode.length !== 6) {
        errors.verifyCode = '验证码格式不正确'
      }
      
      // 验证昵称
      if (!this.formData.nickname) {
        errors.nickname = '请输入昵称'
      } else if (!validator.isValidNickname(this.formData.nickname)) {
        errors.nickname = '昵称格式不正确'
      }
      
      // 验证密码
      if (!this.formData.password) {
        errors.password = '请输入密码'
      } else if (this.formData.password.length < 6 || this.formData.password.length > 20) {
        errors.password = '密码长度应为6-20位'
      }
      
      // 验证确认密码
      if (!this.formData.confirmPassword) {
        errors.confirmPassword = '请再次输入密码'
      } else if (this.formData.password !== this.formData.confirmPassword) {
        errors.confirmPassword = '两次输入的密码不一致'
      }
      
      this.errors = errors
      return Object.keys(errors).length === 0
    },
    
    /**
     * 清除错误信息
     */
    clearError(field) {
      if (this.errors[field]) {
        this.$delete(this.errors, field)
      }
    },
    
    /**
     * 发送验证码
     */
    async sendVerifyCode() {
      if (!validator.isPhone(this.formData.phone)) {
        toast.error('请输入正确的手机号')
        return
      }
      
      this.sendingCode = true
      try {
        await authAPI.sendVerifyCode(this.formData.phone, 'register')
        toast.success('验证码已发送')
        this.startCountdown()
      } catch (error) {
        console.error('发送验证码失败:', error)
        toast.error(error.message || '发送验证码失败')
      } finally {
        this.sendingCode = false
      }
    },
    
    /**
     * 开始倒计时
     */
    startCountdown() {
      this.countdown = 60
      this.countdownTimer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.countdownTimer)
          this.countdownTimer = null
        }
      }, 1000)
    },
    
    /**
     * 切换协议同意状态
     */
    toggleAgreement() {
      this.agreedToTerms = !this.agreedToTerms
    },
    
    /**
     * 处理注册
     */
    async handleRegister() {
      if (!this.validateForm()) {
        return
      }
      
      if (!this.agreedToTerms) {
        toast.error('请先同意用户协议和隐私政策')
        return
      }
      
      this.loading = true
      try {
        await this.userStore.register(
          this.formData.phone,
          this.formData.password,
          this.formData.nickname,
          this.formData.verifyCode
        )
        
        toast.success('注册成功')
        
        // 跳转到首页
        setTimeout(() => {
          navigation.switchTab('/pages/index/index')
        }, 1000)
        
      } catch (error) {
        console.error('注册失败:', error)
        toast.error(error.message || '注册失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 页面跳转
     */
    goToLogin() {
      navigation.navigateBack()
    },
    
    /**
     * 显示协议
     */
    showUserAgreement() {
      navigation.navigateTo('/pages/legal/user-agreement')
    },
    
    showPrivacyPolicy() {
      navigation.navigateTo('/pages/legal/privacy-policy')
    }
  }
}
</script>

<style lang="scss" scoped>
.register-page {
  min-height: 100vh;
  background: linear-gradient(135deg, $wish-bg-primary 0%, rgba(212, 165, 116, 0.1) 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(212, 165, 116, 0.1);

  &.circle-1 {
    width: 180rpx;
    height: 180rpx;
    top: 15%;
    left: -40rpx;
    animation: float 8s ease-in-out infinite;
  }

  &.circle-2 {
    width: 120rpx;
    height: 120rpx;
    bottom: 25%;
    right: -30rpx;
    animation: float 10s ease-in-out infinite reverse;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

/* 主要内容 */
.register-content {
  padding: $wish-spacing-xl $wish-spacing-md;
  padding-top: calc($wish-spacing-xl + constant(safe-area-inset-top));
  padding-top: calc($wish-spacing-xl + env(safe-area-inset-top));
}

/* 头部 */
.register-header {
  text-align: center;
  margin-bottom: $wish-spacing-xl;
}

.page-title {
  display: block;
  font-size: $wish-font-xxl;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.page-subtitle {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
}

/* 注册表单 */
.register-form {
  margin-bottom: $wish-spacing-lg;
}

/* 验证码组 */
.verify-code-group {
  display: flex;
  align-items: flex-start;
  gap: $wish-spacing-sm;
  margin-bottom: $wish-spacing-md;
}

.verify-input {
  flex: 1;
  margin-bottom: 0;
}

.code-button {
  width: 200rpx;
  flex-shrink: 0;
}

/* 协议勾选 */
.agreement-check {
  display: flex;
  align-items: flex-start;
  margin-bottom: $wish-spacing-lg;
  padding: $wish-spacing-sm 0;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 2rpx solid $wish-border-medium;
  border-radius: $wish-radius-sm;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $wish-spacing-sm;
  margin-top: 4rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;

  &--checked {
    background-color: $wish-color-primary;
    border-color: $wish-color-primary;
  }
}

.checkbox-icon {
  width: 20rpx;
  height: 20rpx;
}

.agreement-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  line-height: 1.6;
  flex: 1;
}

.agreement-link {
  color: $wish-color-primary;
}

/* 注册按钮 */
.register-button {
  width: 100%;
  margin-bottom: $wish-spacing-md;
}

/* 登录链接 */
.login-link {
  text-align: center;
}

.login-text {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  margin-right: $wish-spacing-xs;
}

.login-action {
  font-size: $wish-font-md;
  color: $wish-color-primary;
  font-weight: 500;
}
</style>
