/**
 * 社交功能相关API
 * 包括排行榜、评论、私信等功能接口
 */

import request from './request'

export const socialAPI = {
  /**
   * 获取排行榜
   */
  getLeaderboard(params, refresh = false) {
    return request.get('/social/leaderboard', { params })
  },

  /**
   * 获取评论列表
   */
  getComments(params) {
    return request.get('/social/comments', { params })
  },

  /**
   * 发表评论
   */
  createComment(commentData) {
    return request.post('/social/comments', commentData)
  },

  /**
   * 点赞评论
   */
  likeComment(commentId) {
    return request.post(`/social/comments/${commentId}/like`)
  },

  /**
   * 取消点赞评论
   */
  unlikeComment(commentId) {
    return request.delete(`/social/comments/${commentId}/like`)
  },

  /**
   * 删除评论
   */
  deleteComment(commentId) {
    return request.delete(`/social/comments/${commentId}`)
  },

  /**
   * 感谢赐福
   */
  thankBlessing(blessingId, feedback = '') {
    return request.post(`/social/blessings/${blessingId}/thank`, { feedback })
  },

  /**
   * 获取对话列表
   */
  getConversations(params = {}) {
    return request.get('/social/conversations', { params })
  },

  /**
   * 创建对话
   */
  createConversation(userId) {
    return request.post('/social/conversations', { userId })
  },

  /**
   * 获取消息列表
   */
  getMessages(params = {}) {
    return request.get('/social/messages', { params })
  },

  /**
   * 发送消息
   */
  sendMessage(messageData) {
    return request.post('/social/messages', messageData)
  },

  /**
   * 上传消息图片
   */
  uploadMessageImage(filePath) {
    return request.upload('/social/messages/upload-image', filePath)
  },

  /**
   * 搜索用户
   */
  searchUsers(keyword) {
    return request.get('/social/users/search', {
      params: { keyword }
    })
  },

  /**
   * 获取用户信息
   */
  getUserInfo(userId) {
    return request.get(`/social/users/${userId}`)
  },

  /**
   * 获取消息数量统计
   */
  getMessageCounts() {
    return request.get('/social/messages/counts')
  },

  /**
   * 置顶/取消置顶对话
   */
  toggleConversationPin(conversationId) {
    return request.put(`/social/conversations/${conversationId}/pin`)
  },

  /**
   * 免打扰/取消免打扰对话
   */
  toggleConversationMute(conversationId) {
    return request.put(`/social/conversations/${conversationId}/mute`)
  },

  /**
   * 标记对话为已读
   */
  markConversationRead(conversationId) {
    return request.put(`/social/conversations/${conversationId}/read`)
  },

  /**
   * 标记消息为已读
   */
  markMessagesAsRead(conversationId) {
    return request.put(`/social/conversations/${conversationId}/messages/read`)
  },

  /**
   * 删除对话
   */
  deleteConversation(conversationId) {
    return request.delete(`/social/conversations/${conversationId}`)
  },

  /**
   * 清空消息
   */
  clearMessages(conversationId) {
    return request.delete(`/social/conversations/${conversationId}/messages`)
  },

  /**
   * 拉黑用户
   */
  blockUser(userId) {
    return request.post(`/social/users/${userId}/block`)
  },

  /**
   * 标记消息已读（兼容旧接口）
   */
  markMessageRead(messageId) {
    return request.put(`/social/messages/${messageId}/read`)
  }
}
