/**
 * SCSS 工具类
 * 提供常用的样式工具类
 */

// 文本省略号工具类
.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  white-space: normal;
}

.ellipsis-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  white-space: normal;
}

.ellipsis-4 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  white-space: normal;
}

// Flexbox 工具类
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

// 定位工具类
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

// 显示/隐藏工具类
.hidden {
  display: none;
}

.invisible {
  visibility: hidden;
}

// 圆角工具类
.rounded {
  border-radius: $wish-radius-md;
}

.rounded-sm {
  border-radius: $wish-radius-sm;
}

.rounded-lg {
  border-radius: $wish-radius-lg;
}

.rounded-full {
  border-radius: 50%;
}

// 阴影工具类
.shadow {
  box-shadow: $wish-shadow-md;
}

.shadow-sm {
  box-shadow: $wish-shadow-sm;
}

.shadow-lg {
  box-shadow: $wish-shadow-lg;
}

// 间距工具类
.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }

// 文本对齐工具类
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

// 字体粗细工具类
.font-normal {
  font-weight: normal;
}

.font-bold {
  font-weight: bold;
}

.font-light {
  font-weight: 300;
}

// 颜色工具类
.text-primary {
  color: $wish-text-primary;
}

.text-secondary {
  color: $wish-text-secondary;
}

.text-muted {
  color: $wish-text-muted;
}

.text-success {
  color: $wish-success;
}

.text-warning {
  color: $wish-warning;
}

.text-error {
  color: $wish-error;
}

// 背景颜色工具类
.bg-primary {
  background-color: $wish-bg-primary;
}

.bg-secondary {
  background-color: $wish-bg-secondary;
}

.bg-white {
  background-color: #ffffff;
}

// 清除浮动
.clearfix::after {
  content: "";
  display: table;
  clear: both;
}

// 禁用选择
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

// 禁用点击
.pointer-events-none {
  pointer-events: none;
}

// 光标样式
.cursor-pointer {
  cursor: pointer;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

// 过渡动画
.transition {
  transition: all 0.3s ease;
}

.transition-fast {
  transition: all 0.15s ease;
}

.transition-slow {
  transition: all 0.5s ease;
}
