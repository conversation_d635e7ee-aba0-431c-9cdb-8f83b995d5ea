/**
 * 祈愿状态管理
 * 管理心愿列表、心愿详情、发布心愿等
 */

import { defineStore } from 'pinia'
import { wishAPI } from '@/api'

export const useWishStore = defineStore('wish', {
  state: () => ({
    // 心愿列表
    wishList: [],
    // 我的心愿列表
    myWishList: [],
    // 当前心愿详情
    currentWish: null,
    // 分页信息
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0,
      hasMore: true
    },
    // 筛选条件
    filters: {
      sortBy: 'latest', // latest, hot, random
      tag: '',
      keyword: ''
    },
    // 加载状态
    loading: {
      list: false,
      detail: false,
      create: false,
      support: false
    }
  }),

  getters: {
    // 获取指定标签的心愿
    getWishesByTag: (state) => (tag) => {
      return state.wishList.filter(wish => 
        wish.tags && wish.tags.includes(tag)
      )
    },
    
    // 获取热门心愿（支持数量多的）
    hotWishes: (state) => {
      return [...state.wishList]
        .sort((a, b) => (b.supportCount || 0) - (a.supportCount || 0))
        .slice(0, 10)
    },
    
    // 获取最新心愿
    latestWishes: (state) => {
      return [...state.wishList]
        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        .slice(0, 10)
    },
    
    // 统计信息
    wishStats: (state) => ({
      total: state.wishList.length,
      blessed: state.wishList.filter(wish => wish.status === 'blessed').length,
      pending: state.wishList.filter(wish => wish.status === 'pending').length
    })
  },

  actions: {
    /**
     * 获取心愿列表
     */
    async fetchWishList(params = {}, refresh = false) {
      // 如果是刷新，重置分页
      if (refresh) {
        this.pagination.current = 1
        this.pagination.hasMore = true
      }
      
      // 如果没有更多数据，直接返回
      if (!refresh && !this.pagination.hasMore) {
        return
      }
      
      this.loading.list = true
      try {
        const requestParams = {
          page: this.pagination.current,
          pageSize: this.pagination.pageSize,
          sortBy: this.filters.sortBy,
          tag: this.filters.tag,
          ...params
        }
        
        const result = await wishAPI.getWishList(requestParams)
        
        if (refresh) {
          this.wishList = result.list
        } else {
          this.wishList.push(...result.list)
        }
        
        // 更新分页信息
        this.pagination.total = result.total
        this.pagination.current += 1
        this.pagination.hasMore = result.list.length === this.pagination.pageSize
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.list = false
      }
    },

    /**
     * 获取我的心愿列表
     */
    async fetchMyWishes(params = {}, refresh = false) {
      this.loading.list = true
      try {
        const result = await wishAPI.getMyWishes(params)
        
        if (refresh) {
          this.myWishList = result.list
        } else {
          this.myWishList.push(...result.list)
        }
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.list = false
      }
    },

    /**
     * 获取心愿详情
     */
    async fetchWishDetail(wishId) {
      this.loading.detail = true
      try {
        const wish = await wishAPI.getWishDetail(wishId)
        this.currentWish = wish
        return wish
      } catch (error) {
        throw error
      } finally {
        this.loading.detail = false
      }
    },

    /**
     * 创建心愿
     */
    async createWish(wishData) {
      this.loading.create = true
      try {
        const result = await wishAPI.createWish(wishData)
        
        // 将新心愿添加到列表开头
        this.wishList.unshift(result)
        this.myWishList.unshift(result)
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.create = false
      }
    },

    /**
     * 删除心愿
     */
    async deleteWish(wishId) {
      try {
        await wishAPI.deleteWish(wishId)
        
        // 从列表中移除
        this.wishList = this.wishList.filter(wish => wish.id !== wishId)
        this.myWishList = this.myWishList.filter(wish => wish.id !== wishId)
        
        // 如果是当前详情，清空
        if (this.currentWish && this.currentWish.id === wishId) {
          this.currentWish = null
        }
        
        return true
      } catch (error) {
        throw error
      }
    },

    /**
     * 助力心愿
     */
    async supportWish(wishId) {
      this.loading.support = true
      try {
        const result = await wishAPI.supportWish(wishId)
        
        // 更新列表中的心愿支持数
        const updateWishSupport = (wish) => {
          if (wish.id === wishId) {
            wish.supportCount = (wish.supportCount || 0) + 1
            wish.isSupported = true
          }
          return wish
        }
        
        this.wishList = this.wishList.map(updateWishSupport)
        this.myWishList = this.myWishList.map(updateWishSupport)
        
        // 更新当前详情
        if (this.currentWish && this.currentWish.id === wishId) {
          this.currentWish.supportCount = (this.currentWish.supportCount || 0) + 1
          this.currentWish.isSupported = true
        }
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.support = false
      }
    },

    /**
     * 搜索心愿
     */
    async searchWishes(keyword, params = {}) {
      this.loading.list = true
      try {
        const result = await wishAPI.searchWishes(keyword, params)
        this.wishList = result.list
        this.filters.keyword = keyword
        
        // 重置分页
        this.pagination.current = 1
        this.pagination.total = result.total
        this.pagination.hasMore = result.list.length === this.pagination.pageSize
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.list = false
      }
    },

    /**
     * 设置筛选条件
     */
    setFilters(filters) {
      this.filters = { ...this.filters, ...filters }
      
      // 重置分页
      this.pagination.current = 1
      this.pagination.hasMore = true
      
      // 重新获取数据
      return this.fetchWishList({}, true)
    },

    /**
     * 清空心愿列表
     */
    clearWishList() {
      this.wishList = []
      this.pagination.current = 1
      this.pagination.hasMore = true
    },

    /**
     * 更新心愿状态（收到赐福时调用）
     */
    updateWishStatus(wishId, status, blessing = null) {
      const updateWish = (wish) => {
        if (wish.id === wishId) {
          wish.status = status
          if (blessing) {
            wish.blessings = wish.blessings || []
            wish.blessings.push(blessing)
          }
        }
        return wish
      }
      
      this.wishList = this.wishList.map(updateWish)
      this.myWishList = this.myWishList.map(updateWish)
      
      // 更新当前详情
      if (this.currentWish && this.currentWish.id === wishId) {
        this.currentWish.status = status
        if (blessing) {
          this.currentWish.blessings = this.currentWish.blessings || []
          this.currentWish.blessings.push(blessing)
        }
      }
    },

    /**
     * 随机获取一个心愿（守护者使用）
     */
    async getRandomWish() {
      try {
        const result = await wishAPI.getWishList({
          page: 1,
          pageSize: 1,
          sortBy: 'random'
        })
        
        return result.list[0] || null
      } catch (error) {
        throw error
      }
    }
  }
})
