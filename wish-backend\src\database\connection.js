/**
 * 数据库连接管理
 * 处理MongoDB连接和配置
 */

const mongoose = require('mongoose');
const config = require('../config');
const logger = require('../utils/logger');

// 连接状态监听
mongoose.connection.on('connected', () => {
  logger.info('MongoDB连接成功');
});

mongoose.connection.on('error', (error) => {
  logger.error('MongoDB连接错误:', error);
});

mongoose.connection.on('disconnected', () => {
  logger.warn('MongoDB连接断开');
});

// 优雅关闭
process.on('SIGINT', async () => {
  try {
    await mongoose.connection.close();
    logger.info('MongoDB连接已关闭');
    process.exit(0);
  } catch (error) {
    logger.error('关闭MongoDB连接时出错:', error);
    process.exit(1);
  }
});

/**
 * 连接数据库
 */
async function connectDatabase() {
  try {
    await mongoose.connect(config.database.mongodb.uri, config.database.mongodb.options);
    
    // 设置调试模式（仅在开发环境）
    if (config.env === 'development') {
      mongoose.set('debug', true);
    }
    
    return mongoose.connection;
  } catch (error) {
    logger.error('数据库连接失败:', error);
    throw error;
  }
}

/**
 * 断开数据库连接
 */
async function disconnectDatabase() {
  try {
    await mongoose.connection.close();
    logger.info('数据库连接已断开');
  } catch (error) {
    logger.error('断开数据库连接时出错:', error);
    throw error;
  }
}

/**
 * 检查数据库连接状态
 */
function isConnected() {
  return mongoose.connection.readyState === 1;
}

/**
 * 获取数据库连接状态
 */
function getConnectionState() {
  const states = {
    0: 'disconnected',
    1: 'connected',
    2: 'connecting',
    3: 'disconnecting'
  };
  
  return {
    state: states[mongoose.connection.readyState],
    readyState: mongoose.connection.readyState,
    host: mongoose.connection.host,
    port: mongoose.connection.port,
    name: mongoose.connection.name
  };
}

module.exports = {
  connectDatabase,
  disconnectDatabase,
  isConnected,
  getConnectionState,
  mongoose
};
