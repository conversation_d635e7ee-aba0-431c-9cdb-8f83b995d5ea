/**
 * 数据库连接管理
 * 处理MySQL连接和配置
 */

const { Sequelize } = require('sequelize');
const config = require('../config');
const logger = require('../utils/logger');

let sequelize = null;

/**
 * 连接数据库
 */
async function connectDatabase() {
  try {
    sequelize = new Sequelize(
      config.database.mysql.database,
      config.database.mysql.username,
      config.database.mysql.password,
      {
        host: config.database.mysql.host,
        port: config.database.mysql.port,
        dialect: 'mysql',
        logging: config.env === 'development' ? (msg) => logger.debug(msg) : false,
        pool: {
          max: config.database.mysql.pool.max,
          min: config.database.mysql.pool.min,
          acquire: config.database.mysql.pool.acquire,
          idle: config.database.mysql.pool.idle
        },
        dialectOptions: {
          charset: 'utf8mb4',
          collate: 'utf8mb4_unicode_ci'
        },
        define: {
          timestamps: true,
          underscored: true,
          freezeTableName: true
        }
      }
    );

    // 测试连接
    await sequelize.authenticate();
    logger.info('MySQL数据库连接成功');

    return sequelize;
  } catch (error) {
    logger.error('数据库连接失败:', error);
    throw error;
  }
}

/**
 * 断开数据库连接
 */
async function disconnectDatabase() {
  try {
    if (sequelize) {
      await sequelize.close();
      sequelize = null;
      logger.info('数据库连接已断开');
    }
  } catch (error) {
    logger.error('断开数据库连接时出错:', error);
    throw error;
  }
}

/**
 * 检查数据库连接状态
 */
function isConnected() {
  return sequelize && sequelize.connectionManager.pool;
}

/**
 * 获取数据库连接状态
 */
function getConnectionState() {
  if (!sequelize) {
    return {
      state: 'disconnected',
      host: null,
      port: null,
      database: null
    };
  }

  return {
    state: 'connected',
    host: sequelize.config.host,
    port: sequelize.config.port,
    database: sequelize.config.database
  };
}

/**
 * 获取Sequelize实例
 */
function getSequelize() {
  return sequelize;
}

/**
 * 同步数据库模型
 */
async function syncDatabase(force = false) {
  try {
    if (!sequelize) {
      throw new Error('数据库未连接');
    }

    await sequelize.sync({ force });
    logger.info('数据库模型同步完成');
  } catch (error) {
    logger.error('数据库模型同步失败:', error);
    throw error;
  }
}

// 优雅关闭
process.on('SIGINT', async () => {
  try {
    await disconnectDatabase();
    process.exit(0);
  } catch (error) {
    logger.error('关闭数据库连接时出错:', error);
    process.exit(1);
  }
});

process.on('SIGTERM', async () => {
  try {
    await disconnectDatabase();
    process.exit(0);
  } catch (error) {
    logger.error('关闭数据库连接时出错:', error);
    process.exit(1);
  }
});

module.exports = {
  connectDatabase,
  disconnectDatabase,
  isConnected,
  getConnectionState,
  getSequelize,
  syncDatabase
};
