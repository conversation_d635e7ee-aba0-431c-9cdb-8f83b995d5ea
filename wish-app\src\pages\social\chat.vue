<!--
  愿境聊天页面
  一对一私信聊天界面
-->
<template>
  <view class="chat-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar safe-area-top">
      <view class="navbar-content">
        <view class="navbar-back" @click="goBack">
          <image src="/static/icons/arrow-left.png" class="back-icon" />
        </view>
        <view class="navbar-user" @click="goToUserProfile">
          <image :src="chatUser.avatar" class="user-avatar" />
          <view class="user-info">
            <text class="user-nickname">{{ chatUser.nickname }}</text>
            <text class="user-status">{{ chatUser.isOnline ? '在线' : '离线' }}</text>
          </view>
        </view>
        <view class="navbar-actions">
          <view class="navbar-action" @click="showMoreActions = true">
            <image src="/static/icons/more.png" class="action-icon" />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 消息列表 -->
    <scroll-view 
      class="message-list"
      scroll-y
      :scroll-top="scrollTop"
      :scroll-into-view="scrollIntoView"
      @scrolltoupper="loadMoreMessages"
    >
      <!-- 加载更多提示 -->
      <view class="load-more-messages" v-if="hasMoreMessages">
        <wish-loading v-if="loadingMessages" type="dots" text="加载中..." />
        <text v-else class="load-text" @click="loadMoreMessages">点击加载更多消息</text>
      </view>
      
      <!-- 消息项 -->
      <view class="message-items">
        <view 
          v-for="message in messageList"
          :key="message.id"
          :id="`message-${message.id}`"
          class="message-item"
          :class="{ 'message-item--mine': message.isMine }"
        >
          <!-- 时间分隔 -->
          <view v-if="message.showTime" class="time-divider">
            <text class="time-text">{{ formatMessageTime(message.createdAt) }}</text>
          </view>
          
          <!-- 消息内容 -->
          <view class="message-content">
            <image 
              v-if="!message.isMine"
              :src="chatUser.avatar" 
              class="message-avatar" 
            />
            
            <view class="message-bubble" :class="`message-bubble--${message.isMine ? 'mine' : 'other'}`">
              <!-- 文本消息 -->
              <text v-if="message.type === 'text'" class="message-text">{{ message.content }}</text>
              
              <!-- 图片消息 -->
              <image 
                v-else-if="message.type === 'image'"
                :src="message.content"
                class="message-image"
                mode="aspectFill"
                @click="previewImage(message.content)"
              />
              
              <!-- 赐福消息 -->
              <view v-else-if="message.type === 'blessing'" class="blessing-message">
                <image src="/static/icons/blessing-small.png" class="blessing-icon" />
                <text class="blessing-text">{{ message.content }}</text>
              </view>
              
              <!-- 消息状态 -->
              <view v-if="message.isMine" class="message-status">
                <image 
                  v-if="message.status === 'sending'"
                  src="/static/icons/sending.png" 
                  class="status-icon status-icon--sending"
                />
                <image 
                  v-else-if="message.status === 'sent'"
                  src="/static/icons/sent.png" 
                  class="status-icon"
                />
                <image 
                  v-else-if="message.status === 'read'"
                  src="/static/icons/read.png" 
                  class="status-icon status-icon--read"
                />
                <image 
                  v-else-if="message.status === 'failed'"
                  src="/static/icons/failed.png" 
                  class="status-icon status-icon--failed"
                  @click="resendMessage(message)"
                />
              </view>
            </view>
            
            <image 
              v-if="message.isMine"
              :src="userStore.avatar" 
              class="message-avatar" 
            />
          </view>
        </view>
      </view>
    </scroll-view>
    
    <!-- 输入区域 -->
    <view class="input-area safe-area-bottom">
      <view class="input-toolbar">
        <view class="toolbar-item" @click="chooseImage">
          <image src="/static/icons/image.png" class="toolbar-icon" />
        </view>
        
        <view class="toolbar-item" @click="showEmojiPanel = !showEmojiPanel">
          <image src="/static/icons/emoji.png" class="toolbar-icon" />
        </view>
      </view>
      
      <view class="input-wrapper">
        <wish-input
          v-model="inputText"
          placeholder="输入消息..."
          :maxlength="500"
          @focus="onInputFocus"
          @blur="onInputBlur"
          @confirm="sendMessage"
          class="message-input"
        />
        
        <wish-button
          type="primary"
          size="small"
          :text="inputText.trim() ? '发送' : ''"
          :disabled="!inputText.trim()"
          @click="sendMessage"
          class="send-button"
        >
          <image v-if="!inputText.trim()" src="/static/icons/voice.png" class="voice-icon" />
        </wish-button>
      </view>
      
      <!-- 表情面板 -->
      <view v-if="showEmojiPanel" class="emoji-panel">
        <view class="emoji-grid">
          <text 
            v-for="emoji in emojiList"
            :key="emoji"
            class="emoji-item"
            @click="insertEmoji(emoji)"
          >
            {{ emoji }}
          </text>
        </view>
      </view>
    </view>
    
    <!-- 更多操作模态框 -->
    <wish-modal
      v-model:visible="showMoreActions"
      title="更多操作"
      position="bottom"
    >
      <view class="more-actions">
        <view class="action-item" @click="clearMessages">
          <image src="/static/icons/clear.png" class="action-icon" />
          <text class="action-text">清空聊天记录</text>
        </view>
        
        <view class="action-item" @click="reportUser">
          <image src="/static/icons/report.png" class="action-icon" />
          <text class="action-text">举报用户</text>
        </view>
        
        <view class="action-item" @click="blockUser">
          <image src="/static/icons/block.png" class="action-icon" />
          <text class="action-text">拉黑用户</text>
        </view>
      </view>
    </wish-modal>
  </view>
</template>

<script>
import { useUserStore, useSocialStore } from '@/store'
import { timeUtils, navigation, toast } from '@/utils'

export default {
  data() {
    return {
      conversationId: '',
      userId: '',
      chatUser: {},
      messageList: [],
      inputText: '',
      scrollTop: 0,
      scrollIntoView: '',
      loadingMessages: false,
      showEmojiPanel: false,
      showMoreActions: false,
      emojiList: [
        '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣',
        '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
        '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜',
        '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
        '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
        '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠'
      ]
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    socialStore() {
      return useSocialStore()
    },
    
    hasMoreMessages() {
      return this.socialStore.messagePagination.hasMore
    }
  },
  
  onLoad(options) {
    this.conversationId = options.conversationId
    this.userId = options.userId
    this.initPage()
  },
  
  onShow() {
    this.markMessagesAsRead()
  },
  
  onUnload() {
    // 离开页面时标记消息为已读
    this.markMessagesAsRead()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      try {
        // 获取聊天用户信息
        this.chatUser = await this.socialStore.getUserInfo(this.userId)
        
        // 加载消息列表
        await this.loadMessages(true)
        
        // 滚动到底部
        this.scrollToBottom()
        
        // 标记消息为已读
        this.markMessagesAsRead()
      } catch (error) {
        console.error('页面初始化失败:', error)
        toast.error('页面加载失败')
        navigation.navigateBack()
      }
    },
    
    /**
     * 加载消息列表
     */
    async loadMessages(refresh = false) {
      this.loadingMessages = true
      try {
        const params = {
          conversationId: this.conversationId
        }
        
        const result = await this.socialStore.fetchMessages(params, refresh)
        
        if (refresh) {
          this.messageList = this.processMessages(result.list || [])
        } else {
          const newMessages = this.processMessages(result.list || [])
          this.messageList.unshift(...newMessages)
        }
      } catch (error) {
        console.error('加载消息失败:', error)
        toast.error('加载消息失败')
      } finally {
        this.loadingMessages = false
      }
    },
    
    /**
     * 加载更多消息
     */
    async loadMoreMessages() {
      if (!this.hasMoreMessages || this.loadingMessages) return
      await this.loadMessages(false)
    },
    
    /**
     * 处理消息列表
     */
    processMessages(messages) {
      return messages.map((message, index) => {
        // 判断是否显示时间
        const showTime = index === 0 || 
          (messages[index - 1] && 
           new Date(message.createdAt).getTime() - new Date(messages[index - 1].createdAt).getTime() > 5 * 60 * 1000)
        
        return {
          ...message,
          isMine: message.senderId === this.userStore.userId,
          showTime
        }
      })
    },
    
    /**
     * 发送消息
     */
    async sendMessage() {
      const content = this.inputText.trim()
      if (!content) return
      
      // 创建临时消息
      const tempMessage = {
        id: Date.now().toString(),
        content,
        type: 'text',
        isMine: true,
        status: 'sending',
        createdAt: new Date().toISOString(),
        showTime: false
      }
      
      // 添加到消息列表
      this.messageList.push(tempMessage)
      this.inputText = ''
      this.scrollToBottom()
      
      try {
        // 发送消息
        const message = await this.socialStore.sendMessage({
          conversationId: this.conversationId,
          content,
          type: 'text'
        })
        
        // 更新消息状态
        const index = this.messageList.findIndex(m => m.id === tempMessage.id)
        if (index > -1) {
          this.messageList[index] = {
            ...message,
            isMine: true,
            showTime: tempMessage.showTime
          }
        }
      } catch (error) {
        console.error('发送消息失败:', error)
        
        // 更新消息状态为失败
        const index = this.messageList.findIndex(m => m.id === tempMessage.id)
        if (index > -1) {
          this.messageList[index].status = 'failed'
        }
        
        toast.error('发送失败')
      }
    },
    
    /**
     * 重发消息
     */
    async resendMessage(message) {
      message.status = 'sending'
      
      try {
        const newMessage = await this.socialStore.sendMessage({
          conversationId: this.conversationId,
          content: message.content,
          type: message.type
        })
        
        // 更新消息
        const index = this.messageList.findIndex(m => m.id === message.id)
        if (index > -1) {
          this.messageList[index] = {
            ...newMessage,
            isMine: true,
            showTime: message.showTime
          }
        }
      } catch (error) {
        console.error('重发消息失败:', error)
        message.status = 'failed'
        toast.error('重发失败')
      }
    },
    
    /**
     * 选择图片
     */
    chooseImage() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          const tempFilePath = res.tempFilePaths[0]
          
          // 创建临时消息
          const tempMessage = {
            id: Date.now().toString(),
            content: tempFilePath,
            type: 'image',
            isMine: true,
            status: 'sending',
            createdAt: new Date().toISOString(),
            showTime: false
          }
          
          this.messageList.push(tempMessage)
          this.scrollToBottom()
          
          try {
            // 上传图片并发送消息
            const imageUrl = await this.socialStore.uploadMessageImage(tempFilePath)
            const message = await this.socialStore.sendMessage({
              conversationId: this.conversationId,
              content: imageUrl,
              type: 'image'
            })
            
            // 更新消息
            const index = this.messageList.findIndex(m => m.id === tempMessage.id)
            if (index > -1) {
              this.messageList[index] = {
                ...message,
                isMine: true,
                showTime: tempMessage.showTime
              }
            }
          } catch (error) {
            console.error('发送图片失败:', error)
            
            // 更新消息状态为失败
            const index = this.messageList.findIndex(m => m.id === tempMessage.id)
            if (index > -1) {
              this.messageList[index].status = 'failed'
            }
            
            toast.error('发送图片失败')
          }
        }
      })
    },
    
    /**
     * 插入表情
     */
    insertEmoji(emoji) {
      this.inputText += emoji
    },
    
    /**
     * 预览图片
     */
    previewImage(imageUrl) {
      uni.previewImage({
        urls: [imageUrl],
        current: imageUrl
      })
    },
    
    /**
     * 滚动到底部
     */
    scrollToBottom() {
      this.$nextTick(() => {
        if (this.messageList.length > 0) {
          const lastMessage = this.messageList[this.messageList.length - 1]
          this.scrollIntoView = `message-${lastMessage.id}`
        }
      })
    },
    
    /**
     * 标记消息为已读
     */
    async markMessagesAsRead() {
      try {
        await this.socialStore.markMessagesAsRead(this.conversationId)
      } catch (error) {
        console.error('标记消息已读失败:', error)
      }
    },
    
    /**
     * 清空聊天记录
     */
    clearMessages() {
      uni.showModal({
        title: '确认清空',
        content: '确定要清空聊天记录吗？清空后无法恢复。',
        confirmText: '清空',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            try {
              await this.socialStore.clearMessages(this.conversationId)
              this.messageList = []
              toast.success('聊天记录已清空')
              this.showMoreActions = false
            } catch (error) {
              console.error('清空聊天记录失败:', error)
              toast.error('清空失败')
            }
          }
        }
      })
    },
    
    /**
     * 举报用户
     */
    reportUser() {
      this.showMoreActions = false
      navigation.navigateTo('/pages/user/report-user', { userId: this.userId })
    },
    
    /**
     * 拉黑用户
     */
    blockUser() {
      uni.showModal({
        title: '确认拉黑',
        content: '拉黑后将无法收到该用户的消息，确定要拉黑吗？',
        confirmText: '拉黑',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            try {
              await this.socialStore.blockUser(this.userId)
              toast.success('已拉黑该用户')
              this.showMoreActions = false
              navigation.navigateBack()
            } catch (error) {
              console.error('拉黑用户失败:', error)
              toast.error('拉黑失败')
            }
          }
        }
      })
    },
    
    /**
     * 输入框事件
     */
    onInputFocus() {
      this.showEmojiPanel = false
      this.scrollToBottom()
    },
    
    onInputBlur() {
      // 延迟隐藏表情面板，避免点击表情时面板消失
      setTimeout(() => {
        this.showEmojiPanel = false
      }, 200)
    },
    
    /**
     * 格式化消息时间
     */
    formatMessageTime(timestamp) {
      return timeUtils.formatMessageTime(timestamp)
    },
    
    /**
     * 页面跳转
     */
    goBack() {
      navigation.navigateBack()
    },
    
    goToUserProfile() {
      navigation.navigateTo('/pages/user/user-profile', { userId: this.userId })
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-page {
  height: 100vh;
  background-color: $wish-bg-primary;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $wish-spacing-md;
}

.navbar-back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
}

.navbar-user {
  display: flex;
  align-items: center;
  flex: 1;
  margin-left: $wish-spacing-md;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
}

.user-info {
  flex: 1;
}

.user-nickname {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: 4rpx;
  @extend .ellipsis;
}

.user-status {
  font-size: $wish-font-xs;
  color: $wish-text-secondary;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.navbar-action {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 消息列表 */
.message-list {
  flex: 1;
  padding: $wish-spacing-sm;
}

.load-more-messages {
  text-align: center;
  padding: $wish-spacing-md;
}

.load-text {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
}

.message-items {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.message-item {
  display: flex;
  flex-direction: column;

  &--mine {
    align-items: flex-end;
  }
}

.time-divider {
  text-align: center;
  margin: $wish-spacing-md 0;
}

.time-text {
  font-size: $wish-font-xs;
  color: $wish-text-disabled;
  background-color: rgba(255, 255, 255, 0.8);
  padding: $wish-spacing-xs $wish-spacing-sm;
  border-radius: $wish-radius-sm;
}

.message-content {
  display: flex;
  align-items: flex-end;
  max-width: 80%;
}

.message-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin: 0 $wish-spacing-sm;
  flex-shrink: 0;
}

.message-bubble {
  max-width: 100%;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-lg;
  position: relative;

  &--other {
    background-color: $wish-bg-secondary;
    border-bottom-left-radius: $wish-radius-sm;

    &::before {
      content: '';
      position: absolute;
      left: -16rpx;
      bottom: 0;
      width: 0;
      height: 0;
      border: 8rpx solid transparent;
      border-right-color: $wish-bg-secondary;
      border-bottom-color: $wish-bg-secondary;
    }
  }

  &--mine {
    background-color: $wish-color-primary;
    border-bottom-right-radius: $wish-radius-sm;

    &::before {
      content: '';
      position: absolute;
      right: -16rpx;
      bottom: 0;
      width: 0;
      height: 0;
      border: 8rpx solid transparent;
      border-left-color: $wish-color-primary;
      border-bottom-color: $wish-color-primary;
    }
  }
}

.message-text {
  font-size: $wish-font-md;
  line-height: 1.6;
  word-wrap: break-word;

  .message-bubble--other & {
    color: $wish-text-primary;
  }

  .message-bubble--mine & {
    color: $wish-text-inverse;
  }
}

.message-image {
  max-width: 400rpx;
  max-height: 400rpx;
  border-radius: $wish-radius-md;
}

.blessing-message {
  display: flex;
  align-items: center;
}

.blessing-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: $wish-spacing-sm;
}

.blessing-text {
  font-size: $wish-font-md;

  .message-bubble--other & {
    color: $wish-text-primary;
  }

  .message-bubble--mine & {
    color: $wish-text-inverse;
  }
}

.message-status {
  display: flex;
  align-items: center;
  margin-top: $wish-spacing-xs;
  justify-content: flex-end;
}

.status-icon {
  width: 24rpx;
  height: 24rpx;

  &--sending {
    animation: rotate 1s linear infinite;
  }

  &--read {
    color: $wish-color-success;
  }

  &--failed {
    color: $wish-color-error;
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 输入区域 */
.input-area {
  background-color: $wish-bg-secondary;
  border-top: 2rpx solid $wish-border-light;
  padding: $wish-spacing-md;
}

.input-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-sm;
}

.toolbar-item {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: $wish-radius-md;
  margin-right: $wish-spacing-sm;
  transition: background-color 0.3s ease;

  &:active {
    background-color: $wish-bg-primary;
  }
}

.toolbar-icon {
  width: 32rpx;
  height: 32rpx;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: $wish-spacing-sm;
}

.message-input {
  flex: 1;
  margin-bottom: 0;
}

.send-button {
  min-height: auto;
  padding: $wish-spacing-sm $wish-spacing-md;
  min-width: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.voice-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 表情面板 */
.emoji-panel {
  background-color: $wish-bg-primary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-md;
  margin-top: $wish-spacing-sm;
  max-height: 400rpx;
  overflow-y: auto;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-sm;
}

.emoji-item {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  border-radius: $wish-radius-md;
  transition: background-color 0.3s ease;

  &:active {
    background-color: $wish-border-light;
  }
}

/* 更多操作模态框 */
.more-actions {
  padding: $wish-spacing-md 0;
}

.action-item {
  display: flex;
  align-items: center;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
  transition: background-color 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    background-color: $wish-bg-primary;
  }
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: $wish-spacing-md;
}

.action-text {
  font-size: $wish-font-md;
  color: $wish-text-primary;
}
</style>
