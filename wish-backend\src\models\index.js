/**
 * 模型索引文件
 * 初始化所有模型和关联关系
 */

const { getSequelize } = require('../database/connection');
const { User, initUserModel } = require('./User');
const { Wish, initWishModel } = require('./Wish');
const { Message, initMessageModel } = require('./Message');

// 存储所有模型
const models = {};

/**
 * 初始化所有模型
 */
function initModels() {
  const sequelize = getSequelize();
  
  if (!sequelize) {
    throw new Error('数据库连接未建立');
  }

  // 初始化基础模型
  models.User = initUserModel(sequelize);
  models.Wish = initWishModel(sequelize);
  models.Message = initMessageModel(sequelize);
  
  // 初始化关联模型
  initAssociationModels(sequelize);
  
  // 设置模型关联关系
  setupAssociations();
  
  return models;
}

/**
 * 初始化关联模型
 */
function initAssociationModels(sequelize) {
  const { DataTypes } = require('sequelize');
  
  // 心愿点赞模型
  models.WishLike = sequelize.define('WishLike', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    wish_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'wishes',
        key: 'id'
      }
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'wish_likes',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['wish_id', 'user_id'], unique: true },
      { fields: ['user_id'] },
      { fields: ['created_at'] }
    ]
  });
  
  // 心愿分享模型
  models.WishShare = sequelize.define('WishShare', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    wish_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'wishes',
        key: 'id'
      }
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    platform: {
      type: DataTypes.STRING(50),
      allowNull: false
    }
  }, {
    tableName: 'wish_shares',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['wish_id'] },
      { fields: ['user_id'] },
      { fields: ['platform'] },
      { fields: ['created_at'] }
    ]
  });
  
  // 赐福模型
  models.Blessing = sequelize.define('Blessing', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    wish_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'wishes',
        key: 'id'
      }
    },
    guardian_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    images: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    type: {
      type: DataTypes.ENUM('blessing', 'prayer', 'encouragement', 'advice'),
      defaultValue: 'blessing'
    },
    is_anonymous: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    thanked: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    thanked_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    thank_message: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'blessings',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['wish_id'] },
      { fields: ['guardian_id'] },
      { fields: ['created_at'] }
    ]
  });
  
  // 评论模型
  models.Comment = sequelize.define('Comment', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    wish_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'wishes',
        key: 'id'
      }
    },
    user_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    parent_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'comments',
        key: 'id'
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    likes: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    status: {
      type: DataTypes.ENUM('active', 'hidden', 'deleted'),
      defaultValue: 'active'
    }
  }, {
    tableName: 'comments',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['wish_id'] },
      { fields: ['user_id'] },
      { fields: ['parent_id'] },
      { fields: ['created_at'] }
    ]
  });
  
  // 用户关注模型
  models.Follow = sequelize.define('Follow', {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    follower_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    following_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'follows',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['follower_id', 'following_id'], unique: true },
      { fields: ['follower_id'] },
      { fields: ['following_id'] },
      { fields: ['created_at'] }
    ]
  });
}

/**
 * 设置模型关联关系
 */
function setupAssociations() {
  const { User, Wish, Message, WishLike, WishShare, Blessing, Comment, Follow } = models;
  
  // 用户与心愿的关联
  User.hasMany(Wish, { foreignKey: 'creator_id', as: 'wishes' });
  Wish.belongsTo(User, { foreignKey: 'creator_id', as: 'creator' });
  
  // 用户与消息的关联
  User.hasMany(Message, { foreignKey: 'sender_id', as: 'sentMessages' });
  User.hasMany(Message, { foreignKey: 'receiver_id', as: 'receivedMessages' });
  Message.belongsTo(User, { foreignKey: 'sender_id', as: 'sender' });
  Message.belongsTo(User, { foreignKey: 'receiver_id', as: 'receiver' });
  
  // 消息回复关联
  Message.hasMany(Message, { foreignKey: 'reply_to_id', as: 'replies' });
  Message.belongsTo(Message, { foreignKey: 'reply_to_id', as: 'replyTo' });
  
  // 心愿与点赞的关联
  Wish.hasMany(WishLike, { foreignKey: 'wish_id', as: 'wishLikes' });
  WishLike.belongsTo(Wish, { foreignKey: 'wish_id' });
  WishLike.belongsTo(User, { foreignKey: 'user_id' });
  
  // 心愿与分享的关联
  Wish.hasMany(WishShare, { foreignKey: 'wish_id', as: 'wishShares' });
  WishShare.belongsTo(Wish, { foreignKey: 'wish_id' });
  WishShare.belongsTo(User, { foreignKey: 'user_id' });

  // 心愿与赐福的关联
  Wish.hasMany(Blessing, { foreignKey: 'wish_id', as: 'wishBlessings' });
  Blessing.belongsTo(Wish, { foreignKey: 'wish_id' });
  Blessing.belongsTo(User, { foreignKey: 'guardian_id', as: 'guardian' });

  // 心愿与评论的关联
  Wish.hasMany(Comment, { foreignKey: 'wish_id', as: 'wishComments' });
  Comment.belongsTo(Wish, { foreignKey: 'wish_id' });
  Comment.belongsTo(User, { foreignKey: 'user_id', as: 'author' });
  
  // 评论的自关联（回复）
  Comment.hasMany(Comment, { foreignKey: 'parent_id', as: 'replies' });
  Comment.belongsTo(Comment, { foreignKey: 'parent_id', as: 'parent' });
  
  // 用户关注关联
  User.belongsToMany(User, {
    through: Follow,
    as: 'following',
    foreignKey: 'follower_id',
    otherKey: 'following_id'
  });
  User.belongsToMany(User, {
    through: Follow,
    as: 'followers',
    foreignKey: 'following_id',
    otherKey: 'follower_id'
  });
}

/**
 * 获取所有模型
 */
function getModels() {
  return models;
}

/**
 * 获取特定模型
 */
function getModel(modelName) {
  return models[modelName];
}

module.exports = {
  initModels,
  getModels,
  getModel,
  User,
  Wish,
  Message
};
