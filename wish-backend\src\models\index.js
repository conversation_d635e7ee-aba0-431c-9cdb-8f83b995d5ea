/**
 * 模型索引文件
 * 初始化所有模型和关联关系
 */

const { getSequelize } = require('../database/connection');
const { User, initUserModel } = require('./User');
const { Wish, initWishModel } = require('./Wish');

// 存储所有模型
const models = {};

/**
 * 初始化所有模型
 */
function initModels() {
  const sequelize = getSequelize();
  
  if (!sequelize) {
    throw new Error('数据库连接未建立');
  }

  // 初始化用户模型
  models.User = initUserModel();
  
  // 初始化心愿模型
  models.Wish = initWishModel();
  
  // 初始化心愿点赞模型
  models.WishLike = sequelize.define('WishLike', {
    id: {
      type: require('sequelize').DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    wish_id: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'wishes',
        key: 'id'
      }
    },
    user_id: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    }
  }, {
    tableName: 'wish_likes',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['wish_id', 'user_id'], unique: true },
      { fields: ['user_id'] },
      { fields: ['created_at'] }
    ]
  });
  
  // 初始化心愿分享模型
  models.WishShare = sequelize.define('WishShare', {
    id: {
      type: require('sequelize').DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    wish_id: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'wishes',
        key: 'id'
      }
    },
    user_id: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    platform: {
      type: require('sequelize').DataTypes.STRING(50),
      allowNull: false
    }
  }, {
    tableName: 'wish_shares',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['wish_id'] },
      { fields: ['user_id'] },
      { fields: ['platform'] },
      { fields: ['created_at'] }
    ]
  });
  
  // 初始化赐福模型
  models.Blessing = sequelize.define('Blessing', {
    id: {
      type: require('sequelize').DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    wish_id: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'wishes',
        key: 'id'
      }
    },
    guardian_id: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    content: {
      type: require('sequelize').DataTypes.TEXT,
      allowNull: false
    },
    images: {
      type: require('sequelize').DataTypes.JSON,
      defaultValue: []
    },
    type: {
      type: require('sequelize').DataTypes.ENUM('blessing', 'prayer', 'encouragement', 'advice'),
      defaultValue: 'blessing'
    },
    is_anonymous: {
      type: require('sequelize').DataTypes.BOOLEAN,
      defaultValue: false
    },
    thanked: {
      type: require('sequelize').DataTypes.BOOLEAN,
      defaultValue: false
    },
    thanked_at: {
      type: require('sequelize').DataTypes.DATE,
      allowNull: true
    },
    thank_message: {
      type: require('sequelize').DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'blessings',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['wish_id'] },
      { fields: ['guardian_id'] },
      { fields: ['created_at'] }
    ]
  });
  
  // 初始化评论模型
  models.Comment = sequelize.define('Comment', {
    id: {
      type: require('sequelize').DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    wish_id: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'wishes',
        key: 'id'
      }
    },
    user_id: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    parent_id: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'comments',
        key: 'id'
      }
    },
    content: {
      type: require('sequelize').DataTypes.TEXT,
      allowNull: false
    },
    likes: {
      type: require('sequelize').DataTypes.INTEGER,
      defaultValue: 0
    },
    status: {
      type: require('sequelize').DataTypes.ENUM('active', 'hidden', 'deleted'),
      defaultValue: 'active'
    }
  }, {
    tableName: 'comments',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['wish_id'] },
      { fields: ['user_id'] },
      { fields: ['parent_id'] },
      { fields: ['created_at'] }
    ]
  });
  
  // 初始化举报模型
  models.Report = sequelize.define('Report', {
    id: {
      type: require('sequelize').DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    reporter_id: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    target_type: {
      type: require('sequelize').DataTypes.ENUM('user', 'wish', 'comment', 'blessing'),
      allowNull: false
    },
    target_id: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: false
    },
    reason: {
      type: require('sequelize').DataTypes.STRING(100),
      allowNull: false
    },
    description: {
      type: require('sequelize').DataTypes.TEXT,
      allowNull: true
    },
    evidence: {
      type: require('sequelize').DataTypes.JSON,
      defaultValue: []
    },
    contact: {
      type: require('sequelize').DataTypes.STRING(100),
      allowNull: true
    },
    status: {
      type: require('sequelize').DataTypes.ENUM('pending', 'approved', 'rejected'),
      defaultValue: 'pending'
    },
    processed_by: {
      type: require('sequelize').DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    processed_at: {
      type: require('sequelize').DataTypes.DATE,
      allowNull: true
    },
    process_reason: {
      type: require('sequelize').DataTypes.TEXT,
      allowNull: true
    }
  }, {
    tableName: 'reports',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['reporter_id'] },
      { fields: ['target_type', 'target_id'] },
      { fields: ['status'] },
      { fields: ['created_at'] }
    ]
  });

  // 设置模型关联关系
  setupAssociations();
  
  return models;
}

/**
 * 设置模型关联关系
 */
function setupAssociations() {
  const { User, Wish, WishLike, WishShare, Blessing, Comment, Report } = models;
  
  // 用户与心愿的关联
  User.hasMany(Wish, { foreignKey: 'creator_id', as: 'wishes' });
  Wish.belongsTo(User, { foreignKey: 'creator_id', as: 'creator' });
  
  // 心愿与点赞的关联
  Wish.hasMany(WishLike, { foreignKey: 'wish_id', as: 'likes' });
  WishLike.belongsTo(Wish, { foreignKey: 'wish_id' });
  WishLike.belongsTo(User, { foreignKey: 'user_id' });
  
  // 心愿与分享的关联
  Wish.hasMany(WishShare, { foreignKey: 'wish_id', as: 'shares' });
  WishShare.belongsTo(Wish, { foreignKey: 'wish_id' });
  WishShare.belongsTo(User, { foreignKey: 'user_id' });
  
  // 心愿与赐福的关联
  Wish.hasMany(Blessing, { foreignKey: 'wish_id', as: 'blessings' });
  Blessing.belongsTo(Wish, { foreignKey: 'wish_id' });
  Blessing.belongsTo(User, { foreignKey: 'guardian_id', as: 'guardian' });
  
  // 心愿与评论的关联
  Wish.hasMany(Comment, { foreignKey: 'wish_id', as: 'comments' });
  Comment.belongsTo(Wish, { foreignKey: 'wish_id' });
  Comment.belongsTo(User, { foreignKey: 'user_id', as: 'author' });
  
  // 评论的自关联（回复）
  Comment.hasMany(Comment, { foreignKey: 'parent_id', as: 'replies' });
  Comment.belongsTo(Comment, { foreignKey: 'parent_id', as: 'parent' });
  
  // 用户与举报的关联
  User.hasMany(Report, { foreignKey: 'reporter_id', as: 'reports' });
  Report.belongsTo(User, { foreignKey: 'reporter_id', as: 'reporter' });
  Report.belongsTo(User, { foreignKey: 'processed_by', as: 'processor' });
}

/**
 * 获取所有模型
 */
function getModels() {
  return models;
}

/**
 * 获取特定模型
 */
function getModel(modelName) {
  return models[modelName];
}

module.exports = {
  initModels,
  getModels,
  getModel,
  User,
  Wish
};
