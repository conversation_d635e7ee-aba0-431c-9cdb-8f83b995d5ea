/**
 * 文件上传路由
 * 处理文件上传相关的API请求
 */

const express = require('express');
const multer = require('multer');
// const sharp = require('sharp'); // 临时注释，解决 Windows 兼容性问题
let sharp;
try {
  sharp = require('sharp');
} catch (error) {
  console.warn('Sharp 模块加载失败，图片处理功能将被禁用:', error.message);
  sharp = null;
}
const path = require('path');
const fs = require('fs-extra');
const { v4: uuidv4 } = require('uuid');
const authMiddleware = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const logger = require('../utils/logger');
const config = require('../config');

const router = express.Router();

// 确保上传目录存在
const uploadDir = path.join(process.cwd(), config.upload.destination);
fs.ensureDirSync(uploadDir);
fs.ensureDirSync(path.join(uploadDir, 'images'));
fs.ensureDirSync(path.join(uploadDir, 'avatars'));
fs.ensureDirSync(path.join(uploadDir, 'temp'));

// 配置multer存储
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadType = req.params.type || 'temp';
    const destPath = path.join(uploadDir, uploadType);
    fs.ensureDirSync(destPath);
    cb(null, destPath);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}${path.extname(file.originalname)}`;
    cb(null, uniqueName);
  }
});

// 文件过滤器
const fileFilter = (req, file, cb) => {
  // 检查文件类型
  if (config.upload.allowedTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new AppError('不支持的文件类型', 400, 'UNSUPPORTED_FILE_TYPE'), false);
  }
};

// 配置multer
const upload = multer({
  storage: storage,
  limits: {
    fileSize: config.upload.maxFileSize,
    files: 5 // 最多5个文件
  },
  fileFilter: fileFilter
});

/**
 * 图片处理函数
 */
async function processImage(inputPath, outputPath, options = {}) {
  // 如果 Sharp 不可用，直接复制文件
  if (!sharp) {
    logger.warn('Sharp 不可用，跳过图片处理');
    await fs.copy(inputPath, outputPath);
    await fs.remove(inputPath);
    return outputPath;
  }

  const {
    width = 800,
    height = 600,
    quality = 80,
    format = 'jpeg'
  } = options;

  try {
    await sharp(inputPath)
      .resize(width, height, {
        fit: 'inside',
        withoutEnlargement: true
      })
      .jpeg({ quality })
      .toFile(outputPath);

    // 删除原文件
    await fs.remove(inputPath);

    return outputPath;
  } catch (error) {
    logger.error('图片处理失败:', error);
    throw new AppError('图片处理失败', 500, 'IMAGE_PROCESSING_ERROR');
  }
}

/**
 * 生成缩略图
 */
async function generateThumbnail(inputPath, outputPath) {
  // 如果 Sharp 不可用，直接复制文件
  if (!sharp) {
    logger.warn('Sharp 不可用，跳过缩略图生成');
    await fs.copy(inputPath, outputPath);
    return outputPath;
  }

  try {
    await sharp(inputPath)
      .resize(200, 200, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: 70 })
      .toFile(outputPath);

    return outputPath;
  } catch (error) {
    logger.error('缩略图生成失败:', error);
    throw new AppError('缩略图生成失败', 500, 'THUMBNAIL_GENERATION_ERROR');
  }
}

/**
 * 上传图片
 */
router.post('/images', authMiddleware.required, upload.array('images', 5), catchAsync(async (req, res) => {
  if (!req.files || req.files.length === 0) {
    throw new AppError('请选择要上传的图片', 400, 'NO_FILES_UPLOADED');
  }

  const uploadedFiles = [];

  for (const file of req.files) {
    try {
      const originalPath = file.path;
      const filename = file.filename;
      const processedFilename = `processed_${filename}`;
      const thumbnailFilename = `thumb_${filename}`;
      
      const processedPath = path.join(path.dirname(originalPath), processedFilename);
      const thumbnailPath = path.join(path.dirname(originalPath), thumbnailFilename);

      // 处理图片
      await processImage(originalPath, processedPath, {
        width: 1200,
        height: 900,
        quality: 85
      });

      // 生成缩略图
      await generateThumbnail(processedPath, thumbnailPath);

      // 获取文件信息
      const stats = await fs.stat(processedPath);
      let imageInfo = { width: null, height: null, format: null };

      if (sharp) {
        try {
          imageInfo = await sharp(processedPath).metadata();
        } catch (error) {
          logger.warn('获取图片元数据失败:', error);
        }
      }

      uploadedFiles.push({
        originalName: file.originalname,
        filename: processedFilename,
        thumbnailFilename: thumbnailFilename,
        url: `${config.upload.publicPath}images/${processedFilename}`,
        thumbnailUrl: `${config.upload.publicPath}images/${thumbnailFilename}`,
        size: stats.size,
        width: imageInfo.width,
        height: imageInfo.height,
        format: imageInfo.format
      });

    } catch (error) {
      logger.error(`处理文件 ${file.originalname} 失败:`, error);
      // 清理已上传的文件
      try {
        await fs.remove(file.path);
      } catch (cleanupError) {
        logger.error('清理文件失败:', cleanupError);
      }
    }
  }

  if (uploadedFiles.length === 0) {
    throw new AppError('所有文件处理失败', 500, 'ALL_FILES_FAILED');
  }

  logger.info(`用户 ${req.user.username} 上传了 ${uploadedFiles.length} 张图片`);

  res.json({
    message: '图片上传成功',
    files: uploadedFiles
  });
}));

/**
 * 上传头像
 */
router.post('/avatar', authMiddleware.required, upload.single('avatar'), catchAsync(async (req, res) => {
  if (!req.file) {
    throw new AppError('请选择头像文件', 400, 'NO_AVATAR_UPLOADED');
  }

  const file = req.file;
  const originalPath = file.path;
  const filename = `avatar_${req.user.userId}_${Date.now()}.jpg`;
  const avatarPath = path.join(uploadDir, 'avatars', filename);

  try {
    // 处理头像（正方形，200x200）
    if (sharp) {
      await sharp(originalPath)
        .resize(200, 200, {
          fit: 'cover',
          position: 'center'
        })
        .jpeg({ quality: 90 })
        .toFile(avatarPath);
    } else {
      // Sharp 不可用时，直接复制文件
      logger.warn('Sharp 不可用，跳过头像处理');
      await fs.copy(originalPath, avatarPath);
    }

    // 删除原文件
    await fs.remove(originalPath);

    // 更新用户头像
    const { getModel } = require('../models');
    const User = getModel('User');
    const avatarUrl = `${config.upload.publicPath}avatars/${filename}`;
    
    await User.update(
      { avatar: avatarUrl },
      { where: { id: req.user.userId } }
    );

    logger.info(`用户 ${req.user.username} 更新头像`);

    res.json({
      message: '头像上传成功',
      avatarUrl: avatarUrl
    });

  } catch (error) {
    // 清理文件
    try {
      await fs.remove(originalPath);
      await fs.remove(avatarPath);
    } catch (cleanupError) {
      logger.error('清理文件失败:', cleanupError);
    }
    throw error;
  }
}));

/**
 * 删除文件
 */
router.delete('/files/:filename', authMiddleware.required, catchAsync(async (req, res) => {
  const { filename } = req.params;
  
  // 安全检查：防止路径遍历攻击
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    throw new AppError('无效的文件名', 400, 'INVALID_FILENAME');
  }

  // 查找文件
  const possiblePaths = [
    path.join(uploadDir, 'images', filename),
    path.join(uploadDir, 'avatars', filename),
    path.join(uploadDir, 'temp', filename)
  ];

  let fileFound = false;
  for (const filePath of possiblePaths) {
    if (await fs.pathExists(filePath)) {
      await fs.remove(filePath);
      fileFound = true;
      
      // 同时删除缩略图
      const thumbnailPath = path.join(path.dirname(filePath), `thumb_${filename}`);
      if (await fs.pathExists(thumbnailPath)) {
        await fs.remove(thumbnailPath);
      }
      
      break;
    }
  }

  if (!fileFound) {
    throw new AppError('文件不存在', 404, 'FILE_NOT_FOUND');
  }

  logger.info(`用户 ${req.user.username} 删除文件: ${filename}`);

  res.json({
    message: '文件删除成功'
  });
}));

/**
 * 获取文件信息
 */
router.get('/files/:filename/info', catchAsync(async (req, res) => {
  const { filename } = req.params;
  
  // 安全检查
  if (filename.includes('..') || filename.includes('/') || filename.includes('\\')) {
    throw new AppError('无效的文件名', 400, 'INVALID_FILENAME');
  }

  // 查找文件
  const possiblePaths = [
    path.join(uploadDir, 'images', filename),
    path.join(uploadDir, 'avatars', filename),
    path.join(uploadDir, 'temp', filename)
  ];

  let fileInfo = null;
  for (const filePath of possiblePaths) {
    if (await fs.pathExists(filePath)) {
      const stats = await fs.stat(filePath);
      const ext = path.extname(filename).toLowerCase();
      
      fileInfo = {
        filename: filename,
        size: stats.size,
        createdAt: stats.birthtime,
        modifiedAt: stats.mtime,
        type: ext
      };

      // 如果是图片，获取图片信息
      if (['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext) && sharp) {
        try {
          const imageInfo = await sharp(filePath).metadata();
          fileInfo.width = imageInfo.width;
          fileInfo.height = imageInfo.height;
          fileInfo.format = imageInfo.format;
        } catch (error) {
          logger.warn('获取图片信息失败:', error);
        }
      }
      
      break;
    }
  }

  if (!fileInfo) {
    throw new AppError('文件不存在', 404, 'FILE_NOT_FOUND');
  }

  res.json({
    file: fileInfo
  });
}));

/**
 * 清理临时文件
 */
router.post('/cleanup', authMiddleware.requireAdmin, catchAsync(async (req, res) => {
  const tempDir = path.join(uploadDir, 'temp');
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24小时

  let cleanedCount = 0;

  try {
    const files = await fs.readdir(tempDir);
    
    for (const file of files) {
      const filePath = path.join(tempDir, file);
      const stats = await fs.stat(filePath);
      
      if (now - stats.mtime.getTime() > maxAge) {
        await fs.remove(filePath);
        cleanedCount++;
      }
    }

    logger.info(`清理了 ${cleanedCount} 个临时文件`);

    res.json({
      message: '临时文件清理完成',
      cleanedCount: cleanedCount
    });

  } catch (error) {
    logger.error('清理临时文件失败:', error);
    throw new AppError('清理临时文件失败', 500, 'CLEANUP_FAILED');
  }
}));

module.exports = router;
