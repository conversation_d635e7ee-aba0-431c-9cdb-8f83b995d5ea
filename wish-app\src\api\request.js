/**
 * 愿境应用API请求封装
 * 基于uni.request封装，提供统一的请求处理、错误处理、拦截器等功能
 */

import { APP_CONFIG, API_CONFIG, STORAGE_KEYS } from '@/config'
import { storage, toast } from '@/utils'

/**
 * 请求拦截器
 */
const requestInterceptors = []

/**
 * 响应拦截器
 */
const responseInterceptors = []

/**
 * 添加请求拦截器
 */
export function addRequestInterceptor(interceptor) {
  requestInterceptors.push(interceptor)
}

/**
 * 添加响应拦截器
 */
export function addResponseInterceptor(interceptor) {
  responseInterceptors.push(interceptor)
}

/**
 * 请求类
 */
class Request {
  constructor() {
    this.baseURL = APP_CONFIG.baseURL
    this.timeout = API_CONFIG.timeout
    this.retryTimes = API_CONFIG.retryTimes
    
    // 默认请求拦截器 - 添加token
    this.addDefaultRequestInterceptor()
    
    // 默认响应拦截器 - 处理通用错误
    this.addDefaultResponseInterceptor()
  }
  
  /**
   * 添加默认请求拦截器
   */
  addDefaultRequestInterceptor() {
    addRequestInterceptor((config) => {
      // 添加token到请求头
      const token = storage.get(STORAGE_KEYS.TOKEN)
      if (token) {
        config.header = {
          ...config.header,
          'Authorization': `Bearer ${token}`
        }
      }
      
      // 添加通用请求头
      config.header = {
        'Content-Type': 'application/json',
        'X-App-Version': APP_CONFIG.version,
        ...config.header
      }
      
      return config
    })
  }
  
  /**
   * 添加默认响应拦截器
   */
  addDefaultResponseInterceptor() {
    addResponseInterceptor((response) => {
      const { statusCode, data } = response
      
      // HTTP状态码处理
      if (statusCode >= 200 && statusCode < 300) {
        // 业务状态码处理
        if (data.code === 0) {
          return Promise.resolve(data.data)
        } else if (data.code === 401) {
          // token过期，清除本地存储并跳转登录
          this.handleTokenExpired()
          return Promise.reject(new Error(data.message || '登录已过期'))
        } else {
          // 其他业务错误
          return Promise.reject(new Error(data.message || '请求失败'))
        }
      } else {
        // HTTP错误
        return Promise.reject(new Error(this.getHttpErrorMessage(statusCode)))
      }
    })
  }
  
  /**
   * 处理token过期
   */
  handleTokenExpired() {
    storage.remove(STORAGE_KEYS.TOKEN)
    storage.remove(STORAGE_KEYS.USER_INFO)
    
    // 跳转到登录页
    uni.reLaunch({
      url: '/pages/auth/login'
    })
  }
  
  /**
   * 获取HTTP错误信息
   */
  getHttpErrorMessage(statusCode) {
    const errorMessages = {
      400: '请求参数错误',
      401: '未授权访问',
      403: '禁止访问',
      404: '请求资源不存在',
      405: '请求方法不允许',
      408: '请求超时',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用',
      504: '网关超时'
    }
    
    return errorMessages[statusCode] || `请求失败 (${statusCode})`
  }
  
  /**
   * 执行请求拦截器
   */
  async executeRequestInterceptors(config) {
    let processedConfig = config
    
    for (const interceptor of requestInterceptors) {
      try {
        processedConfig = await interceptor(processedConfig)
      } catch (error) {
        console.error('请求拦截器执行失败:', error)
      }
    }
    
    return processedConfig
  }
  
  /**
   * 执行响应拦截器
   */
  async executeResponseInterceptors(response) {
    let processedResponse = response
    
    for (const interceptor of responseInterceptors) {
      try {
        processedResponse = await interceptor(processedResponse)
      } catch (error) {
        return Promise.reject(error)
      }
    }
    
    return processedResponse
  }
  
  /**
   * 核心请求方法
   */
  async request(config) {
    // 处理URL
    const url = config.url.startsWith('http') ? config.url : this.baseURL + config.url
    
    // 默认配置
    const defaultConfig = {
      method: 'GET',
      timeout: this.timeout,
      header: {},
      data: null
    }
    
    // 合并配置
    const finalConfig = {
      ...defaultConfig,
      ...config,
      url
    }
    
    try {
      // 执行请求拦截器
      const processedConfig = await this.executeRequestInterceptors(finalConfig)
      
      // 发起请求
      const response = await this.makeRequest(processedConfig)
      
      // 执行响应拦截器
      return await this.executeResponseInterceptors(response)
      
    } catch (error) {
      // 错误处理
      console.error('请求失败:', error)
      
      // 显示错误提示
      if (error.message && !config.silent) {
        toast.error(error.message)
      }
      
      throw error
    }
  }
  
  /**
   * 发起实际请求（支持重试）
   */
  makeRequest(config, retryCount = 0) {
    return new Promise((resolve, reject) => {
      uni.request({
        ...config,
        success: (response) => {
          resolve(response)
        },
        fail: (error) => {
          // 网络错误重试
          if (retryCount < this.retryTimes && this.shouldRetry(error)) {
            console.log(`请求失败，正在重试 (${retryCount + 1}/${this.retryTimes})`)
            setTimeout(() => {
              this.makeRequest(config, retryCount + 1)
                .then(resolve)
                .catch(reject)
            }, 1000 * (retryCount + 1)) // 递增延迟
          } else {
            reject(new Error(error.errMsg || '网络请求失败'))
          }
        }
      })
    })
  }
  
  /**
   * 判断是否应该重试
   */
  shouldRetry(error) {
    // 网络错误或超时错误才重试
    return error.errMsg && (
      error.errMsg.includes('timeout') ||
      error.errMsg.includes('network') ||
      error.errMsg.includes('fail')
    )
  }
  
  /**
   * GET请求
   */
  get(url, params = {}, config = {}) {
    // 将参数拼接到URL
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    
    const finalUrl = queryString ? `${url}?${queryString}` : url
    
    return this.request({
      url: finalUrl,
      method: 'GET',
      ...config
    })
  }
  
  /**
   * POST请求
   */
  post(url, data = {}, config = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...config
    })
  }
  
  /**
   * PUT请求
   */
  put(url, data = {}, config = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...config
    })
  }
  
  /**
   * DELETE请求
   */
  delete(url, config = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...config
    })
  }
  
  /**
   * 文件上传
   */
  upload(url, filePath, formData = {}, config = {}) {
    return new Promise((resolve, reject) => {
      const token = storage.get(STORAGE_KEYS.TOKEN)
      
      uni.uploadFile({
        url: this.baseURL + url,
        filePath,
        name: 'file',
        formData,
        header: {
          'Authorization': token ? `Bearer ${token}` : '',
          ...config.header
        },
        success: (response) => {
          try {
            const data = JSON.parse(response.data)
            if (data.code === 0) {
              resolve(data.data)
            } else {
              reject(new Error(data.message || '上传失败'))
            }
          } catch (error) {
            reject(new Error('响应数据解析失败'))
          }
        },
        fail: (error) => {
          reject(new Error(error.errMsg || '上传失败'))
        }
      })
    })
  }
}

// 创建请求实例
const request = new Request()

export default request
