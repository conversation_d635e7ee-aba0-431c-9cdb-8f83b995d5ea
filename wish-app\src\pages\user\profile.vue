<!--
  愿境个人中心页面
  用户个人信息展示和管理
-->
<template>
  <view class="profile-page">
    <!-- 头部背景 -->
    <view class="profile-header">
      <view class="header-background"></view>
      
      <!-- 用户信息 -->
      <view class="user-info">
        <view class="avatar-container" @click="changeAvatar">
          <image :src="userStore.avatar" class="user-avatar" />
          <view class="avatar-edit">
            <image src="/static/icons/camera.png" class="edit-icon" />
          </view>
        </view>
        
        <view class="user-details">
          <text class="user-nickname">{{ userStore.nickname }}</text>
          <text class="user-id">ID: {{ userStore.userId }}</text>
          <view class="user-role">
            <text class="role-text">当前身份：{{ currentRoleText }}</text>
            <wish-button
              type="ghost"
              size="small"
              :text="switchRoleText"
              @click="showRoleSwitchModal"
              class="switch-role-btn"
            />
          </view>
        </view>
      </view>
      
      <!-- 数据统计 -->
      <view class="stats-container">
        <view class="stat-item">
          <text class="stat-value">{{ userStore.userStats.wishPower }}</text>
          <text class="stat-label">心愿力</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{ userStore.userStats.meritPoints }}</text>
          <text class="stat-label">功德值</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{ userStore.userStats.totalWishes }}</text>
          <text class="stat-label">心愿数</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{ userStore.userStats.totalBlessings }}</text>
          <text class="stat-label">赐福数</text>
        </view>
      </view>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <!-- 我的内容 -->
      <view class="menu-group">
        <text class="group-title">我的内容</text>
        <view class="menu-list">
          <view class="menu-item" @click="goToMyWishes">
            <view class="menu-icon">
              <image src="/static/icons/my-wishes.png" class="icon-img" />
            </view>
            <text class="menu-text">我的心愿</text>
            <view class="menu-arrow">
              <image src="/static/icons/arrow-right.png" class="arrow-img" />
            </view>
          </view>
          
          <view class="menu-item" @click="goToMyBlessings">
            <view class="menu-icon">
              <image src="/static/icons/my-blessings.png" class="icon-img" />
            </view>
            <text class="menu-text">我的赐福</text>
            <view class="menu-arrow">
              <image src="/static/icons/arrow-right.png" class="arrow-img" />
            </view>
          </view>
          
          <view class="menu-item" @click="goToMyComments">
            <view class="menu-icon">
              <image src="/static/icons/comments.png" class="icon-img" />
            </view>
            <text class="menu-text">我的评论</text>
            <view class="menu-arrow">
              <image src="/static/icons/arrow-right.png" class="arrow-img" />
            </view>
          </view>
        </view>
      </view>
      
      <!-- 设置选项 -->
      <view class="menu-group">
        <text class="group-title">设置</text>
        <view class="menu-list">
          <view class="menu-item" @click="goToEditProfile">
            <view class="menu-icon">
              <image src="/static/icons/edit-profile.png" class="icon-img" />
            </view>
            <text class="menu-text">编辑资料</text>
            <view class="menu-arrow">
              <image src="/static/icons/arrow-right.png" class="arrow-img" />
            </view>
          </view>
          
          <view class="menu-item" @click="goToSettings">
            <view class="menu-icon">
              <image src="/static/icons/settings.png" class="icon-img" />
            </view>
            <text class="menu-text">应用设置</text>
            <view class="menu-arrow">
              <image src="/static/icons/arrow-right.png" class="arrow-img" />
            </view>
          </view>
          
          <view class="menu-item" @click="goToHelp">
            <view class="menu-icon">
              <image src="/static/icons/help.png" class="icon-img" />
            </view>
            <text class="menu-text">帮助与反馈</text>
            <view class="menu-arrow">
              <image src="/static/icons/arrow-right.png" class="arrow-img" />
            </view>
          </view>
          
          <view class="menu-item" @click="goToAbout">
            <view class="menu-icon">
              <image src="/static/icons/about.png" class="icon-img" />
            </view>
            <text class="menu-text">关于愿境</text>
            <view class="menu-arrow">
              <image src="/static/icons/arrow-right.png" class="arrow-img" />
            </view>
          </view>
        </view>
      </view>
      
      <!-- 退出登录 -->
      <view class="logout-section">
        <wish-button
          type="error"
          size="large"
          text="退出登录"
          :loading="logoutLoading"
          @click="showLogoutConfirm"
          class="logout-button"
        />
      </view>
    </view>
    
    <!-- 角色切换模态框 -->
    <wish-modal
      v-model:visible="showRoleModal"
      title="切换身份"
      :mask-closable="false"
    >
      <view class="role-switch-content">
        <view class="role-option" @click="switchRole('wisher')">
          <view class="role-icon">
            <image src="/static/icons/wisher.png" class="role-img" />
          </view>
          <view class="role-info">
            <text class="role-name">祈愿者</text>
            <text class="role-desc">发布心愿，寻求守护</text>
          </view>
          <view class="role-check" v-if="userStore.currentRole === 'wisher'">
            <image src="/static/icons/check.png" class="check-img" />
          </view>
        </view>
        
        <view class="role-option" @click="switchRole('guardian')">
          <view class="role-icon">
            <image src="/static/icons/guardian.png" class="role-img" />
          </view>
          <view class="role-info">
            <text class="role-name">守护者</text>
            <text class="role-desc">回应心愿，传递温暖</text>
          </view>
          <view class="role-check" v-if="userStore.currentRole === 'guardian'">
            <image src="/static/icons/check.png" class="check-img" />
          </view>
        </view>
      </view>
      
      <template #footer>
        <wish-button
          type="ghost"
          text="取消"
          @click="showRoleModal = false"
          class="modal-button"
        />
      </template>
    </wish-modal>
  </view>
</template>

<script>
import { useUserStore } from '@/store'
import { navigation, toast } from '@/utils'

export default {
  data() {
    return {
      showRoleModal: false,
      logoutLoading: false
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    currentRoleText() {
      return this.userStore.isWisher ? '祈愿者' : '守护者'
    },
    
    switchRoleText() {
      return this.userStore.isWisher ? '成为守护者' : '成为祈愿者'
    }
  },
  
  onShow() {
    this.refreshUserData()
  },
  
  methods: {
    /**
     * 刷新用户数据
     */
    async refreshUserData() {
      try {
        await Promise.all([
          this.userStore.fetchUserProfile(),
          this.userStore.fetchUserStats()
        ])
      } catch (error) {
        console.error('刷新用户数据失败:', error)
      }
    },
    
    /**
     * 更换头像
     */
    changeAvatar() {
      uni.chooseImage({
        count: 1,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: async (res) => {
          try {
            const tempFilePath = res.tempFilePaths[0]
            // TODO: 上传头像
            toast.info('头像上传功能开发中')
          } catch (error) {
            console.error('上传头像失败:', error)
            toast.error('上传头像失败')
          }
        }
      })
    },
    
    /**
     * 显示角色切换模态框
     */
    showRoleSwitchModal() {
      this.showRoleModal = true
    },
    
    /**
     * 切换角色
     */
    async switchRole(role) {
      if (role === this.userStore.currentRole) {
        this.showRoleModal = false
        return
      }
      
      try {
        await this.userStore.switchRole(role)
        toast.success(`已切换为${role === 'wisher' ? '祈愿者' : '守护者'}`)
        this.showRoleModal = false
      } catch (error) {
        console.error('切换角色失败:', error)
        toast.error('切换角色失败')
      }
    },
    
    /**
     * 显示退出登录确认
     */
    showLogoutConfirm() {
      uni.showModal({
        title: '确认退出',
        content: '确定要退出登录吗？',
        confirmText: '退出',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.handleLogout()
          }
        }
      })
    },
    
    /**
     * 处理退出登录
     */
    async handleLogout() {
      this.logoutLoading = true
      try {
        await this.userStore.logout()
        toast.success('已退出登录')
        
        // 跳转到登录页
        setTimeout(() => {
          navigation.redirectTo('/pages/auth/login')
        }, 1000)
        
      } catch (error) {
        console.error('退出登录失败:', error)
        toast.error('退出登录失败')
      } finally {
        this.logoutLoading = false
      }
    },
    
    /**
     * 页面跳转方法
     */
    goToMyWishes() {
      navigation.navigateTo('/pages/wisher/my-wishes')
    },
    
    goToMyBlessings() {
      navigation.navigateTo('/pages/guardian/my-blessings')
    },
    
    goToMyComments() {
      navigation.navigateTo('/pages/social/my-comments')
    },
    
    goToEditProfile() {
      navigation.navigateTo('/pages/user/edit-profile')
    },
    
    goToSettings() {
      navigation.navigateTo('/pages/user/settings')
    },
    
    goToHelp() {
      navigation.navigateTo('/pages/help/index')
    },
    
    goToAbout() {
      navigation.navigateTo('/pages/about/index')
    }
  }
}
</script>

<style lang="scss" scoped>
.profile-page {
  min-height: 100vh;
  background-color: $wish-bg-primary;
}

/* 头部区域 */
.profile-header {
  position: relative;
  background-color: $wish-bg-secondary;
  padding-bottom: $wish-spacing-lg;
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 300rpx;
  background: linear-gradient(135deg, $wish-color-primary 0%, $wish-color-secondary 100%);
  opacity: 0.1;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  padding: $wish-spacing-xl $wish-spacing-md $wish-spacing-lg;
  padding-top: calc($wish-spacing-xl + constant(safe-area-inset-top));
  padding-top: calc($wish-spacing-xl + env(safe-area-inset-top));
}

.avatar-container {
  position: relative;
  margin-right: $wish-spacing-md;
}

.user-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid $wish-bg-secondary;
  box-shadow: $wish-shadow-md;
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40rpx;
  height: 40rpx;
  background-color: $wish-color-primary;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid $wish-bg-secondary;
}

.edit-icon {
  width: 20rpx;
  height: 20rpx;
}

.user-details {
  flex: 1;
}

.user-nickname {
  display: block;
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.user-id {
  display: block;
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-sm;
}

.user-role {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.role-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.switch-role-btn {
  padding: 8rpx 16rpx;
  min-height: auto;
}

/* 数据统计 */
.stats-container {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: $wish-bg-secondary;
  margin: 0 $wish-spacing-md;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-md;
  box-shadow: $wish-shadow-sm;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-value {
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-color-primary;
  margin-bottom: $wish-spacing-xs;
}

.stat-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background-color: $wish-border-light;
}

/* 功能菜单 */
.menu-section {
  padding: $wish-spacing-lg $wish-spacing-md;
}

.menu-group {
  margin-bottom: $wish-spacing-lg;
}

.group-title {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-md;
  padding-left: $wish-spacing-sm;
}

.menu-list {
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  overflow: hidden;
  box-shadow: $wish-shadow-sm;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: $wish-spacing-md;
  border-bottom: 2rpx solid $wish-border-light;
  transition: background-color 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:active {
    background-color: $wish-bg-primary;
  }
}

.menu-icon {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $wish-spacing-md;
}

.icon-img {
  width: 32rpx;
  height: 32rpx;
}

.menu-text {
  flex: 1;
  font-size: $wish-font-md;
  color: $wish-text-primary;
}

.menu-arrow {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-img {
  width: 16rpx;
  height: 16rpx;
}

/* 退出登录 */
.logout-section {
  margin-top: $wish-spacing-xl;
}

.logout-button {
  width: 100%;
}

/* 角色切换模态框 */
.role-switch-content {
  padding: $wish-spacing-md 0;
}

.role-option {
  display: flex;
  align-items: center;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
  transition: background-color 0.3s ease;

  &:active {
    background-color: $wish-bg-primary;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.role-icon {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(232, 180, 160, 0.1);
  border-radius: $wish-radius-md;
  margin-right: $wish-spacing-md;
}

.role-img {
  width: 40rpx;
  height: 40rpx;
}

.role-info {
  flex: 1;
}

.role-name {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.role-desc {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.role-check {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.check-img {
  width: 24rpx;
  height: 24rpx;
}

.modal-button {
  width: 100%;
}
</style>
