<!--
  愿境评论页面
  独立的评论页面，支持更丰富的评论功能
-->
<template>
  <view class="comments-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar safe-area-top">
      <view class="navbar-content">
        <view class="navbar-back" @click="goBack">
          <image src="/static/icons/arrow-left.png" class="back-icon" />
        </view>
        <text class="navbar-title">评论 ({{ totalComments }})</text>
        <view class="navbar-actions">
          <view class="navbar-action" @click="showSortModal = true">
            <image src="/static/icons/sort.png" class="action-icon" />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 评论列表 -->
    <scroll-view 
      class="comment-list"
      scroll-y
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="comment-items">
        <view 
          v-for="comment in commentList"
          :key="comment.id"
          class="comment-item"
        >
          <!-- 主评论 -->
          <view class="comment-main">
            <image :src="comment.user.avatar" class="comment-avatar" />
            <view class="comment-content">
              <view class="comment-header">
                <text class="comment-user">{{ comment.user.nickname }}</text>
                <text class="comment-time">{{ formatTime(comment.createdAt) }}</text>
              </view>
              
              <text class="comment-text">{{ comment.content }}</text>
              
              <!-- 评论图片 -->
              <view v-if="comment.images && comment.images.length > 0" class="comment-images">
                <image 
                  v-for="(image, index) in comment.images"
                  :key="index"
                  :src="image"
                  class="comment-image"
                  mode="aspectFill"
                  @click="previewImages(comment.images, index)"
                />
              </view>
              
              <view class="comment-actions">
                <view class="action-item" @click="likeComment(comment.id)">
                  <image 
                    :src="comment.isLiked ? '/static/icons/thumb-up-filled.png' : '/static/icons/thumb-up.png'" 
                    class="action-icon"
                    :class="{ 'action-icon--active': comment.isLiked }"
                  />
                  <text class="action-text">{{ comment.likeCount || 0 }}</text>
                </view>
                
                <view class="action-item" @click="replyComment(comment)">
                  <image src="/static/icons/reply.png" class="action-icon" />
                  <text class="action-text">回复</text>
                </view>
                
                <view v-if="canDeleteComment(comment)" class="action-item" @click="deleteComment(comment.id)">
                  <image src="/static/icons/delete.png" class="action-icon" />
                  <text class="action-text">删除</text>
                </view>
              </view>
            </view>
          </view>
          
          <!-- 回复列表 -->
          <view v-if="comment.replies && comment.replies.length > 0" class="reply-list">
            <view 
              v-for="reply in comment.replies"
              :key="reply.id"
              class="reply-item"
            >
              <image :src="reply.user.avatar" class="reply-avatar" />
              <view class="reply-content">
                <view class="reply-header">
                  <text class="reply-user">{{ reply.user.nickname }}</text>
                  <text v-if="reply.replyTo" class="reply-to">
                    回复 {{ reply.replyTo.nickname }}
                  </text>
                  <text class="reply-time">{{ formatTime(reply.createdAt) }}</text>
                </view>
                
                <text class="reply-text">{{ reply.content }}</text>
                
                <view class="reply-actions">
                  <view class="action-item" @click="likeComment(reply.id)">
                    <image 
                      :src="reply.isLiked ? '/static/icons/thumb-up-filled.png' : '/static/icons/thumb-up.png'" 
                      class="action-icon"
                      :class="{ 'action-icon--active': reply.isLiked }"
                    />
                    <text class="action-text">{{ reply.likeCount || 0 }}</text>
                  </view>
                  
                  <view class="action-item" @click="replyComment(comment, reply)">
                    <image src="/static/icons/reply.png" class="action-icon" />
                    <text class="action-text">回复</text>
                  </view>
                  
                  <view v-if="canDeleteComment(reply)" class="action-item" @click="deleteComment(reply.id)">
                    <image src="/static/icons/delete.png" class="action-icon" />
                    <text class="action-text">删除</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <wish-loading v-if="loading" type="dots" text="加载中..." />
        <text v-else class="load-text" @click="loadMore">点击加载更多</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-else-if="commentList.length > 0">
        <text class="no-more-text">没有更多评论了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && commentList.length === 0">
        <image src="/static/icons/empty-comments.png" class="empty-icon" />
        <text class="empty-text">还没有评论</text>
        <text class="empty-desc">来发表第一条评论吧</text>
      </view>
    </scroll-view>
    
    <!-- 底部评论输入框 -->
    <view class="comment-input-bar safe-area-bottom">
      <view class="input-wrapper">
        <wish-input
          v-model="commentContent"
          :placeholder="commentPlaceholder"
          :maxlength="200"
          @focus="showCommentModal = true"
          class="comment-input"
        />
        <view class="input-actions">
          <view class="action-btn" @click="chooseImage">
            <image src="/static/icons/image.png" class="btn-icon" />
          </view>
          <wish-button
            type="primary"
            size="small"
            text="发送"
            :disabled="!canSubmit"
            @click="submitComment"
            class="send-button"
          />
        </view>
      </view>
    </view>
    
    <!-- 评论输入模态框 -->
    <wish-modal
      v-model:visible="showCommentModal"
      :title="modalTitle"
      position="bottom"
    >
      <view class="comment-modal">
        <wish-input
          v-model="commentContent"
          type="textarea"
          :placeholder="commentPlaceholder"
          :maxlength="200"
          show-word-limit
          :auto-height="true"
          class="modal-input"
        />
        
        <!-- 图片选择 -->
        <view class="image-section">
          <view class="selected-images" v-if="selectedImages.length > 0">
            <view 
              v-for="(image, index) in selectedImages"
              :key="index"
              class="image-item"
            >
              <image :src="image" class="selected-image" mode="aspectFill" />
              <view class="image-remove" @click="removeImage(index)">
                <image src="/static/icons/close-white.png" class="remove-icon" />
              </view>
            </view>
          </view>
          
          <view v-if="selectedImages.length < 3" class="add-image" @click="chooseImage">
            <image src="/static/icons/camera-large.png" class="add-icon" />
            <text class="add-text">添加图片</text>
          </view>
        </view>
      </view>
      
      <template #footer>
        <view class="comment-actions">
          <wish-button
            type="ghost"
            text="取消"
            @click="cancelComment"
            class="comment-button"
          />
          <wish-button
            type="primary"
            text="发表"
            :loading="submittingComment"
            :disabled="!canSubmit"
            @click="submitComment"
            class="comment-button"
          />
        </view>
      </template>
    </wish-modal>
    
    <!-- 排序选择模态框 -->
    <wish-modal
      v-model:visible="showSortModal"
      title="评论排序"
      position="bottom"
    >
      <view class="sort-options">
        <view 
          class="sort-option"
          :class="{ 'sort-option--active': commentSort === sort.value }"
          v-for="sort in sortOptions"
          :key="sort.value"
          @click="changeCommentSort(sort.value)"
        >
          <text class="sort-option-text">{{ sort.label }}</text>
          <image 
            v-if="commentSort === sort.value"
            src="/static/icons/check.png" 
            class="sort-check" 
          />
        </view>
      </view>
    </wish-modal>
  </view>
</template>

<script>
import { useUserStore, useSocialStore } from '@/store'
import { timeUtils, navigation, toast } from '@/utils'

export default {
  data() {
    return {
      targetId: '',
      targetType: '',
      commentList: [],
      commentContent: '',
      selectedImages: [],
      commentSort: 'latest',
      loading: false,
      refreshing: false,
      submittingComment: false,
      showCommentModal: false,
      showSortModal: false,
      replyingTo: null,
      replyingToUser: null,
      sortOptions: [
        { value: 'latest', label: '最新' },
        { value: 'hot', label: '最热' },
        { value: 'earliest', label: '最早' }
      ]
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    socialStore() {
      return useSocialStore()
    },
    
    hasMore() {
      return this.socialStore.commentPagination.hasMore
    },
    
    totalComments() {
      return this.commentList.length
    },
    
    canSubmit() {
      return this.commentContent.trim() || this.selectedImages.length > 0
    },
    
    commentPlaceholder() {
      if (this.replyingToUser) {
        return `回复 ${this.replyingToUser.nickname}...`
      }
      return '写下你的评论...'
    },
    
    modalTitle() {
      if (this.replyingToUser) {
        return `回复 ${this.replyingToUser.nickname}`
      }
      return '发表评论'
    }
  },
  
  onLoad(options) {
    this.targetId = options.targetId
    this.targetType = options.targetType || 'wish'
    this.initPage()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      await this.loadComments(true)
    },
    
    /**
     * 加载评论
     */
    async loadComments(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      try {
        const params = {
          targetId: this.targetId,
          targetType: this.targetType,
          sortBy: this.commentSort
        }
        
        const result = await this.socialStore.fetchComments(params, refresh)
        
        if (refresh) {
          this.commentList = result.list || []
        } else {
          this.commentList.push(...(result.list || []))
        }
      } catch (error) {
        console.error('加载评论失败:', error)
        toast.error('加载失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 加载更多
     */
    async loadMore() {
      if (!this.hasMore || this.loading) return
      await this.loadComments(false)
    },
    
    /**
     * 下拉刷新
     */
    async onRefresh() {
      this.refreshing = true
      try {
        await this.loadComments(true)
      } finally {
        this.refreshing = false
      }
    },
    
    /**
     * 提交评论
     */
    async submitComment() {
      if (!this.canSubmit) {
        return
      }
      
      this.submittingComment = true
      try {
        const commentData = {
          targetId: this.targetId,
          targetType: this.targetType,
          content: this.commentContent.trim(),
          images: this.selectedImages,
          replyTo: this.replyingTo?.id,
          replyToUser: this.replyingToUser?.id
        }
        
        const newComment = await this.socialStore.createComment(commentData)
        
        // 添加到评论列表
        if (this.replyingTo) {
          // 添加到回复列表
          const parentComment = this.commentList.find(c => c.id === this.replyingTo.id)
          if (parentComment) {
            parentComment.replies = parentComment.replies || []
            parentComment.replies.push(newComment)
          }
        } else {
          // 添加到主评论列表
          this.commentList.unshift(newComment)
        }
        
        // 重置输入
        this.resetInput()
        
        toast.success('评论发表成功')
      } catch (error) {
        console.error('发表评论失败:', error)
        toast.error('发表评论失败')
      } finally {
        this.submittingComment = false
      }
    },
    
    /**
     * 重置输入
     */
    resetInput() {
      this.commentContent = ''
      this.selectedImages = []
      this.replyingTo = null
      this.replyingToUser = null
      this.showCommentModal = false
    },
    
    /**
     * 取消评论
     */
    cancelComment() {
      this.resetInput()
    },
    
    /**
     * 回复评论
     */
    replyComment(comment, reply = null) {
      this.replyingTo = comment
      this.replyingToUser = reply ? reply.user : comment.user
      this.showCommentModal = true
    },
    
    /**
     * 点赞评论
     */
    async likeComment(commentId) {
      try {
        const comment = this.findComment(commentId)
        if (!comment) return
        
        if (comment.isLiked) {
          await this.socialStore.unlikeComment(commentId)
          comment.likeCount = Math.max(0, (comment.likeCount || 0) - 1)
        } else {
          await this.socialStore.likeComment(commentId)
          comment.likeCount = (comment.likeCount || 0) + 1
        }
        
        comment.isLiked = !comment.isLiked
      } catch (error) {
        console.error('点赞操作失败:', error)
        toast.error('操作失败')
      }
    },
    
    /**
     * 删除评论
     */
    deleteComment(commentId) {
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这条评论吗？',
        confirmText: '删除',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            try {
              await this.socialStore.deleteComment(commentId, this.targetId, this.targetType)
              
              // 从列表中移除
              this.removeCommentFromList(commentId)
              
              toast.success('删除成功')
            } catch (error) {
              console.error('删除评论失败:', error)
              toast.error('删除失败')
            }
          }
        }
      })
    },
    
    /**
     * 从列表中移除评论
     */
    removeCommentFromList(commentId) {
      // 从主评论列表中查找并移除
      const mainIndex = this.commentList.findIndex(c => c.id === commentId)
      if (mainIndex > -1) {
        this.commentList.splice(mainIndex, 1)
        return
      }
      
      // 从回复列表中查找并移除
      for (const comment of this.commentList) {
        if (comment.replies) {
          const replyIndex = comment.replies.findIndex(r => r.id === commentId)
          if (replyIndex > -1) {
            comment.replies.splice(replyIndex, 1)
            return
          }
        }
      }
    },
    
    /**
     * 查找评论
     */
    findComment(commentId) {
      for (const comment of this.commentList) {
        if (comment.id === commentId) {
          return comment
        }
        if (comment.replies) {
          const reply = comment.replies.find(r => r.id === commentId)
          if (reply) return reply
        }
      }
      return null
    },
    
    /**
     * 判断是否可以删除评论
     */
    canDeleteComment(comment) {
      return comment.user.id === this.userStore.userId
    },
    
    /**
     * 改变评论排序
     */
    async changeCommentSort(sort) {
      this.commentSort = sort
      this.showSortModal = false
      await this.loadComments(true)
    },
    
    /**
     * 选择图片
     */
    chooseImage() {
      const maxCount = 3 - this.selectedImages.length
      if (maxCount <= 0) {
        toast.error('最多只能添加3张图片')
        return
      }
      
      uni.chooseImage({
        count: maxCount,
        sizeType: ['compressed'],
        sourceType: ['album', 'camera'],
        success: (res) => {
          this.selectedImages.push(...res.tempFilePaths)
        }
      })
    },
    
    /**
     * 移除图片
     */
    removeImage(index) {
      this.selectedImages.splice(index, 1)
    },
    
    /**
     * 预览图片
     */
    previewImages(images, current) {
      uni.previewImage({
        urls: images,
        current: current
      })
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      return timeUtils.relativeTime(timestamp)
    },
    
    /**
     * 页面跳转
     */
    goBack() {
      navigation.navigateBack()
    }
  }
}
</script>

<style lang="scss" scoped>
.comments-page {
  height: 100vh;
  background-color: $wish-bg-primary;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $wish-spacing-md;
}

.navbar-back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
}

.navbar-title {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.navbar-action {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 评论列表 */
.comment-list {
  flex: 1;
  padding: $wish-spacing-sm;
}

.comment-items {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-md;
}

.comment-item {
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-md;
  box-shadow: $wish-shadow-sm;
}

/* 主评论 */
.comment-main {
  display: flex;
  align-items: flex-start;
}

.comment-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-xs;
}

.comment-user {
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-right: $wish-spacing-sm;
}

.comment-time {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.comment-text {
  font-size: $wish-font-md;
  color: $wish-text-primary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-sm;
}

.comment-images {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-xs;
  margin-bottom: $wish-spacing-sm;
}

.comment-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: $wish-radius-md;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: $wish-spacing-lg;
}

.action-item {
  display: flex;
  align-items: center;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: $wish-spacing-xs;
  transition: all 0.3s ease;

  &--active {
    filter: hue-rotate(200deg) saturate(1.5);
  }
}

.action-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

/* 回复列表 */
.reply-list {
  margin-top: $wish-spacing-md;
  padding-left: $wish-spacing-lg;
  border-left: 4rpx solid $wish-border-light;
}

.reply-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: $wish-spacing-md;

  &:last-child {
    margin-bottom: 0;
  }
}

.reply-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
}

.reply-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-xs;
}

.reply-user {
  font-size: $wish-font-sm;
  font-weight: 500;
  color: $wish-text-primary;
  margin-right: $wish-spacing-xs;
}

.reply-to {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
  margin-right: $wish-spacing-xs;
}

.reply-time {
  font-size: $wish-font-xs;
  color: $wish-text-secondary;
}

.reply-text {
  font-size: $wish-font-sm;
  color: $wish-text-primary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-xs;
}

.reply-actions {
  display: flex;
  align-items: center;
  gap: $wish-spacing-md;
}

/* 加载状态 */
.load-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.load-text {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
}

.no-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.no-more-text {
  font-size: $wish-font-sm;
  color: $wish-text-disabled;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $wish-spacing-xxl;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: $wish-spacing-lg;
  opacity: 0.6;
}

.empty-text {
  font-size: $wish-font-lg;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.empty-desc {
  font-size: $wish-font-md;
  color: $wish-text-disabled;
}

/* 底部评论输入框 */
.comment-input-bar {
  background-color: $wish-bg-secondary;
  border-top: 2rpx solid $wish-border-light;
  padding: $wish-spacing-md;
}

.input-wrapper {
  display: flex;
  align-items: center;
  gap: $wish-spacing-sm;
}

.comment-input {
  flex: 1;
  margin-bottom: 0;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: $wish-spacing-sm;
}

.action-btn {
  width: 64rpx;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: $wish-radius-md;
  background-color: $wish-bg-primary;
  transition: background-color 0.3s ease;

  &:active {
    background-color: $wish-border-light;
  }
}

.btn-icon {
  width: 24rpx;
  height: 24rpx;
}

.send-button {
  min-height: auto;
  padding: $wish-spacing-sm $wish-spacing-md;
}

/* 评论模态框 */
.comment-modal {
  padding: $wish-spacing-md 0;
}

.modal-input {
  margin-bottom: $wish-spacing-md;
}

.image-section {
  margin-bottom: $wish-spacing-md;
}

.selected-images {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-sm;
  margin-bottom: $wish-spacing-sm;
}

.image-item {
  position: relative;
  width: 160rpx;
  height: 160rpx;
}

.selected-image {
  width: 100%;
  height: 100%;
  border-radius: $wish-radius-md;
}

.image-remove {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.remove-icon {
  width: 16rpx;
  height: 16rpx;
}

.add-image {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed $wish-border-medium;
  border-radius: $wish-radius-md;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: border-color 0.3s ease;

  &:active {
    border-color: $wish-color-primary;
  }
}

.add-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: $wish-spacing-xs;
  opacity: 0.6;
}

.add-text {
  font-size: $wish-font-sm;
  color: $wish-text-disabled;
}

.comment-actions {
  display: flex;
  gap: $wish-spacing-md;
}

.comment-button {
  flex: 1;
}

/* 排序选择模态框 */
.sort-options {
  padding: $wish-spacing-md 0;
}

.sort-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
  transition: background-color 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &--active {
    background-color: rgba(232, 180, 160, 0.1);
  }

  &:active {
    background-color: $wish-bg-primary;
  }
}

.sort-option-text {
  font-size: $wish-font-md;
  color: $wish-text-primary;
}

.sort-check {
  width: 24rpx;
  height: 24rpx;
}
</style>
