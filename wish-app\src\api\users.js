/**
 * 用户相关API
 */

import request from './request'
import { API_CONFIG } from '@/config'

const { endpoints } = API_CONFIG

export const usersAPI = {
  // 获取用户资料
  getUserProfile(userId) {
    return request.get(`${endpoints.users.profile}/${userId}`)
  },

  // 更新用户资料
  updateProfile(data) {
    return request.put(endpoints.users.updateProfile, data)
  },

  // 修改密码
  changePassword(currentPassword, newPassword) {
    return request.put(endpoints.users.changePassword, {
      currentPassword,
      newPassword
    })
  },

  // 获取用户心愿列表
  getUserWishes(userId, params = {}) {
    const defaultParams = {
      page: 1,
      limit: 20,
      status: 'active'
    }
    return request.get(`${endpoints.users.wishes}/${userId}/wishes`, { ...defaultParams, ...params })
  },

  // 获取用户统计信息
  getUserStats(userId) {
    return request.get(`${endpoints.users.stats}/${userId}/stats`)
  },

  // 每日签到
  dailyCheckin() {
    return request.post(endpoints.users.checkin)
  },

  // 获取排行榜
  getLeaderboard(type, limit = 50) {
    return request.get(`${endpoints.users.leaderboard}/${type}`, { limit })
  }
}

export default usersAPI
