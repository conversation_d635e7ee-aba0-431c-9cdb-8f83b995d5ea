/**
 * 愿境应用工具函数库
 * 包含常用的工具函数、格式化函数、验证函数等
 */

/**
 * 存储相关工具
 */
export const storage = {
  // 设置存储
  set(key, value) {
    try {
      const data = JSON.stringify(value)
      uni.setStorageSync(key, data)
      return true
    } catch (error) {
      console.error('存储设置失败:', error)
      return false
    }
  },
  
  // 获取存储
  get(key, defaultValue = null) {
    try {
      const data = uni.getStorageSync(key)
      return data ? JSON.parse(data) : defaultValue
    } catch (error) {
      console.error('存储获取失败:', error)
      return defaultValue
    }
  },
  
  // 删除存储
  remove(key) {
    try {
      uni.removeStorageSync(key)
      return true
    } catch (error) {
      console.error('存储删除失败:', error)
      return false
    }
  },
  
  // 清空存储
  clear() {
    try {
      uni.clearStorageSync()
      return true
    } catch (error) {
      console.error('存储清空失败:', error)
      return false
    }
  }
}

/**
 * 时间格式化工具
 */
export const timeUtils = {
  // 格式化时间戳
  formatTime(timestamp, format = 'YYYY-MM-DD HH:mm:ss') {
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    const second = String(date.getSeconds()).padStart(2, '0')
    
    return format
      .replace('YYYY', year)
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hour)
      .replace('mm', minute)
      .replace('ss', second)
  },
  
  // 相对时间
  relativeTime(timestamp) {
    const now = Date.now()
    const diff = now - timestamp
    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour
    const week = 7 * day
    const month = 30 * day
    
    if (diff < minute) return '刚刚'
    if (diff < hour) return `${Math.floor(diff / minute)}分钟前`
    if (diff < day) return `${Math.floor(diff / hour)}小时前`
    if (diff < week) return `${Math.floor(diff / day)}天前`
    if (diff < month) return `${Math.floor(diff / week)}周前`
    return this.formatTime(timestamp, 'MM-DD')
  },
  
  // 检查是否是今天
  isToday(timestamp) {
    const today = new Date()
    const date = new Date(timestamp)
    return today.toDateString() === date.toDateString()
  }
}

/**
 * 验证工具
 */
export const validator = {
  // 手机号验证
  isPhone(phone) {
    const phoneReg = /^1[3-9]\d{9}$/
    return phoneReg.test(phone)
  },
  
  // 邮箱验证
  isEmail(email) {
    const emailReg = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailReg.test(email)
  },
  
  // 密码强度验证
  isStrongPassword(password) {
    // 至少8位，包含大小写字母和数字
    const strongReg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/
    return strongReg.test(password)
  },
  
  // 昵称验证
  isValidNickname(nickname) {
    // 2-20个字符，支持中文、英文、数字
    const nicknameReg = /^[\u4e00-\u9fa5a-zA-Z0-9]{2,20}$/
    return nicknameReg.test(nickname)
  },
  
  // 内容长度验证
  isValidLength(content, minLength = 1, maxLength = 500) {
    const length = content.trim().length
    return length >= minLength && length <= maxLength
  }
}

/**
 * 格式化工具
 */
export const formatter = {
  // 数字格式化
  formatNumber(num) {
    if (num < 1000) return num.toString()
    if (num < 10000) return (num / 1000).toFixed(1) + 'K'
    if (num < 100000000) return (num / 10000).toFixed(1) + 'W'
    return (num / 100000000).toFixed(1) + '亿'
  },
  
  // 文本截断
  truncateText(text, maxLength = 100, suffix = '...') {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + suffix
  },
  
  // 敏感信息脱敏
  maskSensitive(str, start = 3, end = 4) {
    if (str.length <= start + end) return str
    const masked = '*'.repeat(str.length - start - end)
    return str.substring(0, start) + masked + str.substring(str.length - end)
  }
}

/**
 * 设备信息工具
 */
export const deviceUtils = {
  // 获取系统信息
  getSystemInfo() {
    return new Promise((resolve) => {
      uni.getSystemInfo({
        success: resolve,
        fail: () => resolve({})
      })
    })
  },
  
  // 获取网络状态
  getNetworkType() {
    return new Promise((resolve) => {
      uni.getNetworkType({
        success: resolve,
        fail: () => resolve({ networkType: 'unknown' })
      })
    })
  },
  
  // 检查是否是iOS
  isIOS() {
    const system = uni.getSystemInfoSync()
    return system.platform === 'ios'
  },
  
  // 检查是否是Android
  isAndroid() {
    const system = uni.getSystemInfoSync()
    return system.platform === 'android'
  }
}

/**
 * 页面导航工具
 */
export const navigation = {
  // 页面跳转
  navigateTo(url, params = {}) {
    const query = Object.keys(params).length > 0 
      ? '?' + Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
      : ''
    
    uni.navigateTo({
      url: url + query,
      fail: (error) => {
        console.error('页面跳转失败:', error)
      }
    })
  },
  
  // 页面重定向
  redirectTo(url, params = {}) {
    const query = Object.keys(params).length > 0 
      ? '?' + Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
      : ''
    
    uni.redirectTo({
      url: url + query,
      fail: (error) => {
        console.error('页面重定向失败:', error)
      }
    })
  },
  
  // 返回上一页
  navigateBack(delta = 1) {
    uni.navigateBack({
      delta,
      fail: (error) => {
        console.error('页面返回失败:', error)
      }
    })
  },
  
  // 切换到tabBar页面
  switchTab(url) {
    uni.switchTab({
      url,
      fail: (error) => {
        console.error('切换tabBar失败:', error)
      }
    })
  }
}

/**
 * 消息提示工具
 */
export const toast = {
  // 成功提示
  success(title, duration = 2000) {
    uni.showToast({
      title,
      icon: 'success',
      duration
    })
  },
  
  // 错误提示
  error(title, duration = 2000) {
    uni.showToast({
      title,
      icon: 'error',
      duration
    })
  },
  
  // 普通提示
  info(title, duration = 2000) {
    uni.showToast({
      title,
      icon: 'none',
      duration
    })
  },
  
  // 加载提示
  loading(title = '加载中...') {
    uni.showLoading({
      title,
      mask: true
    })
  },
  
  // 隐藏加载
  hideLoading() {
    uni.hideLoading()
  }
}

/**
 * 防抖函数
 */
export function debounce(func, wait = 300) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

/**
 * 节流函数
 */
export function throttle(func, limit = 300) {
  let inThrottle
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

/**
 * 深拷贝
 */
export function deepClone(obj) {
  if (obj === null || typeof obj !== 'object') return obj
  if (obj instanceof Date) return new Date(obj.getTime())
  if (obj instanceof Array) return obj.map(item => deepClone(item))
  if (typeof obj === 'object') {
    const clonedObj = {}
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        clonedObj[key] = deepClone(obj[key])
      }
    }
    return clonedObj
  }
}
