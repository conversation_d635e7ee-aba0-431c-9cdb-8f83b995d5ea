/**
 * 系统功能状态管理
 * 管理系统配置、通知、标签等系统级功能
 */

import { defineStore } from 'pinia'
import { systemAPI } from '@/api'
import { eventAPI } from '@/api/events'

export const useSystemStore = defineStore('system', {
  state: () => ({
    // 应用配置
    appConfig: {
      version: '1.0.0',
      maintenance: false,
      features: {}
    },
    // 标签列表
    tags: [],
    // 系统通知
    notifications: [],
    // 活动列表
    events: [],
    // 当前活动
    currentEvent: null,
    // 签到状态
    checkInStatus: {
      isCheckedIn: false,
      streak: 0,
      lastCheckInDate: null,
      nextReward: 10
    },
    // 加载状态
    loading: {
      config: false,
      tags: false,
      notifications: false,
      events: false,
      checkIn: false
    }
  }),

  getters: {
    // 获取指定类型的通知
    getNotificationsByType: (state) => (type) => {
      return state.notifications.filter(notification => notification.type === type)
    },
    
    // 未读通知数量
    unreadNotificationCount: (state) => {
      return state.notifications.filter(notification => !notification.isRead).length
    },
    
    // 活跃的活动
    activeEvents: (state) => {
      return state.events.filter(event => event.status === 'active')
    },
    
    // 已结束的活动
    endedEvents: (state) => {
      return state.events.filter(event => event.status === 'ended')
    },
    
    // 是否可以签到
    canCheckIn: (state) => {
      return !state.checkInStatus.isCheckedIn
    },
    
    // 连续签到奖励
    checkInReward: (state) => {
      const { streak } = state.checkInStatus
      if (streak >= 7) return 50  // 连续7天奖励
      if (streak >= 3) return 20  // 连续3天奖励
      return 10  // 基础奖励
    }
  },

  actions: {
    /**
     * 获取应用配置
     */
    async fetchAppConfig() {
      this.loading.config = true
      try {
        const config = await systemAPI.getAppConfig()
        this.appConfig = { ...this.appConfig, ...config }
        return config
      } catch (error) {
        throw error
      } finally {
        this.loading.config = false
      }
    },

    /**
     * 获取标签列表
     */
    async fetchTags() {
      this.loading.tags = true
      try {
        const tags = await systemAPI.getTags()
        this.tags = tags
        return tags
      } catch (error) {
        throw error
      } finally {
        this.loading.tags = false
      }
    },

    /**
     * 获取系统通知
     */
    async fetchNotifications(params = {}) {
      this.loading.notifications = true
      try {
        const result = await systemAPI.getNotifications(params)
        this.notifications = result.list
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.notifications = false
      }
    },

    /**
     * 获取活动列表
     */
    async fetchEvents(params = {}) {
      this.loading.events = true
      try {
        const result = await eventAPI.getEvents(params)
        this.events = result.list
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.events = false
      }
    },

    /**
     * 获取活动详情
     */
    async fetchEventDetail(eventId) {
      try {
        const event = await eventAPI.getEventDetail(eventId)
        this.currentEvent = event
        return event
      } catch (error) {
        throw error
      }
    },

    /**
     * 参与活动
     */
    async joinEvent(eventId, data = {}) {
      try {
        const result = await eventAPI.joinEvent(eventId, data)
        
        // 更新活动状态
        const event = this.events.find(e => e.id === eventId)
        if (event) {
          event.isJoined = true
          event.participantCount = (event.participantCount || 0) + 1
        }
        
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 每日签到
     */
    async dailyCheckIn() {
      this.loading.checkIn = true
      try {
        const result = await systemAPI.dailyCheckIn()
        
        // 更新签到状态
        this.checkInStatus = {
          isCheckedIn: true,
          streak: result.streak,
          lastCheckInDate: new Date().toDateString(),
          nextReward: this.checkInReward
        }
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.checkIn = false
      }
    },

    /**
     * 获取签到状态
     */
    async fetchCheckInStatus() {
      try {
        const status = await systemAPI.getCheckInStatus()
        this.checkInStatus = { ...this.checkInStatus, ...status }
        return status
      } catch (error) {
        throw error
      }
    },

    /**
     * 举报内容
     */
    async reportContent(reportData) {
      try {
        const result = await systemAPI.reportContent(reportData)
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 提交意见反馈
     */
    async submitFeedback(feedbackData) {
      try {
        const result = await systemAPI.submitFeedback(feedbackData)
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 标记通知已读
     */
    markNotificationRead(notificationId) {
      const notification = this.notifications.find(n => n.id === notificationId)
      if (notification) {
        notification.isRead = true
      }
    },

    /**
     * 标记所有通知已读
     */
    markAllNotificationsRead() {
      this.notifications.forEach(notification => {
        notification.isRead = true
      })
    },

    /**
     * 添加通知
     */
    addNotification(notification) {
      this.notifications.unshift({
        id: Date.now(),
        isRead: false,
        createdAt: new Date().toISOString(),
        ...notification
      })
    },

    /**
     * 删除通知
     */
    removeNotification(notificationId) {
      this.notifications = this.notifications.filter(
        notification => notification.id !== notificationId
      )
    },

    /**
     * 清空通知
     */
    clearNotifications() {
      this.notifications = []
    },

    /**
     * 检查应用更新
     */
    async checkAppUpdate() {
      try {
        const config = await this.fetchAppConfig()
        
        // 检查是否有新版本
        const currentVersion = this.appConfig.version
        const latestVersion = config.latestVersion
        
        if (latestVersion && latestVersion !== currentVersion) {
          return {
            hasUpdate: true,
            currentVersion,
            latestVersion,
            updateInfo: config.updateInfo
          }
        }
        
        return { hasUpdate: false }
      } catch (error) {
        console.error('检查更新失败:', error)
        return { hasUpdate: false }
      }
    },

    /**
     * 初始化系统数据
     */
    async initSystemData() {
      try {
        await Promise.all([
          this.fetchAppConfig(),
          this.fetchTags(),
          this.fetchCheckInStatus()
        ])
      } catch (error) {
        console.error('初始化系统数据失败:', error)
      }
    }
  }
})
