<!--
  愿境输入框组件
  支持多种类型和状态的输入框组件
-->
<template>
  <view class="wish-input-wrapper">
    <!-- 标签 -->
    <view v-if="label" class="wish-input__label">
      {{ label }}
      <text v-if="required" class="wish-input__required">*</text>
    </view>
    
    <!-- 输入框容器 -->
    <view 
      class="wish-input"
      :class="[
        `wish-input--${type}`,
        {
          'wish-input--focused': focused,
          'wish-input--disabled': disabled,
          'wish-input--error': error
        }
      ]"
    >
      <!-- 前缀图标 -->
      <view v-if="prefixIcon" class="wish-input__prefix">
        <image :src="prefixIcon" class="wish-input__icon" />
      </view>
      
      <!-- 输入框 -->
      <input
        v-if="type !== 'textarea'"
        class="wish-input__field"
        :type="inputType"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
        @confirm="handleConfirm"
      />
      
      <!-- 多行文本框 -->
      <textarea
        v-else
        class="wish-input__field wish-input__textarea"
        :value="modelValue"
        :placeholder="placeholder"
        :disabled="disabled"
        :maxlength="maxlength"
        :auto-height="autoHeight"
        @input="handleInput"
        @focus="handleFocus"
        @blur="handleBlur"
      />
      
      <!-- 后缀图标 -->
      <view v-if="suffixIcon || showClear" class="wish-input__suffix">
        <!-- 清除按钮 -->
        <view 
          v-if="showClear && modelValue && !disabled"
          class="wish-input__clear"
          @click="handleClear"
        >
          <image src="/static/icons/clear.png" class="wish-input__icon" />
        </view>
        
        <!-- 后缀图标 -->
        <view v-if="suffixIcon" class="wish-input__suffix-icon">
          <image :src="suffixIcon" class="wish-input__icon" />
        </view>
      </view>
    </view>
    
    <!-- 字数统计 -->
    <view v-if="showWordLimit && maxlength" class="wish-input__count">
      {{ (modelValue || '').length }}/{{ maxlength }}
    </view>
    
    <!-- 错误提示 -->
    <view v-if="errorMessage" class="wish-input__error">
      {{ errorMessage }}
    </view>
    
    <!-- 帮助文本 -->
    <view v-if="helpText" class="wish-input__help">
      {{ helpText }}
    </view>
  </view>
</template>

<script>
export default {
  name: 'WishInput',
  props: {
    // v-model绑定值
    modelValue: {
      type: [String, Number],
      default: ''
    },
    // 输入框类型
    type: {
      type: String,
      default: 'text',
      validator: (value) => ['text', 'number', 'password', 'textarea'].includes(value)
    },
    // 标签
    label: {
      type: String,
      default: ''
    },
    // 占位符
    placeholder: {
      type: String,
      default: ''
    },
    // 是否必填
    required: {
      type: Boolean,
      default: false
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 最大长度
    maxlength: {
      type: Number,
      default: null
    },
    // 前缀图标
    prefixIcon: {
      type: String,
      default: ''
    },
    // 后缀图标
    suffixIcon: {
      type: String,
      default: ''
    },
    // 是否显示清除按钮
    showClear: {
      type: Boolean,
      default: false
    },
    // 是否显示字数限制
    showWordLimit: {
      type: Boolean,
      default: false
    },
    // 是否自动高度（textarea）
    autoHeight: {
      type: Boolean,
      default: false
    },
    // 错误状态
    error: {
      type: Boolean,
      default: false
    },
    // 错误信息
    errorMessage: {
      type: String,
      default: ''
    },
    // 帮助文本
    helpText: {
      type: String,
      default: ''
    }
  },
  
  data() {
    return {
      focused: false
    }
  },
  
  computed: {
    inputType() {
      const typeMap = {
        text: 'text',
        number: 'number',
        password: 'password'
      }
      return typeMap[this.type] || 'text'
    }
  },
  
  methods: {
    handleInput(event) {
      const value = event.detail.value
      this.$emit('update:modelValue', value)
      this.$emit('input', value)
    },
    
    handleFocus(event) {
      this.focused = true
      this.$emit('focus', event)
    },
    
    handleBlur(event) {
      this.focused = false
      this.$emit('blur', event)
    },
    
    handleConfirm(event) {
      this.$emit('confirm', event)
    },
    
    handleClear() {
      this.$emit('update:modelValue', '')
      this.$emit('clear')
    }
  }
}
</script>

<style lang="scss" scoped>
.wish-input-wrapper {
  margin-bottom: $wish-spacing-md;
}

.wish-input__label {
  font-size: $wish-font-md;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
  font-weight: 500;
}

.wish-input__required {
  color: $wish-color-error;
  margin-left: 4rpx;
}

.wish-input {
  display: flex;
  align-items: center;
  background-color: $wish-bg-secondary;
  border: 2rpx solid $wish-border-light;
  border-radius: $wish-radius-md;
  padding: $wish-spacing-sm $wish-spacing-md;
  transition: all 0.3s ease;
  
  &--focused {
    border-color: $wish-color-primary;
    box-shadow: 0 0 0 4rpx rgba(232, 180, 160, 0.1);
  }
  
  &--disabled {
    background-color: $wish-bg-primary;
    color: $wish-text-disabled;
    cursor: not-allowed;
  }
  
  &--error {
    border-color: $wish-color-error;
    
    &.wish-input--focused {
      box-shadow: 0 0 0 4rpx rgba(232, 164, 164, 0.1);
    }
  }
}

.wish-input__prefix,
.wish-input__suffix {
  display: flex;
  align-items: center;
}

.wish-input__prefix {
  margin-right: $wish-spacing-xs;
}

.wish-input__suffix {
  margin-left: $wish-spacing-xs;
}

.wish-input__icon {
  width: 32rpx;
  height: 32rpx;
}

.wish-input__clear {
  cursor: pointer;
  margin-right: $wish-spacing-xs;
}

.wish-input__field {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: $wish-font-md;
  color: $wish-text-primary;
  
  &::placeholder {
    color: $wish-text-disabled;
  }
}

.wish-input__textarea {
  min-height: 120rpx;
  resize: none;
}

.wish-input__count {
  text-align: right;
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin-top: $wish-spacing-xs;
}

.wish-input__error {
  font-size: $wish-font-sm;
  color: $wish-color-error;
  margin-top: $wish-spacing-xs;
}

.wish-input__help {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin-top: $wish-spacing-xs;
}
</style>
