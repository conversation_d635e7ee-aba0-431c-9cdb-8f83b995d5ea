/**
 * 用户模型
 * 定义用户数据结构和相关方法
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const config = require('../config');

const userSchema = new mongoose.Schema({
  // 基本信息
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    minlength: 3,
    maxlength: 20,
    match: /^[a-zA-Z0-9_]+$/
  },
  
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  
  phone: {
    type: String,
    sparse: true,
    match: /^1[3-9]\d{9}$/
  },
  
  password: {
    type: String,
    required: true,
    minlength: 8
  },
  
  // 个人资料
  profile: {
    nickname: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50
    },
    avatar: {
      type: String,
      default: ''
    },
    bio: {
      type: String,
      maxlength: 200,
      default: ''
    },
    gender: {
      type: String,
      enum: ['male', 'female', 'other', 'private'],
      default: 'private'
    },
    birthday: {
      type: Date
    },
    location: {
      province: String,
      city: String,
      district: String
    }
  },
  
  // 账户状态
  status: {
    type: String,
    enum: ['active', 'inactive', 'suspended', 'banned'],
    default: 'active'
  },
  
  // 验证状态
  verification: {
    email: {
      verified: {
        type: Boolean,
        default: false
      },
      token: String,
      expiresAt: Date
    },
    phone: {
      verified: {
        type: Boolean,
        default: false
      },
      code: String,
      expiresAt: Date
    }
  },
  
  // 安全设置
  security: {
    loginAttempts: {
      type: Number,
      default: 0
    },
    lockUntil: Date,
    lastLogin: Date,
    lastLoginIP: String,
    passwordChangedAt: {
      type: Date,
      default: Date.now
    },
    twoFactorEnabled: {
      type: Boolean,
      default: false
    },
    twoFactorSecret: String
  },
  
  // 用户等级和积分
  level: {
    current: {
      type: Number,
      default: 1
    },
    experience: {
      type: Number,
      default: 0
    },
    nextLevelExp: {
      type: Number,
      default: 100
    }
  },
  
  // 心愿力和功德值
  points: {
    wishPower: {
      type: Number,
      default: 0
    },
    merit: {
      type: Number,
      default: 0
    },
    totalEarned: {
      wishPower: {
        type: Number,
        default: 0
      },
      merit: {
        type: Number,
        default: 0
      }
    }
  },
  
  // 统计数据
  stats: {
    wishesCreated: {
      type: Number,
      default: 0
    },
    wishesBlessed: {
      type: Number,
      default: 0
    },
    blessingsReceived: {
      type: Number,
      default: 0
    },
    commentsPosted: {
      type: Number,
      default: 0
    },
    likesReceived: {
      type: Number,
      default: 0
    },
    consecutiveDays: {
      type: Number,
      default: 0
    },
    lastActiveDate: Date
  },
  
  // 用户偏好设置
  preferences: {
    language: {
      type: String,
      default: 'zh-CN'
    },
    timezone: {
      type: String,
      default: 'Asia/Shanghai'
    },
    notifications: {
      email: {
        type: Boolean,
        default: true
      },
      push: {
        type: Boolean,
        default: true
      },
      sms: {
        type: Boolean,
        default: false
      }
    },
    privacy: {
      profileVisible: {
        type: Boolean,
        default: true
      },
      wishesVisible: {
        type: Boolean,
        default: true
      },
      allowMessages: {
        type: Boolean,
        default: true
      }
    }
  },
  
  // 角色和权限
  roles: [{
    type: String,
    enum: ['user', 'guardian', 'moderator', 'admin'],
    default: 'user'
  }],
  
  // 第三方账户绑定
  thirdParty: {
    wechat: {
      openid: String,
      unionid: String,
      bound: {
        type: Boolean,
        default: false
      }
    },
    qq: {
      openid: String,
      bound: {
        type: Boolean,
        default: false
      }
    }
  },
  
  // 设备信息
  devices: [{
    deviceId: String,
    platform: String,
    version: String,
    lastUsed: Date,
    pushToken: String
  }]
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.password;
      delete ret.security;
      delete ret.verification;
      delete ret.thirdParty;
      return ret;
    }
  }
});

// 索引
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ phone: 1 });
userSchema.index({ 'points.merit': -1 });
userSchema.index({ 'points.wishPower': -1 });
userSchema.index({ createdAt: -1 });

// 虚拟字段
userSchema.virtual('isLocked').get(function() {
  return !!(this.security.lockUntil && this.security.lockUntil > Date.now());
});

userSchema.virtual('isEmailVerified').get(function() {
  return this.verification.email.verified;
});

userSchema.virtual('isPhoneVerified').get(function() {
  return this.verification.phone.verified;
});

// 中间件
userSchema.pre('save', async function(next) {
  // 密码加密
  if (this.isModified('password')) {
    this.password = await bcrypt.hash(this.password, config.bcrypt.saltRounds);
    this.security.passwordChangedAt = new Date();
  }
  
  // 设置昵称默认值
  if (this.isNew && !this.profile.nickname) {
    this.profile.nickname = this.username;
  }
  
  next();
});

// 实例方法
userSchema.methods.comparePassword = async function(candidatePassword) {
  return bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.generateAuthToken = function() {
  const payload = {
    userId: this._id,
    username: this.username,
    roles: this.roles
  };
  
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.expiresIn,
    issuer: config.jwt.issuer,
    audience: config.jwt.audience
  });
};

userSchema.methods.generateRefreshToken = function() {
  const payload = {
    userId: this._id,
    type: 'refresh'
  };
  
  return jwt.sign(payload, config.jwt.secret, {
    expiresIn: config.jwt.refreshExpiresIn,
    issuer: config.jwt.issuer,
    audience: config.jwt.audience
  });
};

userSchema.methods.incrementLoginAttempts = function() {
  // 如果之前有锁定且已过期，重置计数
  if (this.security.lockUntil && this.security.lockUntil < Date.now()) {
    return this.updateOne({
      $unset: { 'security.lockUntil': 1 },
      $set: { 'security.loginAttempts': 1 }
    });
  }
  
  const updates = { $inc: { 'security.loginAttempts': 1 } };
  
  // 如果达到最大尝试次数且未锁定，设置锁定时间
  if (this.security.loginAttempts + 1 >= config.security.accountLock.maxAttempts && !this.isLocked) {
    updates.$set = { 'security.lockUntil': Date.now() + config.security.accountLock.lockTime };
  }
  
  return this.updateOne(updates);
};

userSchema.methods.resetLoginAttempts = function() {
  return this.updateOne({
    $unset: {
      'security.loginAttempts': 1,
      'security.lockUntil': 1
    }
  });
};

userSchema.methods.updateLastLogin = function(ip) {
  return this.updateOne({
    $set: {
      'security.lastLogin': new Date(),
      'security.lastLoginIP': ip
    }
  });
};

userSchema.methods.addExperience = function(exp) {
  this.level.experience += exp;
  
  // 检查是否升级
  while (this.level.experience >= this.level.nextLevelExp) {
    this.level.experience -= this.level.nextLevelExp;
    this.level.current += 1;
    this.level.nextLevelExp = this.level.current * 100; // 简单的升级公式
  }
  
  return this.save();
};

userSchema.methods.addPoints = function(type, amount) {
  this.points[type] += amount;
  this.points.totalEarned[type] += amount;
  return this.save();
};

// 静态方法
userSchema.statics.findByCredentials = async function(identifier, password) {
  const query = {
    $or: [
      { email: identifier },
      { username: identifier },
      { phone: identifier }
    ]
  };
  
  const user = await this.findOne(query);
  
  if (!user) {
    throw new Error('用户不存在');
  }
  
  if (user.isLocked) {
    throw new Error('账户已被锁定，请稍后再试');
  }
  
  if (user.status !== 'active') {
    throw new Error('账户已被禁用');
  }
  
  const isMatch = await user.comparePassword(password);
  
  if (!isMatch) {
    await user.incrementLoginAttempts();
    throw new Error('密码错误');
  }
  
  // 登录成功，重置登录尝试次数
  if (user.security.loginAttempts > 0) {
    await user.resetLoginAttempts();
  }
  
  return user;
};

userSchema.statics.getLeaderboard = async function(type = 'merit', limit = 100) {
  const sortField = type === 'merit' ? 'points.merit' : 'points.wishPower';
  
  return this.find({ status: 'active' })
    .sort({ [sortField]: -1 })
    .limit(limit)
    .select('username profile.nickname profile.avatar points level')
    .lean();
};

module.exports = mongoose.model('User', userSchema);
