/**
 * 用户模型
 * 定义用户数据结构和相关方法
 */

const { DataTypes, Model } = require('sequelize');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const config = require('../config');
const { getSequelize } = require('../database/connection');

class User extends Model {
  // 实例方法
  async comparePassword(candidatePassword) {
    return bcrypt.compare(candidatePassword, this.password);
  }

  generateAuthToken() {
    const payload = {
      userId: this.id,
      username: this.username,
      roles: this.roles || ['user']
    };
    
    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.expiresIn,
      issuer: config.jwt.issuer,
      audience: config.jwt.audience
    });
  }

  generateRefreshToken() {
    const payload = {
      userId: this.id,
      type: 'refresh'
    };
    
    return jwt.sign(payload, config.jwt.secret, {
      expiresIn: config.jwt.refreshExpiresIn,
      issuer: config.jwt.issuer,
      audience: config.jwt.audience
    });
  }

  async incrementLoginAttempts() {
    const now = new Date();
    
    // 如果之前有锁定且已过期，重置计数
    if (this.lock_until && this.lock_until < now) {
      await this.update({
        lock_until: null,
        login_attempts: 1
      });
      return;
    }
    
    const updates = { login_attempts: this.login_attempts + 1 };
    
    // 如果达到最大尝试次数且未锁定，设置锁定时间
    if (this.login_attempts + 1 >= config.security.accountLock.maxAttempts && !this.isLocked) {
      updates.lock_until = new Date(now.getTime() + config.security.accountLock.lockTime);
    }
    
    await this.update(updates);
  }

  async resetLoginAttempts() {
    await this.update({
      login_attempts: 0,
      lock_until: null
    });
  }

  async updateLastLogin(ip) {
    await this.update({
      last_login: new Date(),
      last_login_ip: ip
    });
  }

  async addExperience(exp) {
    let newExp = this.experience + exp;
    let newLevel = this.level;
    let nextLevelExp = this.next_level_exp;
    
    // 检查是否升级
    while (newExp >= nextLevelExp) {
      newExp -= nextLevelExp;
      newLevel += 1;
      nextLevelExp = newLevel * 100; // 简单的升级公式
    }
    
    await this.update({
      experience: newExp,
      level: newLevel,
      next_level_exp: nextLevelExp
    });
  }

  async addPoints(type, amount) {
    const fieldMap = {
      wishPower: 'wish_power_points',
      merit: 'merit_points'
    };
    
    const totalFieldMap = {
      wishPower: 'total_wish_power_earned',
      merit: 'total_merit_earned'
    };
    
    const updates = {};
    updates[fieldMap[type]] = this[fieldMap[type]] + amount;
    updates[totalFieldMap[type]] = this[totalFieldMap[type]] + amount;
    
    await this.update(updates);
  }

  // 虚拟属性
  get isLocked() {
    return !!(this.lock_until && this.lock_until > new Date());
  }

  get isEmailVerified() {
    return this.email_verified;
  }

  get isPhoneVerified() {
    return this.phone_verified;
  }

  // 静态方法
  static async findByCredentials(identifier, password) {
    const { Op } = require('sequelize');
    const whereClause = {
      [Op.or]: [
        { email: identifier },
        { username: identifier }
      ]
    };
    
    if (/^1[3-9]\d{9}$/.test(identifier)) {
      whereClause[Op.or].push({ phone: identifier });
    }
    
    const user = await this.findOne({ where: whereClause });
    
    if (!user) {
      throw new Error('用户不存在');
    }
    
    if (user.isLocked) {
      throw new Error('账户已被锁定，请稍后再试');
    }
    
    if (user.status !== 'active') {
      throw new Error('账户已被禁用');
    }
    
    const isMatch = await user.comparePassword(password);
    
    if (!isMatch) {
      await user.incrementLoginAttempts();
      throw new Error('密码错误');
    }
    
    // 登录成功，重置登录尝试次数
    if (user.login_attempts > 0) {
      await user.resetLoginAttempts();
    }
    
    return user;
  }

  static async getLeaderboard(type = 'merit', limit = 100) {
    const orderField = type === 'merit' ? 'merit_points' : 'wish_power_points';
    
    return this.findAll({
      where: { status: 'active' },
      order: [[orderField, 'DESC']],
      limit,
      attributes: [
        'id', 'username', 'nickname', 'avatar', 
        'merit_points', 'wish_power_points', 'level'
      ]
    });
  }
}

// 初始化用户模型
function initUserModel() {
  const sequelize = getSequelize();
  
  User.init({
    // 基本信息
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    username: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true,
      validate: {
        len: [3, 20],
        is: /^[a-zA-Z0-9_]+$/
      }
    },
    email: {
      type: DataTypes.STRING(100),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true
      }
    },
    phone: {
      type: DataTypes.STRING(11),
      allowNull: true,
      unique: true,
      validate: {
        is: /^1[3-9]\d{9}$/
      }
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: false,
      validate: {
        len: [8, 255]
      }
    },
    
    // 个人资料
    nickname: {
      type: DataTypes.STRING(50),
      allowNull: false,
      defaultValue: ''
    },
    avatar: {
      type: DataTypes.STRING(255),
      defaultValue: ''
    },
    bio: {
      type: DataTypes.TEXT,
      defaultValue: ''
    },
    gender: {
      type: DataTypes.ENUM('male', 'female', 'other', 'private'),
      defaultValue: 'private'
    },
    birthday: {
      type: DataTypes.DATE,
      allowNull: true
    },
    province: {
      type: DataTypes.STRING(50),
      defaultValue: ''
    },
    city: {
      type: DataTypes.STRING(50),
      defaultValue: ''
    },
    district: {
      type: DataTypes.STRING(50),
      defaultValue: ''
    },
    
    // 账户状态
    status: {
      type: DataTypes.ENUM('active', 'inactive', 'suspended', 'banned'),
      defaultValue: 'active'
    },
    
    // 验证状态
    email_verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    email_verification_token: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    email_verification_expires: {
      type: DataTypes.DATE,
      allowNull: true
    },
    phone_verified: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    phone_verification_code: {
      type: DataTypes.STRING(10),
      allowNull: true
    },
    phone_verification_expires: {
      type: DataTypes.DATE,
      allowNull: true
    },
    
    // 安全设置
    login_attempts: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    lock_until: {
      type: DataTypes.DATE,
      allowNull: true
    },
    last_login: {
      type: DataTypes.DATE,
      allowNull: true
    },
    last_login_ip: {
      type: DataTypes.STRING(45),
      allowNull: true
    },
    password_changed_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    two_factor_enabled: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    two_factor_secret: {
      type: DataTypes.STRING(255),
      allowNull: true
    },

    // 用户等级和积分
    level: {
      type: DataTypes.INTEGER,
      defaultValue: 1
    },
    experience: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    next_level_exp: {
      type: DataTypes.INTEGER,
      defaultValue: 100
    },

    // 心愿力和功德值
    wish_power_points: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    merit_points: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    total_wish_power_earned: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    total_merit_earned: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },

    // 统计数据
    wishes_created: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    wishes_blessed: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    blessings_received: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    comments_posted: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    likes_received: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    consecutive_days: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    last_active_date: {
      type: DataTypes.DATE,
      allowNull: true
    },

    // 用户偏好设置
    language: {
      type: DataTypes.STRING(10),
      defaultValue: 'zh-CN'
    },
    timezone: {
      type: DataTypes.STRING(50),
      defaultValue: 'Asia/Shanghai'
    },
    email_notifications: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    push_notifications: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    sms_notifications: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    profile_visible: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    wishes_visible: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    allow_messages: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },

    // 角色和权限
    roles: {
      type: DataTypes.JSON,
      defaultValue: ['user']
    },

    // 第三方账户绑定
    wechat_openid: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    wechat_unionid: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    wechat_bound: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    },
    qq_openid: {
      type: DataTypes.STRING(100),
      allowNull: true
    },
    qq_bound: {
      type: DataTypes.BOOLEAN,
      defaultValue: false
    }
  }, {
    sequelize,
    modelName: 'User',
    tableName: 'users',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['email'] },
      { fields: ['username'] },
      { fields: ['phone'] },
      { fields: ['merit_points'] },
      { fields: ['wish_power_points'] },
      { fields: ['created_at'] }
    ],
    hooks: {
      beforeSave: async (user) => {
        // 密码加密
        if (user.changed('password')) {
          user.password = await bcrypt.hash(user.password, config.bcrypt.saltRounds);
          user.password_changed_at = new Date();
        }

        // 设置昵称默认值
        if (user.isNewRecord && !user.nickname) {
          user.nickname = user.username;
        }
      }
    }
  });
  
  return User;
}

module.exports = { User, initUserModel };
