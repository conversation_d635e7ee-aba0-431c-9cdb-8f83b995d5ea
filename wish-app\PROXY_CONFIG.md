# 前端代理配置说明

## 概述

为了解决开发环境中的跨域问题，我们配置了Vite代理，将前端的API请求代理到后端服务器。

## 配置详情

### 1. Vite代理配置 (`vite.config.js`)

```javascript
server: {
  proxy: {
    // 代理所有 /api 请求到后端服务器
    '/api': {
      target: 'http://localhost:3000',
      changeOrigin: true,
      secure: false,
      ws: true
    },
    // 代理健康检查接口
    '/health': {
      target: 'http://localhost:3000',
      changeOrigin: true,
      secure: false
    },
    // 代理上传文件接口
    '/uploads': {
      target: 'http://localhost:3000',
      changeOrigin: true,
      secure: false
    },
    // 代理Socket.IO连接
    '/socket.io': {
      target: 'http://localhost:3000',
      changeOrigin: true,
      secure: false,
      ws: true
    }
  }
}
```

### 2. 环境配置 (`src/config/index.js`)

```javascript
const ENV = {
  development: {
    baseURL: '/api', // 使用相对路径，通过代理转发
    wsURL: '/socket.io',
    debug: true,
    useProxy: true
  },
  production: {
    baseURL: 'https://api.wishrealm.com/api',
    wsURL: 'wss://api.wishrealm.com',
    debug: false,
    useProxy: false
  }
}
```

## 代理工作原理

1. **前端请求**: `http://localhost:5173/api/users/profile`
2. **Vite代理**: 将请求转发到 `http://localhost:3000/api/users/profile`
3. **后端处理**: 后端服务器处理请求并返回响应
4. **代理返回**: Vite将后端响应返回给前端

## 优势

### ✅ 解决跨域问题
- 前端和后端在同一域名下，避免CORS问题
- 无需配置复杂的CORS规则

### ✅ 开发体验更好
- 统一的API调用方式
- 更接近生产环境的配置
- 支持WebSocket代理

### ✅ 配置灵活
- 可以代理不同的路径到不同的后端服务
- 支持路径重写和请求修改
- 可以添加请求/响应日志

## 测试工具

### 1. 代理测试页面
访问: `http://localhost:5173/#/pages/test/proxy-test`

### 2. API测试页面
访问: `http://localhost:5173/#/pages/test/api-test`

### 3. 编程方式测试
```javascript
import { quickProxyTest } from '@/utils/proxy-test'

// 运行代理测试
quickProxyTest().then(report => {
  console.log('代理测试结果:', report)
})
```

## 常见问题

### Q: 代理不工作怎么办？
A: 
1. 检查后端服务是否启动 (`http://localhost:3000/health`)
2. 检查vite.config.js中的代理配置
3. 重启前端开发服务器
4. 查看浏览器开发者工具的Network面板

### Q: 如何添加新的代理路径？
A: 在vite.config.js的server.proxy中添加新的配置：
```javascript
'/new-api': {
  target: 'http://localhost:3001',
  changeOrigin: true,
  secure: false
}
```

### Q: 生产环境会使用代理吗？
A: 不会。生产环境使用直接的API地址，代理只在开发环境中使用。

### Q: 如何调试代理请求？
A: 
1. 查看vite.config.js中的configure回调日志
2. 使用浏览器开发者工具的Network面板
3. 运行代理测试工具查看详细信息

## 部署注意事项

### 开发环境
- 确保后端服务运行在 `http://localhost:3000`
- 前端开发服务器运行在 `http://localhost:5173`
- 代理自动处理所有API请求

### 生产环境
- 前端构建后部署到CDN或静态服务器
- API请求直接发送到生产环境的后端服务器
- 需要确保生产环境的CORS配置正确

## 性能考虑

- 代理会增加少量延迟（通常<10ms）
- 开发环境性能影响可忽略
- 生产环境不使用代理，性能不受影响

## 安全考虑

- 代理配置只在开发环境生效
- 生产环境使用HTTPS和正确的CORS配置
- 敏感信息不应在代理配置中暴露
