<!--
  愿境模态框组件
  用于显示弹窗、对话框等模态内容
-->
<template>
  <view 
    v-if="visible"
    class="wish-modal"
    :class="{
      'wish-modal--center': position === 'center',
      'wish-modal--bottom': position === 'bottom'
    }"
    @click="handleMaskClick"
  >
    <!-- 遮罩层 -->
    <view class="wish-modal__mask" :class="{ 'wish-modal__mask--show': showMask }"></view>
    
    <!-- 模态框内容 -->
    <view 
      class="wish-modal__content"
      :class="[
        `wish-modal__content--${position}`,
        {
          'wish-modal__content--show': contentVisible
        }
      ]"
      @click.stop
    >
      <!-- 头部 -->
      <view v-if="title || showClose" class="wish-modal__header">
        <view class="wish-modal__title">{{ title }}</view>
        <view 
          v-if="showClose"
          class="wish-modal__close"
          @click="handleClose"
        >
          <image src="/static/icons/close.png" class="wish-modal__close-icon" />
        </view>
      </view>
      
      <!-- 内容区域 -->
      <view class="wish-modal__body">
        <slot></slot>
      </view>
      
      <!-- 底部 -->
      <view v-if="$slots.footer" class="wish-modal__footer">
        <slot name="footer"></slot>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'WishModal',
  props: {
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    // 标题
    title: {
      type: String,
      default: ''
    },
    // 位置
    position: {
      type: String,
      default: 'center',
      validator: (value) => ['center', 'bottom'].includes(value)
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      default: true
    },
    // 是否显示遮罩
    showMask: {
      type: Boolean,
      default: true
    },
    // 点击遮罩是否关闭
    maskClosable: {
      type: Boolean,
      default: true
    },
    // 层级
    zIndex: {
      type: Number,
      default: 1000
    }
  },
  
  data() {
    return {
      contentVisible: false
    }
  },
  
  watch: {
    visible: {
      handler(newVal) {
        if (newVal) {
          this.show()
        } else {
          this.hide()
        }
      },
      immediate: true
    }
  },
  
  methods: {
    show() {
      // 延迟显示内容，实现动画效果
      this.$nextTick(() => {
        setTimeout(() => {
          this.contentVisible = true
        }, 50)
      })
    },
    
    hide() {
      this.contentVisible = false
    },
    
    handleMaskClick() {
      if (this.maskClosable) {
        this.handleClose()
      }
    },
    
    handleClose() {
      this.$emit('close')
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.wish-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: v-bind(zIndex);
  display: flex;
  align-items: center;
  justify-content: center;
  
  &--bottom {
    align-items: flex-end;
  }
}

.wish-modal__mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
  
  &--show {
    opacity: 1;
  }
}

.wish-modal__content {
  position: relative;
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  box-shadow: $wish-shadow-lg;
  max-width: 90%;
  max-height: 80%;
  overflow: hidden;
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
  
  &--center {
    margin: $wish-spacing-md;
  }
  
  &--bottom {
    width: 100%;
    max-width: 100%;
    border-radius: $wish-radius-lg $wish-radius-lg 0 0;
    transform: translateY(100%);
  }
  
  &--show {
    opacity: 1;
    
    &.wish-modal__content--center {
      transform: scale(1);
    }
    
    &.wish-modal__content--bottom {
      transform: translateY(0);
    }
  }
}

.wish-modal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $wish-spacing-md $wish-spacing-md 0;
  border-bottom: 2rpx solid $wish-border-light;
  margin-bottom: $wish-spacing-md;
}

.wish-modal__title {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
}

.wish-modal__close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: $wish-bg-primary;
  }
}

.wish-modal__close-icon {
  width: 24rpx;
  height: 24rpx;
}

.wish-modal__body {
  padding: $wish-spacing-md;
  max-height: 60vh;
  overflow-y: auto;
}

.wish-modal__footer {
  padding: 0 $wish-spacing-md $wish-spacing-md;
  border-top: 2rpx solid $wish-border-light;
  margin-top: $wish-spacing-md;
  padding-top: $wish-spacing-md;
}
</style>
