/**
 * 守护者状态管理
 * 管理赐福列表、赐福模板、守护者功能等
 */

import { defineStore } from 'pinia'
import { guardianAPI } from '@/api'

export const useGuardianStore = defineStore('guardian', {
  state: () => ({
    // 赐福列表
    blessingList: [],
    // 我的赐福列表
    myBlessingList: [],
    // 赐福模板
    blessingTemplates: [],
    // 当前赐福详情
    currentBlessing: null,
    // 分页信息
    pagination: {
      current: 1,
      pageSize: 20,
      total: 0,
      hasMore: true
    },
    // 加载状态
    loading: {
      list: false,
      templates: false,
      bless: false,
      feedback: false
    }
  }),

  getters: {
    // 按类型分组的赐福模板
    templatesByType: (state) => {
      const grouped = {}
      state.blessingTemplates.forEach(template => {
        const type = template.type || 'default'
        if (!grouped[type]) {
          grouped[type] = []
        }
        grouped[type].push(template)
      })
      return grouped
    },
    
    // 获取收到感谢的赐福
    thankedBlessings: (state) => {
      return state.myBlessingList.filter(blessing => blessing.isThanked)
    },
    
    // 赐福统计
    blessingStats: (state) => ({
      total: state.myBlessingList.length,
      thanked: state.myBlessingList.filter(b => b.isThanked).length,
      pending: state.myBlessingList.filter(b => !b.isThanked).length
    })
  },

  actions: {
    /**
     * 获取赐福列表
     */
    async fetchBlessingList(params = {}, refresh = false) {
      if (refresh) {
        this.pagination.current = 1
        this.pagination.hasMore = true
      }
      
      if (!refresh && !this.pagination.hasMore) {
        return
      }
      
      this.loading.list = true
      try {
        const requestParams = {
          page: this.pagination.current,
          pageSize: this.pagination.pageSize,
          ...params
        }
        
        const result = await guardianAPI.getBlessingList(requestParams)
        
        if (refresh) {
          this.blessingList = result.list
        } else {
          this.blessingList.push(...result.list)
        }
        
        this.pagination.total = result.total
        this.pagination.current += 1
        this.pagination.hasMore = result.list.length === this.pagination.pageSize
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.list = false
      }
    },

    /**
     * 获取我的赐福列表
     */
    async fetchMyBlessings(params = {}, refresh = false) {
      this.loading.list = true
      try {
        const result = await guardianAPI.getMyBlessings(params)
        
        if (refresh) {
          this.myBlessingList = result.list
        } else {
          this.myBlessingList.push(...result.list)
        }
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.list = false
      }
    },

    /**
     * 获取赐福模板
     */
    async fetchBlessingTemplates() {
      this.loading.templates = true
      try {
        const templates = await guardianAPI.getBlessingTemplates()
        this.blessingTemplates = templates
        return templates
      } catch (error) {
        throw error
      } finally {
        this.loading.templates = false
      }
    },

    /**
     * 赐福心愿
     */
    async blessWish(blessingData) {
      this.loading.bless = true
      try {
        const result = await guardianAPI.blessWish(blessingData)
        
        // 添加到我的赐福列表
        this.myBlessingList.unshift(result)
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.bless = false
      }
    },

    /**
     * 反馈赐福（处理叩谢）
     */
    async feedbackBlessing(blessingId, feedback) {
      this.loading.feedback = true
      try {
        const result = await guardianAPI.feedbackBlessing(blessingId, feedback)
        
        // 更新赐福状态
        const updateBlessing = (blessing) => {
          if (blessing.id === blessingId) {
            blessing.isThanked = true
            blessing.feedback = feedback
            blessing.thankedAt = new Date().toISOString()
          }
          return blessing
        }
        
        this.blessingList = this.blessingList.map(updateBlessing)
        this.myBlessingList = this.myBlessingList.map(updateBlessing)
        
        return result
      } catch (error) {
        throw error
      } finally {
        this.loading.feedback = false
      }
    },

    /**
     * 上传赐福图片
     */
    async uploadBlessingImage(filePath) {
      try {
        const result = await guardianAPI.uploadBlessingImage(filePath)
        return result
      } catch (error) {
        throw error
      }
    },

    /**
     * 获取指定模板
     */
    getTemplateById(templateId) {
      return this.blessingTemplates.find(template => template.id === templateId)
    },

    /**
     * 获取指定类型的模板
     */
    getTemplatesByType(type) {
      return this.blessingTemplates.filter(template => template.type === type)
    },

    /**
     * 清空赐福列表
     */
    clearBlessingList() {
      this.blessingList = []
      this.pagination.current = 1
      this.pagination.hasMore = true
    }
  }
})
