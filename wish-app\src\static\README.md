# 静态资源目录

这个目录用于存放应用的静态资源文件，包括图片、图标、字体等。

## 目录结构

```
static/
├── icons/          # 图标文件
├── images/         # 图片文件
├── tabbar/         # 底部导航栏图标
├── avatars/        # 默认头像
└── backgrounds/    # 背景图片
```

## 图标规范

- 图标格式：PNG（支持透明度）
- 图标尺寸：建议使用 48x48px 或更高分辨率
- 命名规范：使用小写字母和连字符，如 `home-active.png`

## 图片规范

- 图片格式：PNG、JPG、WebP
- 图片压缩：建议使用工具压缩图片以减小包体积
- 命名规范：使用描述性名称，如 `wish-background.jpg`

## 使用方式

在组件中使用静态资源：

```vue
<template>
  <image src="/static/icons/home.png" />
</template>
```

在CSS中使用：

```scss
.background {
  background-image: url('/static/images/background.jpg');
}
```
