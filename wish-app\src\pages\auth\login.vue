<!--
  愿境登录页面
  用户登录功能实现
-->
<template>
  <view class="login-page">
    <!-- 背景装饰 -->
    <view class="background-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>
    
    <!-- 主要内容 -->
    <view class="login-content">
      <!-- Logo和标题 -->
      <view class="login-header">
        <image src="/static/logo.png" class="app-logo" />
        <text class="app-title">愿境</text>
        <text class="app-subtitle">让心愿被温柔以待</text>
      </view>
      
      <!-- 登录表单 -->
      <view class="login-form">
        <wish-input
          v-model="formData.phone"
          type="text"
          placeholder="请输入手机号"
          prefix-icon="/static/icons/phone.png"
          :error="errors.phone"
          :error-message="errors.phone"
          @input="clearError('phone')"
        />
        
        <wish-input
          v-model="formData.password"
          type="password"
          placeholder="请输入密码"
          prefix-icon="/static/icons/password.png"
          :error="errors.password"
          :error-message="errors.password"
          @input="clearError('password')"
        />
        
        <!-- 忘记密码 -->
        <view class="form-options">
          <text class="forgot-password" @click="goToForgotPassword">忘记密码？</text>
        </view>
        
        <!-- 登录按钮 -->
        <wish-button
          type="primary"
          size="large"
          text="登录"
          :loading="loading"
          @click="handleLogin"
          class="login-button"
        />
        
        <!-- 注册链接 -->
        <view class="register-link">
          <text class="register-text">还没有账号？</text>
          <text class="register-action" @click="goToRegister">立即注册</text>
        </view>
      </view>
      
      <!-- 第三方登录 -->
      <view class="third-party-login">
        <view class="divider">
          <view class="divider-line"></view>
          <text class="divider-text">其他登录方式</text>
          <view class="divider-line"></view>
        </view>
        
        <view class="third-party-buttons">
          <view class="third-party-item" @click="loginWithWechat">
            <image src="/static/icons/wechat.png" class="third-party-icon" />
            <text class="third-party-text">微信登录</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 用户协议 -->
    <view class="agreement">
      <text class="agreement-text">
        登录即表示同意
        <text class="agreement-link" @click="showUserAgreement">《用户协议》</text>
        和
        <text class="agreement-link" @click="showPrivacyPolicy">《隐私政策》</text>
      </text>
    </view>
  </view>
</template>

<script>
import { useUserStore } from '@/store'
import { validator, navigation, toast } from '@/utils'

export default {
  data() {
    return {
      formData: {
        phone: '',
        password: ''
      },
      errors: {},
      loading: false
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    }
  },
  
  onLoad() {
    // 如果已经登录，直接跳转到首页
    if (this.userStore.isLoggedIn) {
      navigation.switchTab('/pages/index/index')
    }
  },
  
  methods: {
    /**
     * 表单验证
     */
    validateForm() {
      const errors = {}
      
      // 验证手机号
      if (!this.formData.phone) {
        errors.phone = '请输入手机号'
      } else if (!validator.isPhone(this.formData.phone)) {
        errors.phone = '请输入正确的手机号'
      }
      
      // 验证密码
      if (!this.formData.password) {
        errors.password = '请输入密码'
      } else if (this.formData.password.length < 6) {
        errors.password = '密码长度不能少于6位'
      }
      
      this.errors = errors
      return Object.keys(errors).length === 0
    },
    
    /**
     * 清除错误信息
     */
    clearError(field) {
      if (this.errors[field]) {
        this.$delete(this.errors, field)
      }
    },
    
    /**
     * 处理登录
     */
    async handleLogin() {
      if (!this.validateForm()) {
        return
      }
      
      this.loading = true
      try {
        await this.userStore.login(this.formData.phone, this.formData.password)
        
        toast.success('登录成功')
        
        // 跳转到首页
        setTimeout(() => {
          navigation.switchTab('/pages/index/index')
        }, 1000)
        
      } catch (error) {
        console.error('登录失败:', error)
        toast.error(error.message || '登录失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 微信登录
     */
    async loginWithWechat() {
      try {
        // TODO: 实现微信登录
        toast.info('微信登录功能开发中')
      } catch (error) {
        console.error('微信登录失败:', error)
        toast.error('微信登录失败')
      }
    },
    
    /**
     * 页面跳转
     */
    goToRegister() {
      navigation.navigateTo('/pages/auth/register')
    },
    
    goToForgotPassword() {
      navigation.navigateTo('/pages/auth/forgot-password')
    },
    
    /**
     * 显示协议
     */
    showUserAgreement() {
      navigation.navigateTo('/pages/legal/user-agreement')
    },
    
    showPrivacyPolicy() {
      navigation.navigateTo('/pages/legal/privacy-policy')
    }
  }
}
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, $wish-bg-primary 0%, rgba(232, 180, 160, 0.1) 100%);
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(232, 180, 160, 0.1);
  
  &.circle-1 {
    width: 200rpx;
    height: 200rpx;
    top: 10%;
    right: -50rpx;
    animation: float 6s ease-in-out infinite;
  }
  
  &.circle-2 {
    width: 150rpx;
    height: 150rpx;
    top: 60%;
    left: -30rpx;
    animation: float 8s ease-in-out infinite reverse;
  }
  
  &.circle-3 {
    width: 100rpx;
    height: 100rpx;
    bottom: 20%;
    right: 20%;
    animation: float 10s ease-in-out infinite;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* 主要内容 */
.login-content {
  padding: $wish-spacing-xxl $wish-spacing-md;
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

/* 头部 */
.login-header {
  text-align: center;
  margin-bottom: $wish-spacing-xxl;
  margin-top: 20vh;
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: $wish-spacing-md;
}

.app-title {
  display: block;
  font-size: $wish-font-xxl;
  font-weight: 600;
  color: $wish-color-primary;
  margin-bottom: $wish-spacing-xs;
}

.app-subtitle {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
}

/* 登录表单 */
.login-form {
  flex: 1;
  margin-bottom: $wish-spacing-lg;
}

.form-options {
  text-align: right;
  margin-bottom: $wish-spacing-lg;
}

.forgot-password {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
}

.login-button {
  width: 100%;
  margin-bottom: $wish-spacing-md;
}

.register-link {
  text-align: center;
}

.register-text {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  margin-right: $wish-spacing-xs;
}

.register-action {
  font-size: $wish-font-md;
  color: $wish-color-primary;
  font-weight: 500;
}

/* 第三方登录 */
.third-party-login {
  margin-bottom: $wish-spacing-lg;
}

.divider {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-md;
}

.divider-line {
  flex: 1;
  height: 2rpx;
  background-color: $wish-border-light;
}

.divider-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin: 0 $wish-spacing-md;
}

.third-party-buttons {
  display: flex;
  justify-content: center;
}

.third-party-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  transition: background-color 0.3s ease;
  
  &:active {
    background-color: rgba(232, 180, 160, 0.1);
  }
}

.third-party-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: $wish-spacing-xs;
}

.third-party-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

/* 用户协议 */
.agreement {
  text-align: center;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.agreement-text {
  font-size: $wish-font-xs;
  color: $wish-text-secondary;
  line-height: 1.6;
}

.agreement-link {
  color: $wish-color-primary;
}
</style>
