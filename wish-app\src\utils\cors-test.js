/**
 * CORS连接测试工具
 * 用于测试前端与后端的跨域请求
 */

export class CORSTest {
  constructor(baseURL = 'http://localhost:3000') {
    this.baseURL = baseURL
  }

  // 测试简单请求（GET）
  async testSimpleRequest() {
    try {
      console.log('🧪 测试简单GET请求...')
      
      const response = await fetch(`${this.baseURL}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log('✅ 简单GET请求成功:', data)
        return { success: true, data }
      } else {
        console.log('❌ 简单GET请求失败:', response.status, response.statusText)
        return { success: false, error: `${response.status} ${response.statusText}` }
      }
    } catch (error) {
      console.log('❌ 简单GET请求异常:', error.message)
      return { success: false, error: error.message }
    }
  }

  // 测试预检请求（OPTIONS）
  async testPreflightRequest() {
    try {
      console.log('🧪 测试预检OPTIONS请求...')
      
      const response = await fetch(`${this.baseURL}/api/users/checkin-status`, {
        method: 'OPTIONS',
        headers: {
          'Origin': window.location.origin,
          'Access-Control-Request-Method': 'GET',
          'Access-Control-Request-Headers': 'Content-Type, Authorization'
        }
      })
      
      console.log('OPTIONS响应状态:', response.status)
      console.log('OPTIONS响应头:')
      for (const [key, value] of response.headers.entries()) {
        if (key.toLowerCase().includes('access-control')) {
          console.log(`  ${key}: ${value}`)
        }
      }
      
      if (response.ok || response.status === 204) {
        console.log('✅ 预检OPTIONS请求成功')
        return { success: true }
      } else {
        console.log('❌ 预检OPTIONS请求失败:', response.status, response.statusText)
        return { success: false, error: `${response.status} ${response.statusText}` }
      }
    } catch (error) {
      console.log('❌ 预检OPTIONS请求异常:', error.message)
      return { success: false, error: error.message }
    }
  }

  // 测试复杂请求（POST with JSON）
  async testComplexRequest() {
    try {
      console.log('🧪 测试复杂POST请求...')
      
      const response = await fetch(`${this.baseURL}/api/users/checkin-status`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Origin': window.location.origin
        },
        credentials: 'include'
      })
      
      if (response.ok) {
        const data = await response.json()
        console.log('✅ 复杂POST请求成功:', data)
        return { success: true, data }
      } else {
        console.log('❌ 复杂POST请求失败:', response.status, response.statusText)
        const errorText = await response.text()
        console.log('错误详情:', errorText)
        return { success: false, error: `${response.status} ${response.statusText}`, details: errorText }
      }
    } catch (error) {
      console.log('❌ 复杂POST请求异常:', error.message)
      return { success: false, error: error.message }
    }
  }

  // 运行完整的CORS测试
  async runFullTest() {
    console.log('🚀 开始CORS连接测试...\n')
    console.log(`前端地址: ${window.location.origin}`)
    console.log(`后端地址: ${this.baseURL}\n`)
    
    const results = {
      simple: await this.testSimpleRequest(),
      preflight: await this.testPreflightRequest(),
      complex: await this.testComplexRequest()
    }
    
    console.log('\n📊 CORS测试结果汇总:')
    console.log(`简单请求: ${results.simple.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`预检请求: ${results.preflight.success ? '✅ 通过' : '❌ 失败'}`)
    console.log(`复杂请求: ${results.complex.success ? '✅ 通过' : '❌ 失败'}`)
    
    const allPassed = results.simple.success && results.preflight.success && results.complex.success
    
    if (allPassed) {
      console.log('\n🎉 所有CORS测试通过！前后端连接正常。')
    } else {
      console.log('\n⚠️  部分CORS测试失败，请检查后端CORS配置。')
      
      if (!results.simple.success) {
        console.log('建议检查: 后端服务是否正常运行')
      }
      if (!results.preflight.success) {
        console.log('建议检查: CORS预检请求配置')
      }
      if (!results.complex.success) {
        console.log('建议检查: CORS复杂请求配置和认证设置')
      }
    }
    
    return {
      success: allPassed,
      results
    }
  }
}

// 创建全局测试实例
export const corsTest = new CORSTest()

// 快速测试函数
export async function quickCORSTest() {
  return await corsTest.runFullTest()
}

export default CORSTest
