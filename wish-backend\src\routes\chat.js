/**
 * 私聊路由
 * 处理私聊相关的API请求
 */

const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const { getModel } = require('../models');
const authMiddleware = require('../middleware/auth');
const { AppError, catchAsync, handleValidationErrors } = require('../middleware/errorHandler');
const { Cache } = require('../utils/redis');
const { isUserOnline, getOnlineUserCount } = require('../socket');
const logger = require('../utils/logger');

const router = express.Router();

/**
 * 私聊模块根路由
 */
router.get('/', (req, res) => {
  res.json({
    module: '私聊模块',
    endpoints: {
      conversations: 'GET /api/chat/conversations',
      conversation: 'GET /api/chat/conversations/:userId',
      sendMessage: 'POST /api/chat/messages',
      deleteMessage: 'DELETE /api/chat/messages/:messageId',
      unreadCount: 'GET /api/chat/unread-count',
      markRead: 'PUT /api/chat/conversations/:userId/read',
      search: 'GET /api/chat/search',
      onlineStats: 'GET /api/chat/online-stats'
    },
    socketEvents: {
      send_message: '发送消息',
      mark_read: '标记已读',
      typing: '正在输入',
      stop_typing: '停止输入',
      get_online_status: '获取在线状态'
    }
  });
});

/**
 * 获取对话列表
 */
router.get('/conversations', [
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须在1-50之间')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { page = 1, limit = 20 } = req.query;
  const userId = req.user.userId;

  const Message = getModel('Message');
  const conversations = await Message.getConversationList(userId, {
    page: parseInt(page),
    limit: parseInt(limit)
  });

  // 添加在线状态信息
  const conversationsWithStatus = conversations.map(conversation => {
    const otherUserId = conversation.sender_id === userId ? 
      conversation.receiver_id : conversation.sender_id;
    
    const otherUser = conversation.sender_id === userId ? 
      conversation.receiver : conversation.sender;

    return {
      ...conversation.toJSON(),
      otherUser: {
        ...otherUser.toJSON(),
        isOnline: isUserOnline(otherUserId)
      },
      unreadCount: conversation.receiver_id === userId && !conversation.is_read ? 1 : 0
    };
  });

  res.json({
    conversations: conversationsWithStatus,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: conversations.length
    }
  });
}));

/**
 * 获取与特定用户的对话历史
 */
router.get('/conversations/:userId', [
  param('userId').isInt().withMessage('用户ID必须是整数'),
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('beforeMessageId').optional().isInt().withMessage('消息ID必须是整数')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { userId } = req.params;
  const { page = 1, limit = 50, beforeMessageId } = req.query;
  const currentUserId = req.user.userId;

  // 检查对方用户是否存在
  const User = getModel('User');
  const otherUser = await User.findByPk(userId, {
    attributes: ['id', 'username', 'nickname', 'avatar', 'allow_messages']
  });

  if (!otherUser) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  const Message = getModel('Message');
  const messages = await Message.getConversation(currentUserId, parseInt(userId), {
    page: parseInt(page),
    limit: parseInt(limit),
    beforeMessageId: beforeMessageId ? parseInt(beforeMessageId) : null
  });

  // 标记消息为已读
  await Message.markConversationAsRead(currentUserId, parseInt(userId));

  res.json({
    messages: messages.reverse(), // 按时间正序返回
    otherUser: {
      ...otherUser.toJSON(),
      isOnline: isUserOnline(parseInt(userId))
    },
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      hasMore: messages.length === parseInt(limit)
    }
  });
}));

/**
 * 发送消息（HTTP接口，作为Socket.IO的备用）
 */
router.post('/messages', [
  body('receiverId').isInt().withMessage('接收者ID必须是整数'),
  body('content').isLength({ min: 1, max: 1000 }).withMessage('消息内容长度必须在1-1000字符之间'),
  body('type').optional().isIn(['text', 'image', 'voice', 'video', 'file']).withMessage('消息类型无效'),
  body('mediaUrl').optional().isURL().withMessage('媒体URL格式无效'),
  body('replyToId').optional().isInt().withMessage('回复消息ID必须是整数')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const {
    receiverId,
    content,
    type = 'text',
    mediaUrl,
    replyToId
  } = req.body;
  const senderId = req.user.userId;

  if (senderId === receiverId) {
    throw new AppError('不能给自己发送消息', 400, 'CANNOT_MESSAGE_SELF');
  }

  // 检查接收者是否存在
  const User = getModel('User');
  const receiver = await User.findByPk(receiverId);
  if (!receiver) {
    throw new AppError('接收者不存在', 404, 'RECEIVER_NOT_FOUND');
  }

  // 检查是否允许发送消息
  if (!receiver.allow_messages) {
    throw new AppError('对方不接受私信', 403, 'MESSAGES_NOT_ALLOWED');
  }

  // 创建消息
  const Message = getModel('Message');
  const message = await Message.create({
    sender_id: senderId,
    receiver_id: receiverId,
    content,
    type,
    media_url: mediaUrl,
    reply_to_id: replyToId
  });

  // 获取完整的消息信息
  const fullMessage = await Message.findByPk(message.id, {
    include: [
      {
        model: User,
        as: 'sender',
        attributes: ['id', 'username', 'nickname', 'avatar']
      },
      {
        model: User,
        as: 'receiver',
        attributes: ['id', 'username', 'nickname', 'avatar']
      }
    ]
  });

  logger.info(`HTTP消息发送: ${req.user.username} -> ${receiver.username}`);

  res.status(201).json({
    message: '消息发送成功',
    data: fullMessage
  });
}));

/**
 * 删除消息
 */
router.delete('/messages/:messageId', [
  param('messageId').isInt().withMessage('消息ID必须是整数')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { messageId } = req.params;
  const userId = req.user.userId;

  const Message = getModel('Message');
  const message = await Message.findByPk(messageId);

  if (!message) {
    throw new AppError('消息不存在', 404, 'MESSAGE_NOT_FOUND');
  }

  // 检查权限（只能删除自己发送的消息或接收的消息）
  if (message.sender_id !== userId && message.receiver_id !== userId) {
    throw new AppError('无权删除此消息', 403, 'ACCESS_DENIED');
  }

  // 软删除（记录删除者）
  await message.markAsDeleted(userId);

  res.json({
    message: '消息删除成功'
  });
}));

/**
 * 获取未读消息数量
 */
router.get('/unread-count', authMiddleware.required, catchAsync(async (req, res) => {
  const userId = req.user.userId;
  const Message = getModel('Message');

  const unreadCount = await Message.getUnreadCount(userId);

  res.json({
    unreadCount
  });
}));

/**
 * 标记对话为已读
 */
router.put('/conversations/:userId/read', [
  param('userId').isInt().withMessage('用户ID必须是整数')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { userId } = req.params;
  const currentUserId = req.user.userId;

  const Message = getModel('Message');
  await Message.markConversationAsRead(currentUserId, parseInt(userId));

  res.json({
    message: '标记已读成功'
  });
}));

/**
 * 搜索消息
 */
router.get('/search', [
  query('keyword').isLength({ min: 1, max: 50 }).withMessage('搜索关键词长度必须在1-50字符之间'),
  query('userId').optional().isInt().withMessage('用户ID必须是整数'),
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 50 }).withMessage('每页数量必须在1-50之间')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const {
    keyword,
    userId,
    page = 1,
    limit = 20
  } = req.query;
  const currentUserId = req.user.userId;

  const Message = getModel('Message');
  const User = getModel('User');
  const { Op } = require('sequelize');

  const where = {
    [Op.or]: [
      { sender_id: currentUserId },
      { receiver_id: currentUserId }
    ],
    content: {
      [Op.like]: `%${keyword}%`
    }
  };

  // 如果指定了用户，只搜索与该用户的对话
  if (userId) {
    where[Op.and] = [
      {
        [Op.or]: [
          { sender_id: currentUserId, receiver_id: parseInt(userId) },
          { sender_id: parseInt(userId), receiver_id: currentUserId }
        ]
      }
    ];
  }

  const result = await Message.findAndCountAll({
    where,
    order: [['created_at', 'DESC']],
    offset: (page - 1) * limit,
    limit: parseInt(limit),
    include: [
      {
        model: User,
        as: 'sender',
        attributes: ['id', 'username', 'nickname', 'avatar']
      },
      {
        model: User,
        as: 'receiver',
        attributes: ['id', 'username', 'nickname', 'avatar']
      }
    ]
  });

  res.json({
    messages: result.rows,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: result.count,
      pages: Math.ceil(result.count / limit)
    }
  });
}));

/**
 * 获取在线用户统计
 */
router.get('/online-stats', authMiddleware.optional, catchAsync(async (req, res) => {
  const onlineCount = getOnlineUserCount();

  res.json({
    onlineUsers: onlineCount,
    timestamp: new Date()
  });
}));

module.exports = router;
