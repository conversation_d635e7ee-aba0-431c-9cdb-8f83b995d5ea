<!--
  愿境我的心愿页面
  展示用户发布的所有心愿
-->
<template>
  <view class="my-wishes-page">
    <!-- 统计信息 -->
    <view class="stats-header">
      <view class="stat-item">
        <text class="stat-number">{{ totalWishes }}</text>
        <text class="stat-label">总心愿</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-number">{{ blessedWishes }}</text>
        <text class="stat-label">已赐福</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-number">{{ pendingWishes }}</text>
        <text class="stat-label">等待中</text>
      </view>
    </view>
    
    <!-- 状态筛选 -->
    <view class="status-filter">
      <view 
        class="filter-item"
        :class="{ 'filter-item--active': statusFilter === status.value }"
        v-for="status in statusOptions"
        :key="status.value"
        @click="changeStatusFilter(status.value)"
      >
        <text class="filter-text">{{ status.label }}</text>
      </view>
    </view>
    
    <!-- 心愿列表 -->
    <scroll-view 
      class="wish-list"
      scroll-y
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="wish-items">
        <wish-card
          v-for="wish in myWishList"
          :key="wish.id"
          class="wish-item"
          shadow="light"
          hover
          clickable
          @click="goToWishDetail(wish.id)"
        >
          <view class="wish-content">
            <!-- 心愿状态标识 -->
            <view class="wish-status-badge">
              <view 
                class="status-dot"
                :class="`status-dot--${wish.status}`"
              ></view>
              <text class="status-text">{{ getStatusText(wish.status) }}</text>
            </view>
            
            <!-- 心愿内容 -->
            <text class="wish-title">{{ wish.title }}</text>
            <text class="wish-desc">{{ wish.content }}</text>
            
            <!-- 心愿标签 -->
            <view class="wish-tags">
              <text 
                v-for="tag in wish.tags" 
                :key="tag"
                class="wish-tag"
              >
                #{{ tag }}
              </text>
            </view>
            
            <!-- 心愿数据 -->
            <view class="wish-stats">
              <view class="stat-group">
                <image src="/static/icons/heart.png" class="stat-icon" />
                <text class="stat-value">{{ wish.supportCount || 0 }}</text>
                <text class="stat-text">助力</text>
              </view>
              
              <view class="stat-group">
                <image src="/static/icons/comment.png" class="stat-icon" />
                <text class="stat-value">{{ wish.commentCount || 0 }}</text>
                <text class="stat-text">评论</text>
              </view>
              
              <view class="stat-group" v-if="wish.blessings && wish.blessings.length > 0">
                <image src="/static/icons/blessing.png" class="stat-icon" />
                <text class="stat-value">{{ wish.blessings.length }}</text>
                <text class="stat-text">赐福</text>
              </view>
            </view>
            
            <!-- 发布时间 -->
            <view class="wish-time">
              <text class="time-text">{{ formatTime(wish.createdAt) }}</text>
            </view>
            
            <!-- 操作按钮 -->
            <view class="wish-actions">
              <wish-button
                v-if="wish.status === 'blessed'"
                type="success"
                size="small"
                text="查看赐福"
                @click.stop="viewBlessings(wish.id)"
                class="action-button"
              />
              
              <wish-button
                type="ghost"
                size="small"
                text="编辑"
                @click.stop="editWish(wish.id)"
                class="action-button"
              />
              
              <wish-button
                type="error"
                size="small"
                text="删除"
                @click.stop="deleteWish(wish.id)"
                class="action-button"
              />
            </view>
          </view>
        </wish-card>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <wish-loading v-if="loading" type="dots" text="加载中..." />
        <text v-else class="load-text" @click="loadMore">点击加载更多</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-else-if="myWishList.length > 0">
        <text class="no-more-text">没有更多心愿了</text>
      </view>
      
      <!-- 空状态 */
      <view class="empty-state" v-if="!loading && myWishList.length === 0">
        <image src="/static/icons/empty-my-wishes.png" class="empty-icon" />
        <text class="empty-text">{{ getEmptyText() }}</text>
        <text class="empty-desc">{{ getEmptyDesc() }}</text>
        <wish-button
          type="primary"
          text="发布心愿"
          @click="goToPostWish"
          class="empty-button"
        />
      </view>
    </scroll-view>
    
    <!-- 删除确认模态框 -->
    <wish-modal
      v-model:visible="showDeleteModal"
      title="确认删除"
      :mask-closable="false"
    >
      <view class="delete-content">
        <text class="delete-text">确定要删除这个心愿吗？</text>
        <text class="delete-desc">删除后无法恢复，请谨慎操作</text>
      </view>
      
      <template #footer>
        <view class="delete-actions">
          <wish-button
            type="ghost"
            text="取消"
            @click="showDeleteModal = false"
            class="delete-button"
          />
          <wish-button
            type="error"
            text="确认删除"
            :loading="deleting"
            @click="confirmDelete"
            class="delete-button"
          />
        </view>
      </template>
    </wish-modal>
  </view>
</template>

<script>
import { useWishStore } from '@/store'
import { timeUtils, navigation, toast } from '@/utils'

export default {
  data() {
    return {
      statusFilter: 'all',
      loading: false,
      refreshing: false,
      showDeleteModal: false,
      deleteWishId: null,
      deleting: false,
      statusOptions: [
        { value: 'all', label: '全部' },
        { value: 'pending', label: '等待中' },
        { value: 'blessed', label: '已赐福' }
      ]
    }
  },
  
  computed: {
    wishStore() {
      return useWishStore()
    },
    
    myWishList() {
      return this.wishStore.myWishList
    },
    
    hasMore() {
      return this.wishStore.pagination.hasMore
    },
    
    totalWishes() {
      return this.myWishList.length
    },
    
    blessedWishes() {
      return this.myWishList.filter(wish => wish.status === 'blessed').length
    },
    
    pendingWishes() {
      return this.myWishList.filter(wish => wish.status === 'pending').length
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.refreshData()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      await this.loadMyWishes(true)
    },
    
    /**
     * 刷新数据
     */
    async refreshData() {
      await this.loadMyWishes(true)
    },
    
    /**
     * 加载我的心愿
     */
    async loadMyWishes(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      try {
        const params = {
          status: this.statusFilter === 'all' ? '' : this.statusFilter
        }
        
        await this.wishStore.fetchMyWishes(params, refresh)
      } catch (error) {
        console.error('加载我的心愿失败:', error)
        toast.error('加载失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 加载更多
     */
    async loadMore() {
      if (!this.hasMore || this.loading) return
      await this.loadMyWishes(false)
    },
    
    /**
     * 下拉刷新
     */
    async onRefresh() {
      this.refreshing = true
      try {
        await this.loadMyWishes(true)
      } finally {
        this.refreshing = false
      }
    },
    
    /**
     * 改变状态筛选
     */
    async changeStatusFilter(status) {
      if (this.statusFilter === status) return
      
      this.statusFilter = status
      await this.loadMyWishes(true)
    },
    
    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        pending: '等待守护',
        blessed: '已赐福'
      }
      return statusMap[status] || '未知状态'
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      return timeUtils.relativeTime(timestamp)
    },
    
    /**
     * 获取空状态文本
     */
    getEmptyText() {
      if (this.statusFilter === 'blessed') {
        return '还没有被赐福的心愿'
      } else if (this.statusFilter === 'pending') {
        return '没有等待中的心愿'
      }
      return '还没有发布过心愿'
    },
    
    /**
     * 获取空状态描述
     */
    getEmptyDesc() {
      if (this.statusFilter === 'all') {
        return '发布你的第一个心愿吧'
      }
      return '去许愿广场看看其他人的心愿'
    },
    
    /**
     * 查看赐福
     */
    viewBlessings(wishId) {
      navigation.navigateTo('/pages/wisher/wish-blessings', { wishId })
    },
    
    /**
     * 编辑心愿
     */
    editWish(wishId) {
      navigation.navigateTo('/pages/wisher/edit-wish', { id: wishId })
    },
    
    /**
     * 删除心愿
     */
    deleteWish(wishId) {
      this.deleteWishId = wishId
      this.showDeleteModal = true
    },
    
    /**
     * 确认删除
     */
    async confirmDelete() {
      if (!this.deleteWishId) return
      
      this.deleting = true
      try {
        await this.wishStore.deleteWish(this.deleteWishId)
        toast.success('删除成功')
        this.showDeleteModal = false
        this.deleteWishId = null
      } catch (error) {
        console.error('删除心愿失败:', error)
        toast.error(error.message || '删除失败')
      } finally {
        this.deleting = false
      }
    },
    
    /**
     * 页面跳转
     */
    goToWishDetail(wishId) {
      navigation.navigateTo('/pages/wisher/wish-detail', { id: wishId })
    },
    
    goToPostWish() {
      navigation.switchTab('/pages/wisher/post-wish')
    }
  }
}
</script>

<style lang="scss" scoped>
.my-wishes-page {
  height: 100vh;
  background-color: $wish-bg-primary;
  display: flex;
  flex-direction: column;
}

/* 统计信息 */
.stats-header {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-lg;
  border-bottom: 2rpx solid $wish-border-light;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: $wish-font-xxl;
  font-weight: 600;
  color: $wish-color-primary;
  margin-bottom: $wish-spacing-xs;
}

.stat-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background-color: $wish-border-light;
}

/* 状态筛选 */
.status-filter {
  display: flex;
  align-items: center;
  padding: $wish-spacing-sm $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.filter-item {
  padding: $wish-spacing-xs $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-right: $wish-spacing-md;
  transition: all 0.3s ease;

  &--active {
    background-color: $wish-color-primary;

    .filter-text {
      color: $wish-text-inverse;
    }
  }
}

.filter-text {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

/* 心愿列表 */
.wish-list {
  flex: 1;
  padding: $wish-spacing-sm;
}

.wish-items {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.wish-item {
  margin: 0;
}

.wish-content {
  padding: 0;
  position: relative;
}

/* 心愿状态标识 */
.wish-status-badge {
  display: flex;
  align-items: center;
  position: absolute;
  top: 0;
  right: 0;
  padding: $wish-spacing-xs $wish-spacing-sm;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 0 $wish-radius-lg 0 $wish-radius-md;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-xs;

  &--pending {
    background-color: $wish-color-warning;
  }

  &--blessed {
    background-color: $wish-color-success;
  }
}

.status-text {
  font-size: $wish-font-xs;
  color: $wish-text-secondary;
}

/* 心愿内容 */
.wish-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
  margin-top: $wish-spacing-lg;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wish-desc {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-sm;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.wish-tags {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-xs;
  margin-bottom: $wish-spacing-sm;
}

.wish-tag {
  font-size: $wish-font-xs;
  color: $wish-color-primary;
  background-color: rgba(232, 180, 160, 0.1);
  padding: 4rpx 8rpx;
  border-radius: $wish-radius-sm;
}

/* 心愿数据 */
.wish-stats {
  display: flex;
  align-items: center;
  gap: $wish-spacing-lg;
  margin-bottom: $wish-spacing-sm;
}

.stat-group {
  display: flex;
  align-items: center;
}

.stat-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: $wish-spacing-xs;
}

.stat-value {
  font-size: $wish-font-sm;
  font-weight: 500;
  color: $wish-text-primary;
  margin-right: 4rpx;
}

.stat-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

/* 发布时间 */
.wish-time {
  margin-bottom: $wish-spacing-md;
}

.time-text {
  font-size: $wish-font-sm;
  color: $wish-text-disabled;
}

/* 操作按钮 */
.wish-actions {
  display: flex;
  gap: $wish-spacing-sm;
  padding-top: $wish-spacing-sm;
  border-top: 2rpx solid $wish-border-light;
}

.action-button {
  flex: 1;
  min-height: auto;
  padding: $wish-spacing-xs $wish-spacing-sm;
}

/* 加载状态 */
.load-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.load-text {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
}

.no-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.no-more-text {
  font-size: $wish-font-sm;
  color: $wish-text-disabled;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $wish-spacing-xxl;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: $wish-spacing-lg;
  opacity: 0.6;
}

.empty-text {
  font-size: $wish-font-lg;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.empty-desc {
  font-size: $wish-font-md;
  color: $wish-text-disabled;
  margin-bottom: $wish-spacing-xl;
}

.empty-button {
  width: 300rpx;
}

/* 删除确认模态框 */
.delete-content {
  text-align: center;
  padding: $wish-spacing-lg 0;
}

.delete-text {
  display: block;
  font-size: $wish-font-lg;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.delete-desc {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
}

.delete-actions {
  display: flex;
  gap: $wish-spacing-md;
}

.delete-button {
  flex: 1;
}
</style>
