<!--
  愿境排行榜页面
  展示社区用户的各项排名数据
-->
<template>
  <view class="leaderboard-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar safe-area-top">
      <view class="navbar-content">
        <view class="navbar-back" @click="goBack">
          <image src="/static/icons/arrow-left.png" class="back-icon" />
        </view>
        <text class="navbar-title">排行榜</text>
        <view class="navbar-placeholder"></view>
      </view>
    </view>
    
    <!-- 排行榜类型切换 -->
    <view class="leaderboard-tabs">
      <view 
        class="tab-item"
        :class="{ 'tab-item--active': currentType === type.value }"
        v-for="type in leaderboardTypes"
        :key="type.value"
        @click="switchType(type.value)"
      >
        <image :src="type.icon" class="tab-icon" />
        <text class="tab-text">{{ type.name }}</text>
      </view>
    </view>
    
    <!-- 时间范围选择 -->
    <view class="time-range-selector">
      <view 
        class="range-item"
        :class="{ 'range-item--active': timeRange === range.value }"
        v-for="range in timeRanges"
        :key="range.value"
        @click="switchTimeRange(range.value)"
      >
        <text class="range-text">{{ range.label }}</text>
      </view>
    </view>
    
    <!-- 我的排名 -->
    <view class="my-ranking" v-if="myRanking">
      <wish-card class="ranking-card" shadow="light">
        <view class="my-rank-content">
          <view class="rank-info">
            <text class="rank-label">我的排名</text>
            <text class="rank-number">#{{ myRanking.rank || '未上榜' }}</text>
          </view>
          
          <view class="rank-avatar">
            <image :src="userStore.avatar" class="avatar-img" />
          </view>
          
          <view class="rank-data">
            <text class="rank-value">{{ myRanking.value || 0 }}</text>
            <text class="rank-unit">{{ getCurrentTypeUnit() }}</text>
          </view>
        </view>
      </wish-card>
    </view>
    
    <!-- 排行榜列表 -->
    <scroll-view 
      class="leaderboard-list"
      scroll-y
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="ranking-items">
        <view 
          v-for="(item, index) in leaderboardData"
          :key="item.userId"
          class="ranking-item"
          @click="goToUserProfile(item.userId)"
        >
          <!-- 排名徽章 -->
          <view class="rank-badge">
            <view v-if="index < 3" class="medal-container">
              <image :src="getMedalIcon(index)" class="medal-icon" />
              <text class="medal-rank">{{ index + 1 }}</text>
            </view>
            <text v-else class="rank-text">{{ index + 1 }}</text>
          </view>
          
          <!-- 用户信息 -->
          <view class="user-info">
            <image :src="item.avatar" class="user-avatar" />
            <view class="user-details">
              <text class="user-nickname">{{ item.nickname }}</text>
              <view class="user-badges">
                <text v-if="item.isGuardian" class="badge guardian-badge">守护者</text>
                <text v-if="item.level > 1" class="badge level-badge">Lv.{{ item.level }}</text>
              </view>
            </view>
          </view>
          
          <!-- 数据展示 -->
          <view class="rank-data">
            <text class="data-value">{{ formatValue(item.value) }}</text>
            <text class="data-unit">{{ getCurrentTypeUnit() }}</text>
            
            <!-- 趋势指示 -->
            <view v-if="item.trend" class="trend-indicator">
              <image 
                :src="getTrendIcon(item.trend)" 
                class="trend-icon"
                :class="`trend-icon--${item.trend}`"
              />
              <text class="trend-text">{{ item.rankChange || 0 }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <wish-loading v-if="loading" type="dots" text="加载中..." />
        <text v-else class="load-text" @click="loadMore">点击加载更多</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-else-if="leaderboardData.length > 0">
        <text class="no-more-text">没有更多数据了</text>
      </view>
      
      <!-- 空状态 */
      <view class="empty-state" v-if="!loading && leaderboardData.length === 0">
        <image src="/static/icons/empty-leaderboard.png" class="empty-icon" />
        <text class="empty-text">暂无排行数据</text>
        <text class="empty-desc">快去参与社区活动吧</text>
      </view>
    </scroll-view>
    
    <!-- 排行榜说明 -->
    <view class="leaderboard-info">
      <wish-button
        type="ghost"
        size="small"
        text="排行榜说明"
        @click="showInfoModal = true"
        class="info-button"
      />
    </view>
    
    <!-- 排行榜说明模态框 -->
    <wish-modal
      v-model:visible="showInfoModal"
      title="排行榜说明"
      position="center"
    >
      <view class="info-content">
        <view class="info-section">
          <text class="info-title">功德值排行榜</text>
          <text class="info-desc">根据用户获得的功德值进行排名，通过赐福他人心愿可获得功德值。</text>
        </view>
        
        <view class="info-section">
          <text class="info-title">心愿力排行榜</text>
          <text class="info-desc">根据用户拥有的心愿力进行排名，通过每日仪式和社区互动可获得心愿力。</text>
        </view>
        
        <view class="info-section">
          <text class="info-title">活跃度排行榜</text>
          <text class="info-desc">根据用户的社区活跃度进行排名，包括发布心愿、评论、助力等行为。</text>
        </view>
        
        <view class="info-section">
          <text class="info-title">更新时间</text>
          <text class="info-desc">排行榜数据每小时更新一次，排名变化会有趋势指示。</text>
        </view>
      </view>
    </wish-modal>
  </view>
</template>

<script>
import { useUserStore, useSocialStore } from '@/store'
import { navigation, toast } from '@/utils'

export default {
  data() {
    return {
      currentType: 'merit',
      timeRange: 'week',
      loading: false,
      refreshing: false,
      showInfoModal: false,
      leaderboardData: [],
      myRanking: null,
      leaderboardTypes: [
        {
          value: 'merit',
          name: '功德值',
          icon: '/static/icons/merit-ranking.png'
        },
        {
          value: 'wishPower',
          name: '心愿力',
          icon: '/static/icons/wish-power-ranking.png'
        },
        {
          value: 'activity',
          name: '活跃度',
          icon: '/static/icons/activity-ranking.png'
        },
        {
          value: 'blessing',
          name: '赐福数',
          icon: '/static/icons/blessing-ranking.png'
        }
      ],
      timeRanges: [
        { value: 'day', label: '今日' },
        { value: 'week', label: '本周' },
        { value: 'month', label: '本月' },
        { value: 'all', label: '总榜' }
      ]
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    socialStore() {
      return useSocialStore()
    },
    
    hasMore() {
      return this.socialStore.pagination.hasMore
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.refreshData()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      await this.loadLeaderboard(true)
    },
    
    /**
     * 刷新数据
     */
    async refreshData() {
      await this.loadLeaderboard(true)
    },
    
    /**
     * 加载排行榜数据
     */
    async loadLeaderboard(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      try {
        const params = {
          type: this.currentType,
          timeRange: this.timeRange
        }
        
        const result = await this.socialStore.fetchLeaderboard(params, refresh)
        
        if (refresh) {
          this.leaderboardData = result.list || []
          this.myRanking = result.myRanking || null
        } else {
          this.leaderboardData.push(...(result.list || []))
        }
      } catch (error) {
        console.error('加载排行榜失败:', error)
        toast.error('加载失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 加载更多
     */
    async loadMore() {
      if (!this.hasMore || this.loading) return
      await this.loadLeaderboard(false)
    },
    
    /**
     * 下拉刷新
     */
    async onRefresh() {
      this.refreshing = true
      try {
        await this.loadLeaderboard(true)
      } finally {
        this.refreshing = false
      }
    },
    
    /**
     * 切换排行榜类型
     */
    async switchType(type) {
      if (this.currentType === type) return
      
      this.currentType = type
      await this.loadLeaderboard(true)
    },
    
    /**
     * 切换时间范围
     */
    async switchTimeRange(range) {
      if (this.timeRange === range) return
      
      this.timeRange = range
      await this.loadLeaderboard(true)
    },
    
    /**
     * 获取当前类型的单位
     */
    getCurrentTypeUnit() {
      const unitMap = {
        merit: '分',
        wishPower: '点',
        activity: '分',
        blessing: '个'
      }
      return unitMap[this.currentType] || ''
    },
    
    /**
     * 获取奖牌图标
     */
    getMedalIcon(index) {
      const medals = [
        '/static/icons/gold-medal.png',
        '/static/icons/silver-medal.png',
        '/static/icons/bronze-medal.png'
      ]
      return medals[index] || '/static/icons/medal.png'
    },
    
    /**
     * 获取趋势图标
     */
    getTrendIcon(trend) {
      const trendMap = {
        up: '/static/icons/trend-up.png',
        down: '/static/icons/trend-down.png',
        same: '/static/icons/trend-same.png'
      }
      return trendMap[trend] || '/static/icons/trend-same.png'
    },
    
    /**
     * 格式化数值
     */
    formatValue(value) {
      if (value >= 10000) {
        return (value / 10000).toFixed(1) + 'w'
      } else if (value >= 1000) {
        return (value / 1000).toFixed(1) + 'k'
      }
      return value.toString()
    },
    
    /**
     * 页面跳转
     */
    goBack() {
      navigation.navigateBack()
    },
    
    goToUserProfile(userId) {
      navigation.navigateTo('/pages/user/user-profile', { userId })
    }
  }
}
</script>

<style lang="scss" scoped>
.leaderboard-page {
  min-height: 100vh;
  background-color: $wish-bg-primary;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $wish-spacing-md;
}

.navbar-back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
}

.navbar-title {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
}

.navbar-placeholder {
  width: 48rpx;
}

/* 排行榜类型切换 */
.leaderboard-tabs {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-md;
  border-bottom: 2rpx solid $wish-border-light;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $wish-spacing-sm;
  border-radius: $wish-radius-md;
  transition: all 0.3s ease;

  &--active {
    background-color: rgba(232, 180, 160, 0.1);

    .tab-text {
      color: $wish-color-primary;
      font-weight: 600;
    }
  }
}

.tab-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: $wish-spacing-xs;
}

.tab-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

/* 时间范围选择 */
.time-range-selector {
  display: flex;
  align-items: center;
  padding: $wish-spacing-sm $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.range-item {
  padding: $wish-spacing-xs $wish-spacing-sm;
  border-radius: $wish-radius-sm;
  margin-right: $wish-spacing-md;
  transition: all 0.3s ease;

  &--active {
    background-color: $wish-color-primary;

    .range-text {
      color: $wish-text-inverse;
    }
  }
}

.range-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

/* 我的排名 */
.my-ranking {
  padding: $wish-spacing-md;
}

.ranking-card {
  margin: 0;
}

.my-rank-content {
  display: flex;
  align-items: center;
  padding: $wish-spacing-md;
}

.rank-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: $wish-spacing-lg;
}

.rank-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.rank-number {
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-color-primary;
}

.rank-avatar {
  margin-right: $wish-spacing-lg;
}

.avatar-img {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.rank-data {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.rank-value {
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.rank-unit {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

/* 排行榜列表 */
.leaderboard-list {
  flex: 1;
  padding: $wish-spacing-md;
}

.ranking-items {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.ranking-item {
  display: flex;
  align-items: center;
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-md;
  box-shadow: $wish-shadow-sm;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.rank-badge {
  width: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: $wish-spacing-md;
}

.medal-container {
  position: relative;
  width: 48rpx;
  height: 48rpx;
}

.medal-icon {
  width: 100%;
  height: 100%;
}

.medal-rank {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: $wish-font-xs;
  font-weight: 600;
  color: $wish-text-inverse;
}

.rank-text {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-secondary;
}

.user-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
}

.user-details {
  flex: 1;
}

.user-nickname {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
  @extend .ellipsis;
}

.user-badges {
  display: flex;
  gap: $wish-spacing-xs;
}

.badge {
  font-size: $wish-font-xs;
  padding: 2rpx 8rpx;
  border-radius: $wish-radius-sm;
}

.guardian-badge {
  color: $wish-color-secondary;
  background-color: rgba(212, 165, 116, 0.1);
}

.level-badge {
  color: $wish-color-primary;
  background-color: rgba(232, 180, 160, 0.1);
}

.rank-data {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.data-value {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.data-unit {
  font-size: $wish-font-xs;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.trend-indicator {
  display: flex;
  align-items: center;
}

.trend-icon {
  width: 16rpx;
  height: 16rpx;
  margin-right: $wish-spacing-xs;

  &--up {
    color: $wish-color-success;
  }

  &--down {
    color: $wish-color-error;
  }

  &--same {
    color: $wish-text-disabled;
  }
}

.trend-text {
  font-size: $wish-font-xs;
  color: $wish-text-secondary;
}

/* 加载状态 */
.load-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.load-text {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
}

.no-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.no-more-text {
  font-size: $wish-font-sm;
  color: $wish-text-disabled;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $wish-spacing-xxl;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: $wish-spacing-lg;
  opacity: 0.6;
}

.empty-text {
  font-size: $wish-font-lg;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.empty-desc {
  font-size: $wish-font-md;
  color: $wish-text-disabled;
}

/* 排行榜说明 */
.leaderboard-info {
  text-align: center;
  padding: $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-top: 2rpx solid $wish-border-light;
}

.info-button {
  min-height: auto;
  padding: $wish-spacing-xs $wish-spacing-md;
}

/* 排行榜说明模态框 */
.info-content {
  padding: $wish-spacing-md 0;
}

.info-section {
  margin-bottom: $wish-spacing-lg;

  &:last-child {
    margin-bottom: 0;
  }
}

.info-title {
  display: block;
  font-size: $wish-font-md;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.info-desc {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  line-height: 1.6;
}
</style>
