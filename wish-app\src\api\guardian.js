/**
 * 守护相关API
 */

import request from './request'
import { API_CONFIG } from '@/config'

const { endpoints } = API_CONFIG

export const guardianAPI = {
  // 为心愿赐福
  blessWish(wishId, data) {
    return request.post(`${endpoints.guardian.bless}/${wishId}`, data)
  },

  // 获取心愿的赐福列表
  getBlessings(wishId, params = {}) {
    const defaultParams = {
      page: 1,
      limit: 20
    }
    return request.get(`${endpoints.guardian.blessings}/${wishId}`, { ...defaultParams, ...params })
  },

  // 感谢赐福
  thankBlessing(blessingId, message = '') {
    return request.post(`${endpoints.guardian.thank}/${blessingId}/thank`, { message })
  },

  // 获取我的赐福历史
  getMyBlessings(params = {}) {
    const defaultParams = {
      page: 1,
      limit: 20,
      type: ''
    }
    return request.get(endpoints.guardian.myBlessings, { ...defaultParams, ...params })
  },

  // 获取收到的赐福
  getReceivedBlessings(params = {}) {
    const defaultParams = {
      page: 1,
      limit: 20
    }
    return request.get(endpoints.guardian.receivedBlessings, { ...defaultParams, ...params })
  },

  // 获取守护者统计
  getGuardianStats() {
    return request.get(endpoints.guardian.stats)
  }
}

export default guardianAPI
