<!--
  愿境发布心愿页面
  用户发布新心愿的功能页面
-->
<template>
  <view class="post-wish-page">
    <!-- 心愿力检查 -->
    <view v-if="!canPostWish" class="insufficient-power">
      <view class="power-warning">
        <image src="/static/icons/warning.png" class="warning-icon" />
        <text class="warning-text">心愿力不足</text>
        <text class="warning-desc">发布心愿需要消耗 5 点心愿力</text>
      </view>
      
      <view class="power-actions">
        <wish-button
          type="primary"
          text="完成每日仪式"
          @click="goToDailyRitual"
          class="action-button"
        />
        <wish-button
          type="ghost"
          text="助力他人心愿"
          @click="goToWishPlaza"
          class="action-button"
        />
      </view>
    </view>
    
    <!-- 发布表单 -->
    <view v-else class="post-form">
      <!-- 心愿标题 -->
      <view class="form-section">
        <text class="section-title">心愿标题</text>
        <wish-input
          v-model="formData.title"
          placeholder="简洁地描述你的心愿..."
          :maxlength="50"
          show-word-limit
          :error="errors.title"
          :error-message="errors.title"
          @input="clearError('title')"
        />
      </view>
      
      <!-- 心愿内容 -->
      <view class="form-section">
        <text class="section-title">详细描述</text>
        <wish-input
          v-model="formData.content"
          type="textarea"
          placeholder="详细描述你的心愿，让守护者更好地理解..."
          :maxlength="500"
          show-word-limit
          :auto-height="true"
          :error="errors.content"
          :error-message="errors.content"
          @input="clearError('content')"
        />
      </view>
      
      <!-- 心愿标签 -->
      <view class="form-section">
        <text class="section-title">选择标签</text>
        <view class="tag-list">
          <view 
            class="tag-item"
            :class="{ 'tag-item--selected': formData.tags.includes(tag) }"
            v-for="tag in availableTags"
            :key="tag"
            @click="toggleTag(tag)"
          >
            <text class="tag-text">{{ tag }}</text>
          </view>
        </view>
        <text class="tag-hint">最多选择3个标签</text>
      </view>
      
      <!-- 心愿类型 -->
      <view class="form-section">
        <text class="section-title">心愿类型</text>
        <view class="type-options">
          <view 
            class="type-option"
            :class="{ 'type-option--selected': formData.type === type.value }"
            v-for="type in wishTypes"
            :key="type.value"
            @click="selectType(type.value)"
          >
            <image :src="type.icon" class="type-icon" />
            <text class="type-name">{{ type.name }}</text>
            <text class="type-desc">{{ type.desc }}</text>
          </view>
        </view>
      </view>
      
      <!-- 是否匿名 -->
      <view class="form-section">
        <view class="anonymous-option" @click="toggleAnonymous">
          <view class="anonymous-info">
            <text class="anonymous-title">匿名发布</text>
            <text class="anonymous-desc">其他用户将看不到你的真实身份</text>
          </view>
          <view 
            class="anonymous-switch"
            :class="{ 'anonymous-switch--on': formData.anonymous }"
          >
            <view class="switch-thumb"></view>
          </view>
        </view>
      </view>
      
      <!-- 心愿力消耗提示 -->
      <view class="cost-info">
        <view class="cost-item">
          <image src="/static/icons/wish-power.png" class="cost-icon" />
          <text class="cost-text">将消耗 5 点心愿力</text>
        </view>
        <view class="current-power">
          <text class="power-text">当前心愿力：{{ userStore.userStats.wishPower }}</text>
        </view>
      </view>
      
      <!-- 发布按钮 -->
      <wish-button
        type="primary"
        size="large"
        text="发布心愿"
        :loading="publishing"
        @click="publishWish"
        class="publish-button"
      />
    </view>
    
    <!-- 发布成功模态框 -->
    <wish-modal
      v-model:visible="showSuccessModal"
      title="心愿发布成功"
      :show-close="false"
      :mask-closable="false"
    >
      <view class="success-content">
        <image src="/static/icons/success-large.png" class="success-icon" />
        <text class="success-text">你的心愿已经发布到愿境</text>
        <text class="success-desc">守护者们会看到并给予回应</text>
      </view>
      
      <template #footer>
        <view class="success-actions">
          <wish-button
            type="ghost"
            text="继续发布"
            @click="continuePost"
            class="success-button"
          />
          <wish-button
            type="primary"
            text="查看我的心愿"
            @click="goToMyWishes"
            class="success-button"
          />
        </view>
      </template>
    </wish-modal>
  </view>
</template>

<script>
import { useUserStore, useWishStore, useSystemStore } from '@/store'
import { validator, navigation, toast } from '@/utils'
import { CONSTANTS } from '@/config'

export default {
  data() {
    return {
      formData: {
        title: '',
        content: '',
        tags: [],
        type: 'personal',
        anonymous: false
      },
      errors: {},
      publishing: false,
      showSuccessModal: false,
      availableTags: [],
      wishTypes: [
        {
          value: 'personal',
          name: '个人心愿',
          desc: '关于自己的愿望',
          icon: '/static/icons/personal-wish.png'
        },
        {
          value: 'family',
          name: '家庭心愿',
          desc: '关于家人的愿望',
          icon: '/static/icons/family-wish.png'
        },
        {
          value: 'career',
          name: '事业心愿',
          desc: '关于工作事业的愿望',
          icon: '/static/icons/career-wish.png'
        },
        {
          value: 'health',
          name: '健康心愿',
          desc: '关于身心健康的愿望',
          icon: '/static/icons/health-wish.png'
        },
        {
          value: 'other',
          name: '其他心愿',
          desc: '其他类型的愿望',
          icon: '/static/icons/other-wish.png'
        }
      ]
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    wishStore() {
      return useWishStore()
    },
    
    systemStore() {
      return useSystemStore()
    },
    
    canPostWish() {
      return this.userStore.canPostWish
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    // 检查用户是否有足够的心愿力
    if (!this.canPostWish) {
      this.userStore.fetchUserStats()
    }
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      try {
        // 获取可用标签
        const tags = await this.systemStore.fetchTags()
        this.availableTags = tags || CONSTANTS.WISH_TAGS
      } catch (error) {
        console.error('获取标签失败:', error)
        this.availableTags = CONSTANTS.WISH_TAGS
      }
    },
    
    /**
     * 表单验证
     */
    validateForm() {
      const errors = {}
      
      // 验证标题
      if (!this.formData.title.trim()) {
        errors.title = '请输入心愿标题'
      } else if (this.formData.title.length > CONSTANTS.WISH_TITLE_MAX_LENGTH) {
        errors.title = `标题不能超过${CONSTANTS.WISH_TITLE_MAX_LENGTH}字`
      }
      
      // 验证内容
      if (!this.formData.content.trim()) {
        errors.content = '请详细描述你的心愿'
      } else if (this.formData.content.length > CONSTANTS.WISH_CONTENT_MAX_LENGTH) {
        errors.content = `内容不能超过${CONSTANTS.WISH_CONTENT_MAX_LENGTH}字`
      }
      
      // 验证标签
      if (this.formData.tags.length === 0) {
        toast.error('请至少选择一个标签')
        return false
      }
      
      this.errors = errors
      return Object.keys(errors).length === 0
    },
    
    /**
     * 清除错误信息
     */
    clearError(field) {
      if (this.errors[field]) {
        this.$delete(this.errors, field)
      }
    },
    
    /**
     * 切换标签选择
     */
    toggleTag(tag) {
      const index = this.formData.tags.indexOf(tag)
      if (index > -1) {
        this.formData.tags.splice(index, 1)
      } else {
        if (this.formData.tags.length >= 3) {
          toast.error('最多只能选择3个标签')
          return
        }
        this.formData.tags.push(tag)
      }
    },
    
    /**
     * 选择心愿类型
     */
    selectType(type) {
      this.formData.type = type
    },
    
    /**
     * 切换匿名状态
     */
    toggleAnonymous() {
      this.formData.anonymous = !this.formData.anonymous
    },
    
    /**
     * 发布心愿
     */
    async publishWish() {
      if (!this.validateForm()) {
        return
      }
      
      if (!this.canPostWish) {
        toast.error('心愿力不足，无法发布心愿')
        return
      }
      
      this.publishing = true
      try {
        // 创建心愿数据
        const wishData = {
          title: this.formData.title.trim(),
          content: this.formData.content.trim(),
          tags: this.formData.tags,
          type: this.formData.type,
          anonymous: this.formData.anonymous
        }
        
        // 发布心愿
        await this.wishStore.createWish(wishData)
        
        // 消耗心愿力
        this.userStore.consumeWishPower(5)
        
        // 显示成功模态框
        this.showSuccessModal = true
        
      } catch (error) {
        console.error('发布心愿失败:', error)
        toast.error(error.message || '发布失败，请重试')
      } finally {
        this.publishing = false
      }
    },
    
    /**
     * 继续发布
     */
    continuePost() {
      this.showSuccessModal = false
      this.resetForm()
    },
    
    /**
     * 重置表单
     */
    resetForm() {
      this.formData = {
        title: '',
        content: '',
        tags: [],
        type: 'personal',
        anonymous: false
      }
      this.errors = {}
    },
    
    /**
     * 页面跳转
     */
    goToDailyRitual() {
      navigation.navigateTo('/pages/wisher/daily-ritual')
    },
    
    goToWishPlaza() {
      navigation.switchTab('/pages/wisher/wish-plaza')
    },
    
    goToMyWishes() {
      this.showSuccessModal = false
      navigation.navigateTo('/pages/wisher/my-wishes')
    }
  }
}
</script>

<style lang="scss" scoped>
.post-wish-page {
  min-height: 100vh;
  background-color: $wish-bg-primary;
  padding: $wish-spacing-md;
}

/* 心愿力不足提示 */
.insufficient-power {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.power-warning {
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-xl;
  margin-bottom: $wish-spacing-xl;
  box-shadow: $wish-shadow-sm;
}

.warning-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: $wish-spacing-md;
}

.warning-text {
  display: block;
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-color-warning;
  margin-bottom: $wish-spacing-xs;
}

.warning-desc {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
}

.power-actions {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-md;
  width: 100%;
}

.action-button {
  width: 100%;
}

/* 发布表单 */
.post-form {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

.form-section {
  margin-bottom: $wish-spacing-xl;
}

.section-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-md;
}

/* 标签选择 */
.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-sm;
  margin-bottom: $wish-spacing-xs;
}

.tag-item {
  padding: $wish-spacing-xs $wish-spacing-sm;
  border: 2rpx solid $wish-border-medium;
  border-radius: $wish-radius-md;
  transition: all 0.3s ease;

  &--selected {
    border-color: $wish-color-primary;
    background-color: rgba(232, 180, 160, 0.1);

    .tag-text {
      color: $wish-color-primary;
    }
  }
}

.tag-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

.tag-hint {
  font-size: $wish-font-xs;
  color: $wish-text-disabled;
}

/* 心愿类型 */
.type-options {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.type-option {
  display: flex;
  align-items: center;
  padding: $wish-spacing-md;
  border: 2rpx solid $wish-border-light;
  border-radius: $wish-radius-md;
  background-color: $wish-bg-secondary;
  transition: all 0.3s ease;

  &--selected {
    border-color: $wish-color-primary;
    background-color: rgba(232, 180, 160, 0.1);
  }
}

.type-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: $wish-spacing-md;
}

.type-name {
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: 4rpx;
}

.type-desc {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  flex: 1;
}

/* 匿名选项 */
.anonymous-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-md;
  box-shadow: $wish-shadow-sm;
}

.anonymous-info {
  flex: 1;
}

.anonymous-title {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: 4rpx;
}

.anonymous-desc {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.anonymous-switch {
  width: 80rpx;
  height: 44rpx;
  background-color: $wish-border-medium;
  border-radius: 22rpx;
  position: relative;
  transition: background-color 0.3s ease;

  &--on {
    background-color: $wish-color-primary;

    .switch-thumb {
      transform: translateX(36rpx);
    }
  }
}

.switch-thumb {
  width: 36rpx;
  height: 36rpx;
  background-color: $wish-bg-secondary;
  border-radius: 50%;
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  transition: transform 0.3s ease;
  box-shadow: $wish-shadow-sm;
}

/* 消耗提示 */
.cost-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $wish-spacing-md;
  background-color: rgba(232, 180, 160, 0.1);
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-xl;
}

.cost-item {
  display: flex;
  align-items: center;
}

.cost-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: $wish-spacing-xs;
}

.cost-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.current-power {
  display: flex;
  align-items: center;
}

.power-text {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
  font-weight: 500;
}

/* 发布按钮 */
.publish-button {
  width: 100%;
}

/* 成功模态框 */
.success-content {
  text-align: center;
  padding: $wish-spacing-lg 0;
}

.success-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: $wish-spacing-lg;
}

.success-text {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.success-desc {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
}

.success-actions {
  display: flex;
  gap: $wish-spacing-md;
}

.success-button {
  flex: 1;
}
</style>
