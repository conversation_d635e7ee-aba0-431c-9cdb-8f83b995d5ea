/**
 * 心愿模型
 * 定义心愿数据结构和相关方法
 */

const mongoose = require('mongoose');

const wishSchema = new mongoose.Schema({
  // 基本信息
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  
  content: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  
  // 心愿创建者
  creator: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // 心愿类型
  type: {
    type: String,
    enum: ['health', 'career', 'love', 'family', 'study', 'wealth', 'travel', 'other'],
    required: true
  },
  
  // 心愿分类
  category: {
    type: String,
    enum: ['personal', 'family', 'friends', 'society', 'world'],
    default: 'personal'
  },
  
  // 心愿图片
  images: [{
    url: String,
    thumbnail: String,
    alt: String
  }],
  
  // 心愿标签
  tags: [{
    type: String,
    trim: true,
    maxlength: 20
  }],
  
  // 心愿状态
  status: {
    type: String,
    enum: ['pending', 'active', 'fulfilled', 'expired', 'hidden', 'deleted'],
    default: 'active'
  },
  
  // 可见性设置
  visibility: {
    type: String,
    enum: ['public', 'friends', 'private'],
    default: 'public'
  },
  
  // 地理位置
  location: {
    type: {
      type: String,
      enum: ['Point'],
      default: 'Point'
    },
    coordinates: {
      type: [Number], // [longitude, latitude]
      default: [0, 0]
    },
    address: {
      province: String,
      city: String,
      district: String,
      detail: String
    }
  },
  
  // 心愿期限
  deadline: {
    type: Date
  },
  
  // 优先级
  priority: {
    type: String,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium'
  },
  
  // 统计数据
  stats: {
    views: {
      type: Number,
      default: 0
    },
    likes: {
      type: Number,
      default: 0
    },
    shares: {
      type: Number,
      default: 0
    },
    comments: {
      type: Number,
      default: 0
    },
    blessings: {
      type: Number,
      default: 0
    }
  },
  
  // 互动用户
  interactions: {
    likes: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      createdAt: {
        type: Date,
        default: Date.now
      }
    }],
    shares: [{
      user: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      platform: String,
      createdAt: {
        type: Date,
        default: Date.now
      }
    }]
  },
  
  // 赐福信息
  blessings: [{
    guardian: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    content: {
      type: String,
      required: true,
      maxlength: 500
    },
    images: [{
      url: String,
      thumbnail: String
    }],
    type: {
      type: String,
      enum: ['blessing', 'prayer', 'encouragement', 'advice'],
      default: 'blessing'
    },
    isAnonymous: {
      type: Boolean,
      default: false
    },
    thanked: {
      type: Boolean,
      default: false
    },
    thankedAt: Date,
    thankMessage: String,
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // 审核信息
  moderation: {
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'flagged'],
      default: 'approved'
    },
    reviewedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    reviewedAt: Date,
    reason: String,
    autoReviewed: {
      type: Boolean,
      default: false
    }
  },
  
  // 推荐权重
  recommendation: {
    score: {
      type: Number,
      default: 0
    },
    factors: {
      recency: Number,
      engagement: Number,
      quality: Number,
      relevance: Number
    },
    lastCalculated: {
      type: Date,
      default: Date.now
    }
  },
  
  // 元数据
  metadata: {
    source: {
      type: String,
      enum: ['web', 'mobile', 'miniprogram'],
      default: 'mobile'
    },
    version: {
      type: String,
      default: '1.0'
    },
    ip: String,
    userAgent: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// 索引
wishSchema.index({ creator: 1, createdAt: -1 });
wishSchema.index({ type: 1, status: 1 });
wishSchema.index({ status: 1, visibility: 1, createdAt: -1 });
wishSchema.index({ 'stats.likes': -1 });
wishSchema.index({ 'stats.blessings': -1 });
wishSchema.index({ 'recommendation.score': -1 });
wishSchema.index({ location: '2dsphere' });
wishSchema.index({ tags: 1 });
wishSchema.index({ deadline: 1 });

// 虚拟字段
wishSchema.virtual('isExpired').get(function() {
  return this.deadline && this.deadline < new Date();
});

wishSchema.virtual('daysLeft').get(function() {
  if (!this.deadline) return null;
  const now = new Date();
  const diff = this.deadline.getTime() - now.getTime();
  return Math.ceil(diff / (1000 * 60 * 60 * 24));
});

wishSchema.virtual('blessingsCount').get(function() {
  return this.blessings.length;
});

wishSchema.virtual('likesCount').get(function() {
  return this.interactions.likes.length;
});

// 中间件
wishSchema.pre('save', function(next) {
  // 更新统计数据
  this.stats.likes = this.interactions.likes.length;
  this.stats.blessings = this.blessings.length;
  
  // 检查是否过期
  if (this.isExpired && this.status === 'active') {
    this.status = 'expired';
  }
  
  next();
});

wishSchema.pre(/^find/, function(next) {
  // 默认不查询已删除的心愿
  if (!this.getQuery().status) {
    this.where({ status: { $ne: 'deleted' } });
  }
  next();
});

// 实例方法
wishSchema.methods.addView = function() {
  this.stats.views += 1;
  return this.save();
};

wishSchema.methods.toggleLike = function(userId) {
  const likeIndex = this.interactions.likes.findIndex(
    like => like.user.toString() === userId.toString()
  );
  
  if (likeIndex > -1) {
    // 取消点赞
    this.interactions.likes.splice(likeIndex, 1);
    return { action: 'unliked', count: this.interactions.likes.length };
  } else {
    // 添加点赞
    this.interactions.likes.push({ user: userId });
    return { action: 'liked', count: this.interactions.likes.length };
  }
};

wishSchema.methods.addShare = function(userId, platform) {
  this.interactions.shares.push({
    user: userId,
    platform: platform
  });
  this.stats.shares += 1;
  return this.save();
};

wishSchema.methods.addBlessing = function(blessingData) {
  this.blessings.push(blessingData);
  this.stats.blessings += 1;
  return this.save();
};

wishSchema.methods.thankBlessing = function(blessingId, thankMessage) {
  const blessing = this.blessings.id(blessingId);
  if (blessing) {
    blessing.thanked = true;
    blessing.thankedAt = new Date();
    blessing.thankMessage = thankMessage;
    return this.save();
  }
  throw new Error('赐福不存在');
};

wishSchema.methods.updateRecommendationScore = function() {
  const now = new Date();
  const ageInDays = (now - this.createdAt) / (1000 * 60 * 60 * 24);
  
  // 计算各项因子
  const recency = Math.max(0, 1 - ageInDays / 30); // 30天内的新鲜度
  const engagement = Math.min(1, (this.stats.likes + this.stats.comments * 2 + this.stats.blessings * 3) / 100);
  const quality = this.content.length > 50 ? 1 : 0.5; // 内容质量简单判断
  
  // 综合评分
  this.recommendation.score = (recency * 0.3 + engagement * 0.5 + quality * 0.2) * 100;
  this.recommendation.factors = { recency, engagement, quality };
  this.recommendation.lastCalculated = now;
  
  return this.save();
};

// 静态方法
wishSchema.statics.getPublicWishes = function(options = {}) {
  const {
    page = 1,
    limit = 20,
    type,
    category,
    sortBy = 'createdAt',
    sortOrder = -1,
    location,
    radius = 10000 // 10km
  } = options;
  
  const query = {
    status: 'active',
    visibility: 'public',
    'moderation.status': 'approved'
  };
  
  if (type) query.type = type;
  if (category) query.category = category;
  
  // 地理位置查询
  if (location && location.coordinates) {
    query.location = {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: location.coordinates
        },
        $maxDistance: radius
      }
    };
  }
  
  const sort = { [sortBy]: sortOrder };
  
  return this.find(query)
    .populate('creator', 'username profile.nickname profile.avatar')
    .sort(sort)
    .skip((page - 1) * limit)
    .limit(limit)
    .lean();
};

wishSchema.statics.getRecommendedWishes = function(userId, limit = 20) {
  return this.find({
    status: 'active',
    visibility: 'public',
    'moderation.status': 'approved',
    creator: { $ne: userId }
  })
    .populate('creator', 'username profile.nickname profile.avatar')
    .sort({ 'recommendation.score': -1, createdAt: -1 })
    .limit(limit)
    .lean();
};

wishSchema.statics.searchWishes = function(keyword, options = {}) {
  const {
    page = 1,
    limit = 20,
    type,
    category
  } = options;
  
  const query = {
    status: 'active',
    visibility: 'public',
    'moderation.status': 'approved',
    $or: [
      { title: { $regex: keyword, $options: 'i' } },
      { content: { $regex: keyword, $options: 'i' } },
      { tags: { $in: [new RegExp(keyword, 'i')] } }
    ]
  };
  
  if (type) query.type = type;
  if (category) query.category = category;
  
  return this.find(query)
    .populate('creator', 'username profile.nickname profile.avatar')
    .sort({ 'recommendation.score': -1, createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .lean();
};

module.exports = mongoose.model('Wish', wishSchema);
