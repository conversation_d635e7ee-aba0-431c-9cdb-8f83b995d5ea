/**
 * 心愿模型
 * 定义心愿数据结构和相关方法
 */

const { DataTypes, Model } = require('sequelize');
const { getSequelize } = require('../database/connection');

class Wish extends Model {
  // 实例方法
  async addView() {
    await this.increment('views');
  }

  async toggleLike(userId) {
    const sequelize = getSequelize();
    const { Op } = require('sequelize');
    
    // 检查是否已点赞
    const existingLike = await sequelize.models.WishLike.findOne({
      where: {
        wish_id: this.id,
        user_id: userId
      }
    });
    
    if (existingLike) {
      // 取消点赞
      await existingLike.destroy();
      await this.decrement('likes');
      return { action: 'unliked', count: this.likes - 1 };
    } else {
      // 添加点赞
      await sequelize.models.WishLike.create({
        wish_id: this.id,
        user_id: userId
      });
      await this.increment('likes');
      return { action: 'liked', count: this.likes + 1 };
    }
  }

  // 虚拟属性
  get isExpired() {
    return this.deadline && this.deadline < new Date();
  }

  get daysLeft() {
    if (!this.deadline) return null;
    const now = new Date();
    const diff = this.deadline.getTime() - now.getTime();
    return Math.ceil(diff / (1000 * 60 * 60 * 24));
  }

  // 静态方法
  static async getPublicWishes(options = {}) {
    const {
      page = 1,
      limit = 20,
      type,
      category,
      sortBy = 'created_at',
      sortOrder = -1
    } = options;
    
    const { Op } = require('sequelize');
    const where = {
      status: 'active',
      visibility: 'public'
    };
    
    if (type) where.type = type;
    if (category) where.category = category;
    
    const order = [[sortBy, sortOrder === -1 ? 'DESC' : 'ASC']];
    
    return this.findAll({
      where,
      order,
      limit,
      offset: (page - 1) * limit,
      include: [{
        model: this.sequelize.models.User,
        as: 'creator',
        attributes: ['id', 'username', 'nickname', 'avatar']
      }]
    });
  }

  static async getRecommendedWishes(userId, limit = 20) {
    const { Op } = require('sequelize');
    
    return this.findAll({
      where: {
        status: 'active',
        visibility: 'public',
        creator_id: { [Op.ne]: userId }
      },
      order: [['created_at', 'DESC']],
      limit,
      include: [{
        model: this.sequelize.models.User,
        as: 'creator',
        attributes: ['id', 'username', 'nickname', 'avatar']
      }]
    });
  }

  static async searchWishes(keyword, options = {}) {
    const {
      page = 1,
      limit = 20,
      type,
      category
    } = options;
    
    const { Op } = require('sequelize');
    const where = {
      status: 'active',
      visibility: 'public',
      [Op.or]: [
        { title: { [Op.like]: `%${keyword}%` } },
        { content: { [Op.like]: `%${keyword}%` } }
      ]
    };
    
    if (type) where.type = type;
    if (category) where.category = category;
    
    return this.findAll({
      where,
      order: [['created_at', 'DESC']],
      limit,
      offset: (page - 1) * limit,
      include: [{
        model: this.sequelize.models.User,
        as: 'creator',
        attributes: ['id', 'username', 'nickname', 'avatar']
      }]
    });
  }
}

// 初始化心愿模型
function initWishModel() {
  const sequelize = getSequelize();
  
  Wish.init({
    // 基本信息
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true
    },
    title: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [1, 100]
      }
    },
    content: {
      type: DataTypes.TEXT,
      allowNull: false,
      validate: {
        len: [1, 1000]
      }
    },
    
    // 心愿创建者
    creator_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    
    // 心愿类型
    type: {
      type: DataTypes.ENUM('health', 'career', 'love', 'family', 'study', 'wealth', 'travel', 'other'),
      allowNull: false
    },
    
    // 心愿分类
    category: {
      type: DataTypes.ENUM('personal', 'family', 'friends', 'society', 'world'),
      defaultValue: 'personal'
    },
    
    // 心愿图片
    images: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    
    // 心愿标签
    tags: {
      type: DataTypes.JSON,
      defaultValue: []
    },
    
    // 心愿状态
    status: {
      type: DataTypes.ENUM('pending', 'active', 'fulfilled', 'expired', 'hidden', 'deleted'),
      defaultValue: 'active'
    },
    
    // 可见性设置
    visibility: {
      type: DataTypes.ENUM('public', 'friends', 'private'),
      defaultValue: 'public'
    },
    
    // 地理位置
    longitude: {
      type: DataTypes.DECIMAL(10, 8),
      defaultValue: 0
    },
    latitude: {
      type: DataTypes.DECIMAL(10, 8),
      defaultValue: 0
    },
    province: {
      type: DataTypes.STRING(50),
      defaultValue: ''
    },
    city: {
      type: DataTypes.STRING(50),
      defaultValue: ''
    },
    district: {
      type: DataTypes.STRING(50),
      defaultValue: ''
    },
    address_detail: {
      type: DataTypes.STRING(200),
      defaultValue: ''
    },
    
    // 心愿期限
    deadline: {
      type: DataTypes.DATE,
      allowNull: true
    },
    
    // 优先级
    priority: {
      type: DataTypes.ENUM('low', 'medium', 'high', 'urgent'),
      defaultValue: 'medium'
    },
    
    // 统计数据
    views: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    likes: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    shares: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    comments: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    blessings: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    
    // 审核信息
    moderation_status: {
      type: DataTypes.ENUM('pending', 'approved', 'rejected', 'flagged'),
      defaultValue: 'approved'
    },
    reviewed_by: {
      type: DataTypes.INTEGER,
      allowNull: true,
      references: {
        model: 'users',
        key: 'id'
      }
    },
    reviewed_at: {
      type: DataTypes.DATE,
      allowNull: true
    },
    review_reason: {
      type: DataTypes.TEXT,
      allowNull: true
    },
    
    // 推荐权重
    recommendation_score: {
      type: DataTypes.DECIMAL(5, 2),
      defaultValue: 0
    },
    recommendation_updated_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    
    // 元数据
    source: {
      type: DataTypes.ENUM('web', 'mobile', 'miniprogram'),
      defaultValue: 'mobile'
    },
    ip: {
      type: DataTypes.STRING(45),
      allowNull: true
    },
    user_agent: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    modelName: 'Wish',
    tableName: 'wishes',
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [
      { fields: ['creator_id', 'created_at'] },
      { fields: ['type', 'status'] },
      { fields: ['status', 'visibility', 'created_at'] },
      { fields: ['likes'] },
      { fields: ['blessings'] },
      { fields: ['recommendation_score'] },
      { fields: ['deadline'] }
    ]
  });
  
  return Wish;
}

module.exports = { Wish, initWishModel };
