<!--
  愿境聆听世愿页面
  守护者聆听和发现需要帮助的心愿
-->
<template>
  <view class="listen-wishes-page">
    <!-- 守护者状态栏 -->
    <view class="guardian-status">
      <view class="status-info">
        <image src="/static/icons/guardian-badge.png" class="guardian-badge" />
        <view class="status-text">
          <text class="status-title">守护者模式</text>
          <text class="status-desc">聆听世间心愿，传递温暖力量</text>
        </view>
      </view>
      
      <view class="guardian-stats">
        <view class="stat-item">
          <text class="stat-value">{{ userStore.userStats.meritPoints }}</text>
          <text class="stat-label">功德值</text>
        </view>
        <view class="stat-divider"></view>
        <view class="stat-item">
          <text class="stat-value">{{ userStore.userStats.totalBlessings }}</text>
          <text class="stat-label">赐福数</text>
        </view>
      </view>
    </view>
    
    <!-- 聆听模式选择 -->
    <view class="listen-modes">
      <view 
        class="mode-item"
        :class="{ 'mode-item--active': listenMode === mode.value }"
        v-for="mode in listenModes"
        :key="mode.value"
        @click="changeListenMode(mode.value)"
      >
        <image :src="mode.icon" class="mode-icon" />
        <text class="mode-text">{{ mode.label }}</text>
      </view>
    </view>
    
    <!-- 心愿发现区域 -->
    <view class="wish-discovery">
      <!-- 随机心愿模式 -->
      <view v-if="listenMode === 'random'" class="random-mode">
        <view class="discovery-card">
          <view class="card-header">
            <image src="/static/icons/random-wish.png" class="header-icon" />
            <text class="header-title">发现心愿</text>
            <text class="header-desc">随机为你推荐一个需要守护的心愿</text>
          </view>
          
          <wish-button
            type="primary"
            size="large"
            :text="randomButtonText"
            :loading="loadingRandom"
            @click="discoverRandomWish"
            class="discover-button"
          />
        </view>
        
        <!-- 当前心愿展示 -->
        <wish-card 
          v-if="currentWish"
          class="current-wish-card"
          shadow="medium"
          hover
          clickable
          @click="goToWishDetail(currentWish.id)"
        >
          <view class="wish-content">
            <view class="wish-header">
              <image 
                :src="currentWish.anonymous ? '/static/default-anonymous.png' : currentWish.user.avatar" 
                class="user-avatar" 
              />
              <view class="user-info">
                <text class="user-nickname">
                  {{ currentWish.anonymous ? '匿名祈愿者' : currentWish.user.nickname }}
                </text>
                <text class="wish-time">{{ formatTime(currentWish.createdAt) }}</text>
              </view>
              <view class="wish-type">
                <image :src="getTypeIcon(currentWish.type)" class="type-icon" />
              </view>
            </view>
            
            <text class="wish-title">{{ currentWish.title }}</text>
            <text class="wish-desc">{{ currentWish.content }}</text>
            
            <view class="wish-tags">
              <text 
                v-for="tag in currentWish.tags" 
                :key="tag"
                class="wish-tag"
              >
                #{{ tag }}
              </text>
            </view>
            
            <view class="wish-actions">
              <wish-button
                type="primary"
                text="赐福此愿"
                @click.stop="blessWish(currentWish.id)"
                class="bless-button"
              />
              <wish-button
                type="ghost"
                text="换一个"
                @click.stop="discoverRandomWish"
                class="next-button"
              />
            </view>
          </view>
        </wish-card>
      </view>
      
      <!-- 筛选心愿模式 -->
      <view v-else class="filter-mode">
        <!-- 筛选条件 -->
        <view class="filter-section">
          <text class="section-title">选择你想守护的心愿类型</text>
          
          <view class="filter-options">
            <view 
              class="filter-option"
              :class="{ 'filter-option--selected': wishFilters.type === type.value }"
              v-for="type in wishTypes"
              :key="type.value"
              @click="selectWishType(type.value)"
            >
              <image :src="type.icon" class="option-icon" />
              <text class="option-text">{{ type.name }}</text>
            </view>
          </view>
        </view>
        
        <view class="filter-section">
          <text class="section-title">选择标签</text>
          
          <view class="tag-options">
            <view 
              class="tag-option"
              :class="{ 'tag-option--selected': wishFilters.tags.includes(tag) }"
              v-for="tag in availableTags"
              :key="tag"
              @click="toggleTag(tag)"
            >
              <text class="tag-text">{{ tag }}</text>
            </view>
          </view>
        </view>
        
        <wish-button
          type="primary"
          size="large"
          text="开始聆听"
          @click="startListening"
          class="listen-button"
        />
      </view>
    </view>
    
    <!-- 心愿列表 -->
    <scroll-view 
      v-if="listenMode === 'filter' && filteredWishes.length > 0"
      class="wish-list"
      scroll-y
      @scrolltolower="loadMore"
    >
      <view class="wish-items">
        <wish-card
          v-for="wish in filteredWishes"
          :key="wish.id"
          class="wish-item"
          shadow="light"
          hover
          clickable
          @click="goToWishDetail(wish.id)"
        >
          <view class="wish-content">
            <view class="wish-header">
              <image 
                :src="wish.anonymous ? '/static/default-anonymous.png' : wish.user.avatar" 
                class="user-avatar" 
              />
              <view class="user-info">
                <text class="user-nickname">
                  {{ wish.anonymous ? '匿名祈愿者' : wish.user.nickname }}
                </text>
                <text class="wish-time">{{ formatTime(wish.createdAt) }}</text>
              </view>
            </view>
            
            <text class="wish-title">{{ wish.title }}</text>
            <text class="wish-desc">{{ wish.content }}</text>
            
            <view class="wish-footer">
              <view class="wish-tags">
                <text 
                  v-for="tag in wish.tags" 
                  :key="tag"
                  class="wish-tag"
                >
                  #{{ tag }}
                </text>
              </view>
              
              <wish-button
                type="primary"
                size="small"
                text="赐福"
                @click.stop="blessWish(wish.id)"
                class="quick-bless-button"
              />
            </view>
          </view>
        </wish-card>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <wish-loading v-if="loading" type="dots" text="加载中..." />
      </view>
    </scroll-view>
    
    <!-- 空状态 -->
    <view class="empty-state" v-if="listenMode === 'filter' && !loading && filteredWishes.length === 0">
      <image src="/static/icons/empty-listen.png" class="empty-icon" />
      <text class="empty-text">暂时没有符合条件的心愿</text>
      <text class="empty-desc">试试调整筛选条件或切换到随机模式</text>
    </view>
  </view>
</template>

<script>
import { useUserStore, useWishStore, useGuardianStore, useSystemStore } from '@/store'
import { timeUtils, navigation, toast } from '@/utils'
import { CONSTANTS } from '@/config'

export default {
  data() {
    return {
      listenMode: 'random', // random, filter
      currentWish: null,
      loadingRandom: false,
      loading: false,
      wishFilters: {
        type: '',
        tags: []
      },
      filteredWishes: [],
      listenModes: [
        {
          value: 'random',
          label: '随机发现',
          icon: '/static/icons/random-mode.png'
        },
        {
          value: 'filter',
          label: '精准聆听',
          icon: '/static/icons/filter-mode.png'
        }
      ],
      wishTypes: [
        {
          value: '',
          name: '全部',
          icon: '/static/icons/all-wish.png'
        },
        {
          value: 'personal',
          name: '个人',
          icon: '/static/icons/personal-wish.png'
        },
        {
          value: 'family',
          name: '家庭',
          icon: '/static/icons/family-wish.png'
        },
        {
          value: 'career',
          name: '事业',
          icon: '/static/icons/career-wish.png'
        },
        {
          value: 'health',
          name: '健康',
          icon: '/static/icons/health-wish.png'
        },
        {
          value: 'other',
          name: '其他',
          icon: '/static/icons/other-wish.png'
        }
      ],
      availableTags: []
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    wishStore() {
      return useWishStore()
    },
    
    guardianStore() {
      return useGuardianStore()
    },
    
    systemStore() {
      return useSystemStore()
    },
    
    hasMore() {
      return this.wishStore.pagination.hasMore
    },
    
    randomButtonText() {
      return this.currentWish ? '换一个心愿' : '发现心愿'
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    // 确保用户是守护者身份
    if (!this.userStore.isGuardian) {
      this.userStore.switchRole('guardian')
    }
    this.refreshData()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      try {
        // 获取可用标签
        const tags = await this.systemStore.fetchTags()
        this.availableTags = tags || CONSTANTS.WISH_TAGS
        
        // 如果是随机模式，自动发现一个心愿
        if (this.listenMode === 'random') {
          await this.discoverRandomWish()
        }
      } catch (error) {
        console.error('页面初始化失败:', error)
      }
    },
    
    /**
     * 刷新数据
     */
    async refreshData() {
      try {
        await this.userStore.fetchUserStats()
      } catch (error) {
        console.error('刷新数据失败:', error)
      }
    },
    
    /**
     * 改变聆听模式
     */
    changeListenMode(mode) {
      this.listenMode = mode
      
      if (mode === 'random' && !this.currentWish) {
        this.discoverRandomWish()
      } else if (mode === 'filter') {
        this.filteredWishes = []
      }
    },
    
    /**
     * 发现随机心愿
     */
    async discoverRandomWish() {
      this.loadingRandom = true
      try {
        const wish = await this.wishStore.getRandomWish()
        this.currentWish = wish
        
        if (!wish) {
          toast.info('暂时没有需要守护的心愿')
        }
      } catch (error) {
        console.error('发现心愿失败:', error)
        toast.error('发现心愿失败，请重试')
      } finally {
        this.loadingRandom = false
      }
    },
    
    /**
     * 选择心愿类型
     */
    selectWishType(type) {
      this.wishFilters.type = this.wishFilters.type === type ? '' : type
    },
    
    /**
     * 切换标签选择
     */
    toggleTag(tag) {
      const index = this.wishFilters.tags.indexOf(tag)
      if (index > -1) {
        this.wishFilters.tags.splice(index, 1)
      } else {
        if (this.wishFilters.tags.length >= 3) {
          toast.error('最多只能选择3个标签')
          return
        }
        this.wishFilters.tags.push(tag)
      }
    },
    
    /**
     * 开始聆听
     */
    async startListening() {
      this.loading = true
      try {
        const params = {
          type: this.wishFilters.type,
          tags: this.wishFilters.tags,
          status: 'pending' // 只获取等待守护的心愿
        }
        
        const result = await this.wishStore.fetchWishList(params, true)
        this.filteredWishes = result.list || []
        
        if (this.filteredWishes.length === 0) {
          toast.info('没有找到符合条件的心愿')
        }
      } catch (error) {
        console.error('聆听心愿失败:', error)
        toast.error('聆听失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 加载更多
     */
    async loadMore() {
      if (!this.hasMore || this.loading) return
      
      this.loading = true
      try {
        const params = {
          type: this.wishFilters.type,
          tags: this.wishFilters.tags,
          status: 'pending'
        }
        
        const result = await this.wishStore.fetchWishList(params, false)
        this.filteredWishes.push(...(result.list || []))
      } catch (error) {
        console.error('加载更多失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 赐福心愿
     */
    async blessWish(wishId) {
      navigation.navigateTo('/pages/guardian/bless-wish', { wishId })
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      return timeUtils.relativeTime(timestamp)
    },
    
    /**
     * 获取类型图标
     */
    getTypeIcon(type) {
      const typeConfig = this.wishTypes.find(t => t.value === type)
      return typeConfig?.icon || '/static/icons/other-wish.png'
    },
    
    /**
     * 页面跳转
     */
    goToWishDetail(wishId) {
      navigation.navigateTo('/pages/wisher/wish-detail', { id: wishId })
    }
  }
}
</script>

<style lang="scss" scoped>
.listen-wishes-page {
  min-height: 100vh;
  background: linear-gradient(180deg, $wish-bg-primary 0%, rgba(212, 165, 116, 0.05) 100%);
}

/* 守护者状态栏 */
.guardian-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-lg;
  border-bottom: 2rpx solid $wish-border-light;
  box-shadow: $wish-shadow-sm;
}

.status-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.guardian-badge {
  width: 64rpx;
  height: 64rpx;
  margin-right: $wish-spacing-md;
}

.status-text {
  flex: 1;
}

.status-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-color-secondary;
  margin-bottom: $wish-spacing-xs;
}

.status-desc {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.guardian-stats {
  display: flex;
  align-items: center;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 $wish-spacing-md;
}

.stat-value {
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-color-secondary;
  margin-bottom: $wish-spacing-xs;
}

.stat-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.stat-divider {
  width: 2rpx;
  height: 40rpx;
  background-color: $wish-border-light;
}

/* 聆听模式选择 */
.listen-modes {
  display: flex;
  padding: $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.mode-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-lg;
  margin: 0 $wish-spacing-xs;
  transition: all 0.3s ease;

  &--active {
    background-color: rgba(212, 165, 116, 0.1);

    .mode-text {
      color: $wish-color-secondary;
      font-weight: 600;
    }
  }
}

.mode-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: $wish-spacing-sm;
}

.mode-text {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  transition: all 0.3s ease;
}

/* 心愿发现区域 */
.wish-discovery {
  padding: $wish-spacing-md;
}

/* 随机模式 */
.random-mode {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-lg;
}

.discovery-card {
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-xl;
  text-align: center;
  box-shadow: $wish-shadow-sm;
}

.card-header {
  margin-bottom: $wish-spacing-xl;
}

.header-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: $wish-spacing-md;
}

.header-title {
  display: block;
  font-size: $wish-font-xl;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
}

.header-desc {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
}

.discover-button {
  width: 100%;
}

.current-wish-card {
  margin: 0;
}

.wish-content {
  padding: 0;
}

.wish-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-sm;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
}

.user-info {
  flex: 1;
}

.user-nickname {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: 4rpx;
}

.wish-time {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.wish-type {
  width: 32rpx;
  height: 32rpx;
}

.type-icon {
  width: 100%;
  height: 100%;
}

.wish-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
  line-height: 1.4;
}

.wish-desc {
  display: block;
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-sm;
}

.wish-tags {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-xs;
  margin-bottom: $wish-spacing-md;
}

.wish-tag {
  font-size: $wish-font-xs;
  color: $wish-color-secondary;
  background-color: rgba(212, 165, 116, 0.1);
  padding: 4rpx 8rpx;
  border-radius: $wish-radius-sm;
}

.wish-actions {
  display: flex;
  gap: $wish-spacing-md;
}

.bless-button {
  flex: 2;
}

.next-button {
  flex: 1;
}

/* 筛选模式 */
.filter-mode {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-xl;
}

.filter-section {
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-lg;
  box-shadow: $wish-shadow-sm;
}

.section-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-md;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-sm;
}

.filter-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $wish-spacing-md;
  border: 2rpx solid $wish-border-light;
  border-radius: $wish-radius-md;
  background-color: $wish-bg-primary;
  transition: all 0.3s ease;
  min-width: 120rpx;

  &--selected {
    border-color: $wish-color-secondary;
    background-color: rgba(212, 165, 116, 0.1);
  }
}

.option-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: $wish-spacing-xs;
}

.option-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.tag-options {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-sm;
}

.tag-option {
  padding: $wish-spacing-xs $wish-spacing-sm;
  border: 2rpx solid $wish-border-medium;
  border-radius: $wish-radius-md;
  transition: all 0.3s ease;

  &--selected {
    border-color: $wish-color-secondary;
    background-color: rgba(212, 165, 116, 0.1);

    .tag-text {
      color: $wish-color-secondary;
    }
  }
}

.tag-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

.listen-button {
  width: 100%;
}

/* 心愿列表 */
.wish-list {
  max-height: 60vh;
  padding: 0 $wish-spacing-md;
}

.wish-items {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.wish-item {
  margin: 0;
}

.wish-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: $wish-spacing-sm;
}

.quick-bless-button {
  min-height: auto;
  padding: $wish-spacing-xs $wish-spacing-sm;
}

/* 加载状态 */
.load-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $wish-spacing-xxl;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: $wish-spacing-lg;
  opacity: 0.6;
}

.empty-text {
  font-size: $wish-font-lg;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.empty-desc {
  font-size: $wish-font-md;
  color: $wish-text-disabled;
}
</style>
