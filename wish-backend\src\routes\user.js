/**
 * 用户路由
 * 处理用户相关的API请求
 */

const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const bcrypt = require('bcryptjs');
const { getModel } = require('../models');
const authMiddleware = require('../middleware/auth');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { Cache } = require('../utils/redis');
const logger = require('../utils/logger');
const config = require('../config');

const router = express.Router();

/**
 * 获取用户资料
 */
router.get('/:id', [
  param('id').isMongoId().withMessage('无效的用户ID')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const { id } = req.params;
  
  // 尝试从缓存获取
  const cacheKey = `user:profile:${id}`;
  let user = await Cache.get(cacheKey);

  if (!user) {
    const User = getModel('User');
    user = await User.findByPk(id, {
      attributes: { exclude: ['password', 'email_verification_token', 'phone_verification_code'] }
    });

    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }

    // 缓存用户信息
    await Cache.set(cacheKey, user, config.cache.ttl.user);
  }

  // 检查隐私设置
  if (!user.profile_visible &&
      (!req.user || req.user.userId.toString() !== id)) {
    throw new AppError('用户资料不公开', 403, 'PROFILE_PRIVATE');
  }

  res.json({
    user
  });
}));

/**
 * 更新用户资料
 */
router.put('/profile', authMiddleware.required, [
  body('profile.nickname').optional().trim().isLength({ min: 1, max: 50 }).withMessage('昵称长度必须在1-50字符之间'),
  body('profile.bio').optional().trim().isLength({ max: 200 }).withMessage('个人简介不能超过200字符'),
  body('profile.gender').optional().isIn(['male', 'female', 'other', 'private']),
  body('profile.birthday').optional().isISO8601().withMessage('生日格式无效'),
  body('profile.location.province').optional().trim().isLength({ max: 50 }),
  body('profile.location.city').optional().trim().isLength({ max: 50 }),
  body('profile.location.district').optional().trim().isLength({ max: 50 })
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const { profile, preferences } = req.body;
  const userId = req.user.userId;

  const user = await User.findById(userId);
  
  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  // 更新资料
  if (profile) {
    Object.assign(user.profile, profile);
  }
  
  if (preferences) {
    Object.assign(user.preferences, preferences);
  }

  await user.save();

  // 清除缓存
  await Cache.del(`user:profile:${userId}`);

  logger.info(`用户 ${user.username} 更新资料`);

  res.json({
    message: '资料更新成功',
    user: {
      profile: user.profile,
      preferences: user.preferences
    }
  });
}));

/**
 * 修改密码
 */
router.put('/password', authMiddleware.required, [
  body('currentPassword').notEmpty().withMessage('当前密码不能为空'),
  body('newPassword').isLength({ min: 8 }).matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/).withMessage('新密码至少8位，包含大小写字母和数字')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const { currentPassword, newPassword } = req.body;
  const userId = req.user.userId;

  const user = await User.findById(userId);
  
  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  // 验证当前密码
  const isCurrentPasswordValid = await user.comparePassword(currentPassword);
  if (!isCurrentPasswordValid) {
    throw new AppError('当前密码错误', 400, 'INVALID_CURRENT_PASSWORD');
  }

  // 检查新密码是否与当前密码相同
  const isSamePassword = await user.comparePassword(newPassword);
  if (isSamePassword) {
    throw new AppError('新密码不能与当前密码相同', 400, 'SAME_PASSWORD');
  }

  // 更新密码
  user.password = newPassword;
  await user.save();

  logger.info(`用户 ${user.username} 修改密码`);

  res.json({
    message: '密码修改成功'
  });
}));

/**
 * 获取用户心愿列表
 */
router.get('/:id/wishes', [
  param('id').isMongoId().withMessage('无效的用户ID'),
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['active', 'fulfilled', 'expired'])
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const { id } = req.params;
  const { page = 1, limit = 20, status = 'active' } = req.query;

  // 检查用户是否存在
  const user = await User.findById(id);
  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  // 检查心愿可见性权限
  const isOwner = req.user && req.user.userId.toString() === id;
  const isAdmin = req.user && req.user.roles.includes('admin');
  
  if (!user.preferences.privacy.wishesVisible && !isOwner && !isAdmin) {
    throw new AppError('用户心愿不公开', 403, 'WISHES_PRIVATE');
  }

  const query = {
    creator: id,
    status
  };

  // 非所有者只能看到公开的心愿
  if (!isOwner && !isAdmin) {
    query.visibility = 'public';
  }

  const wishes = await Wish.find(query)
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(parseInt(limit))
    .populate('creator', 'username profile.nickname profile.avatar')
    .lean();

  const total = await Wish.countDocuments(query);

  res.json({
    wishes,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total,
      pages: Math.ceil(total / limit)
    }
  });
}));

/**
 * 获取用户统计信息
 */
router.get('/:id/stats', [
  param('id').isMongoId().withMessage('无效的用户ID')
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const { id } = req.params;

  const cacheKey = `user:stats:${id}`;
  let stats = await Cache.get(cacheKey);

  if (!stats) {
    const user = await User.findById(id).select('stats points level');
    
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }

    // 获取详细统计
    const [activeWishes, fulfilledWishes, totalBlessings] = await Promise.all([
      Wish.countDocuments({ creator: id, status: 'active' }),
      Wish.countDocuments({ creator: id, status: 'fulfilled' }),
      Wish.aggregate([
        { $match: { creator: id } },
        { $group: { _id: null, total: { $sum: '$stats.blessings' } } }
      ])
    ]);

    stats = {
      ...user.stats.toObject(),
      points: user.points,
      level: user.level,
      activeWishes,
      fulfilledWishes,
      totalBlessingsReceived: totalBlessings[0]?.total || 0
    };

    // 缓存统计信息
    await Cache.set(cacheKey, stats, 300); // 5分钟缓存
  }

  res.json({
    stats
  });
}));

/**
 * 每日签到
 */
router.post('/checkin', authMiddleware.required, catchAsync(async (req, res) => {
  const userId = req.user.userId;
  const today = new Date().toDateString();
  
  // 检查今日是否已签到
  const checkinKey = `user:checkin:${userId}:${today}`;
  const hasCheckedIn = await Cache.exists(checkinKey);
  
  if (hasCheckedIn) {
    throw new AppError('今日已签到', 400, 'ALREADY_CHECKED_IN');
  }

  const user = await User.findById(userId);
  
  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  // 检查连续签到
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const yesterdayKey = `user:checkin:${userId}:${yesterday.toDateString()}`;
  const checkedInYesterday = await Cache.exists(yesterdayKey);

  if (checkedInYesterday) {
    user.stats.consecutiveDays += 1;
  } else {
    user.stats.consecutiveDays = 1;
  }

  // 计算奖励
  let wishPowerReward = config.business.wishPower.dailyRitual;
  
  // 连续签到奖励
  if (user.stats.consecutiveDays >= 7) {
    wishPowerReward *= 2; // 连续7天双倍奖励
  }

  // 更新用户数据
  await user.addPoints('wishPower', wishPowerReward);
  user.stats.lastActiveDate = new Date();
  await user.save();

  // 记录签到
  await Cache.set(checkinKey, true, 24 * 60 * 60); // 24小时

  // 清除统计缓存
  await Cache.del(`user:stats:${userId}`);

  logger.info(`用户 ${user.username} 签到，连续${user.stats.consecutiveDays}天`);

  res.json({
    message: '签到成功',
    reward: {
      wishPower: wishPowerReward
    },
    consecutiveDays: user.stats.consecutiveDays,
    totalWishPower: user.points.wishPower
  });
}));

/**
 * 获取排行榜
 */
router.get('/leaderboard/:type', [
  param('type').isIn(['merit', 'wishPower']).withMessage('无效的排行榜类型'),
  query('limit').optional().isInt({ min: 1, max: 100 })
], catchAsync(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new AppError('参数验证失败', 400, 'VALIDATION_ERROR');
  }

  const { type } = req.params;
  const { limit = 50 } = req.query;

  const cacheKey = `leaderboard:${type}:${limit}`;
  let leaderboard = await Cache.get(cacheKey);

  if (!leaderboard) {
    leaderboard = await User.getLeaderboard(type, parseInt(limit));
    await Cache.set(cacheKey, leaderboard, config.cache.ttl.leaderboard);
  }

  // 添加排名
  const rankedLeaderboard = leaderboard.map((user, index) => ({
    ...user,
    rank: index + 1
  }));

  res.json({
    leaderboard: rankedLeaderboard,
    type
  });
}));

module.exports = router;
