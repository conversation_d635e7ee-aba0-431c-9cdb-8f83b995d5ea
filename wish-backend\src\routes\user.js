/**
 * 用户路由
 * 处理用户相关的API请求
 */

const express = require('express');
const { body, query, param, validationResult } = require('express-validator');
const { getModel } = require('../models');
const authMiddleware = require('../middleware/auth');
const { AppError, catchAsync, handleValidationErrors } = require('../middleware/errorHandler');
const { Cache } = require('../utils/redis');
const logger = require('../utils/logger');
const config = require('../config');

const router = express.Router();

/**
 * 用户模块根路由
 */
router.get('/', (req, res) => {
  res.json({
    module: '用户模块',
    endpoints: {
      profile: 'GET /api/users/:id',
      updateProfile: 'PUT /api/users/profile',
      changePassword: 'PUT /api/users/password',
      userWishes: 'GET /api/users/:id/wishes',
      userStats: 'GET /api/users/:id/stats',
      checkinStatus: 'GET /api/users/checkin-status',
      checkin: 'POST /api/users/checkin',
      leaderboard: 'GET /api/users/leaderboard/:type'
    }
  });
});

/**
 * 获取用户资料
 */
router.get('/:id', [
  param('id').isInt().withMessage('用户ID必须是整数')
], handleValidationErrors, authMiddleware.optional, catchAsync(async (req, res) => {
  const { id } = req.params;

  // 尝试从缓存获取
  const cacheKey = `user:profile:${id}`;
  let user = await Cache.get(cacheKey);
  
  if (!user) {
    const User = getModel('User');
    user = await User.findByPk(id, {
      attributes: { exclude: ['password', 'email_verification_token', 'phone_verification_code'] }
    });
    
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }
    
    // 缓存用户信息
    await Cache.set(cacheKey, user, config.cache.ttl.user);
  }

  // 检查隐私设置
  if (!user.profile_visible && 
      (!req.user || req.user.userId.toString() !== id)) {
    throw new AppError('用户资料不公开', 403, 'PROFILE_PRIVATE');
  }

  res.json({
    user
  });
}));

/**
 * 更新用户资料
 */
router.put('/profile', [
  body('nickname').optional().isLength({ min: 1, max: 50 }).withMessage('昵称长度1-50字符'),
  body('bio').optional().isLength({ max: 200 }).withMessage('个人简介最多200字符'),
  body('gender').optional().isIn(['male', 'female', 'other', 'private']),
  body('birthday').optional().isISO8601().withMessage('生日格式无效'),
  body('location.province').optional().isLength({ max: 50 }),
  body('location.city').optional().isLength({ max: 50 }),
  body('location.district').optional().isLength({ max: 50 }),
  body('preferences.privacy.profileVisible').optional().isBoolean(),
  body('preferences.privacy.wishesVisible').optional().isBoolean(),
  body('preferences.privacy.allowMessages').optional().isBoolean(),
  body('preferences.notifications.email').optional().isBoolean(),
  body('preferences.notifications.push').optional().isBoolean(),
  body('preferences.notifications.sms').optional().isBoolean()
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { profile, preferences } = req.body;
  const userId = req.user.userId;
  const User = getModel('User');

  const user = await User.findByPk(userId);
  
  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  // 更新资料
  const updates = {};
  if (profile) {
    if (profile.nickname) updates.nickname = profile.nickname;
    if (profile.bio) updates.bio = profile.bio;
    if (profile.gender) updates.gender = profile.gender;
    if (profile.birthday) updates.birthday = profile.birthday;
    if (profile.location) {
      if (profile.location.province) updates.province = profile.location.province;
      if (profile.location.city) updates.city = profile.location.city;
      if (profile.location.district) updates.district = profile.location.district;
    }
  }
  
  if (preferences) {
    if (preferences.privacy) {
      if (preferences.privacy.profileVisible !== undefined) updates.profile_visible = preferences.privacy.profileVisible;
      if (preferences.privacy.wishesVisible !== undefined) updates.wishes_visible = preferences.privacy.wishesVisible;
      if (preferences.privacy.allowMessages !== undefined) updates.allow_messages = preferences.privacy.allowMessages;
    }
    if (preferences.notifications) {
      if (preferences.notifications.email !== undefined) updates.email_notifications = preferences.notifications.email;
      if (preferences.notifications.push !== undefined) updates.push_notifications = preferences.notifications.push;
      if (preferences.notifications.sms !== undefined) updates.sms_notifications = preferences.notifications.sms;
    }
  }

  await user.update(updates);

  // 清除缓存
  await Cache.del(`user:profile:${userId}`);

  logger.info(`用户 ${user.username} 更新资料`);

  res.json({
    message: '资料更新成功',
    user: await User.findByPk(userId, {
      attributes: { exclude: ['password', 'email_verification_token', 'phone_verification_code'] }
    })
  });
}));

/**
 * 修改密码
 */
router.put('/password', [
  body('currentPassword').notEmpty().withMessage('请提供当前密码'),
  body('newPassword')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('新密码至少8位，包含大小写字母和数字')
], handleValidationErrors, authMiddleware.required, catchAsync(async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user.userId;
  const User = getModel('User');

  const user = await User.findByPk(userId);
  
  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  // 验证当前密码
  const isCurrentPasswordValid = await user.comparePassword(currentPassword);
  if (!isCurrentPasswordValid) {
    throw new AppError('当前密码错误', 400, 'INVALID_CURRENT_PASSWORD');
  }

  // 检查新密码是否与当前密码相同
  const isSamePassword = await user.comparePassword(newPassword);
  if (isSamePassword) {
    throw new AppError('新密码不能与当前密码相同', 400, 'SAME_PASSWORD');
  }

  // 更新密码
  await user.update({ password: newPassword });

  logger.info(`用户 ${user.username} 修改密码`);

  res.json({
    message: '密码修改成功'
  });
}));

/**
 * 获取用户心愿列表
 */
router.get('/:id/wishes', [
  param('id').isInt().withMessage('用户ID必须是整数'),
  query('page').optional().isInt({ min: 1 }).withMessage('页码必须是正整数'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('每页数量必须在1-100之间'),
  query('status').optional().isIn(['active', 'fulfilled', 'expired', 'hidden'])
], handleValidationErrors, authMiddleware.optional, catchAsync(async (req, res) => {
  const { id } = req.params;
  const { page = 1, limit = 20, status = 'active' } = req.query;
  const User = getModel('User');
  const Wish = getModel('Wish');

  // 检查用户是否存在
  const user = await User.findByPk(id);
  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  // 检查心愿可见性权限
  const isOwner = req.user && req.user.userId.toString() === id;
  const isAdmin = req.user && req.user.roles.includes('admin');
  
  if (!user.wishes_visible && !isOwner && !isAdmin) {
    throw new AppError('用户心愿不公开', 403, 'WISHES_PRIVATE');
  }

  const where = {
    creator_id: id,
    status
  };

  // 非所有者只能看到公开的心愿
  if (!isOwner && !isAdmin) {
    where.visibility = 'public';
  }

  const result = await Wish.findAndCountAll({
    where,
    order: [['created_at', 'DESC']],
    offset: (page - 1) * limit,
    limit: parseInt(limit),
    include: [{
      model: User,
      as: 'creator',
      attributes: ['id', 'username', 'nickname', 'avatar']
    }]
  });

  res.json({
    wishes: result.rows,
    pagination: {
      page: parseInt(page),
      limit: parseInt(limit),
      total: result.count,
      pages: Math.ceil(result.count / limit)
    }
  });
}));

/**
 * 获取用户统计信息
 */
router.get('/:id/stats', [
  param('id').isInt().withMessage('用户ID必须是整数')
], handleValidationErrors, authMiddleware.optional, catchAsync(async (req, res) => {
  const { id } = req.params;
  const cacheKey = `user:stats:${id}`;

  let stats = await Cache.get(cacheKey);

  if (!stats) {
    const User = getModel('User');
    const Wish = getModel('Wish');
    const user = await User.findByPk(id, {
      attributes: ['id', 'wishes_created', 'wishes_blessed', 'blessings_received', 
                  'wish_power_points', 'merit_points', 'level']
    });
    
    if (!user) {
      throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
    }

    // 获取详细统计
    const [activeWishes, fulfilledWishes] = await Promise.all([
      Wish.count({ where: { creator_id: id, status: 'active' } }),
      Wish.count({ where: { creator_id: id, status: 'fulfilled' } })
    ]);

    stats = {
      wishesCreated: user.wishes_created,
      wishesBlessedByUser: user.wishes_blessed,
      blessingsReceived: user.blessings_received,
      wishPowerPoints: user.wish_power_points,
      meritPoints: user.merit_points,
      level: user.level,
      activeWishes,
      fulfilledWishes
    };

    // 缓存统计信息
    await Cache.set(cacheKey, stats, 300); // 5分钟缓存
  }

  res.json({
    stats
  });
}));

/**
 * 获取签到状态
 */
router.get('/checkin-status', authMiddleware.required, catchAsync(async (req, res) => {
  const userId = req.user.userId;
  const today = new Date().toDateString();
  const checkinKey = `user:checkin:${userId}:${today}`;

  // 检查今日是否已签到
  const hasCheckedIn = await Cache.exists(checkinKey);

  const User = getModel('User');
  const user = await User.findByPk(userId);

  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  // 计算下次签到奖励
  let nextReward = config.business.wishPower.dailyRitual;
  const consecutiveDays = user.consecutive_days || 0;

  // 如果连续签到达到7天，下次奖励翻倍
  if (consecutiveDays >= 6) {
    nextReward *= 2;
  }

  res.json({
    isCheckedIn: hasCheckedIn,
    consecutiveDays: consecutiveDays,
    lastCheckInDate: user.last_active_date ? user.last_active_date.toDateString() : null,
    nextReward: nextReward,
    totalWishPower: user.wish_power_points
  });
}));

/**
 * 每日签到
 */
router.post('/checkin', authMiddleware.required, catchAsync(async (req, res) => {
  const userId = req.user.userId;
  const today = new Date().toDateString();
  const checkinKey = `user:checkin:${userId}:${today}`;

  // 检查今日是否已签到
  const hasCheckedIn = await Cache.exists(checkinKey);
  if (hasCheckedIn) {
    throw new AppError('今日已签到', 400, 'ALREADY_CHECKED_IN');
  }

  const User = getModel('User');
  const user = await User.findByPk(userId);

  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  // 检查连续签到
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  const yesterdayKey = `user:checkin:${userId}:${yesterday.toDateString()}`;
  const checkedInYesterday = await Cache.exists(yesterdayKey);

  let consecutiveDays = user.consecutive_days || 0;
  if (checkedInYesterday) {
    consecutiveDays += 1;
  } else {
    consecutiveDays = 1;
  }

  // 计算奖励
  let wishPowerReward = config.business.wishPower.dailyRitual;

  // 连续签到奖励
  if (consecutiveDays >= 7) {
    wishPowerReward *= 2; // 连续7天双倍奖励
  }

  // 更新用户数据
  await user.addPoints('wishPower', wishPowerReward);
  await user.update({
    consecutive_days: consecutiveDays,
    last_active_date: new Date()
  });

  // 记录签到
  await Cache.set(checkinKey, true, 24 * 60 * 60); // 24小时

  logger.info(`用户 ${user.username} 签到，连续${consecutiveDays}天`);

  res.json({
    message: '签到成功',
    reward: {
      wishPower: wishPowerReward
    },
    consecutiveDays: consecutiveDays,
    totalWishPower: user.wish_power_points + wishPowerReward
  });
}));

/**
 * 获取排行榜
 */
router.get('/leaderboard/:type', [
  param('type').isIn(['merit', 'wishPower']).withMessage('排行榜类型无效'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('数量限制必须在1-100之间')
], handleValidationErrors, catchAsync(async (req, res) => {
  const { type } = req.params;
  const { limit = 50 } = req.query;
  
  const cacheKey = `leaderboard:${type}:${limit}`;
  let leaderboard = await Cache.get(cacheKey);

  if (!leaderboard) {
    const User = getModel('User');
    leaderboard = await User.getLeaderboard(type, parseInt(limit));
    await Cache.set(cacheKey, leaderboard, config.cache.ttl.leaderboard);
  }

  res.json({
    leaderboard,
    type,
    updatedAt: new Date().toISOString()
  });
}));

module.exports = router;
