/**
 * Redis工具
 * 提供Redis连接和缓存功能（可选）
 */

const config = require('../config');
const logger = require('./logger');

let client = null;
let isRedisEnabled = config.redis.enabled;

/**
 * 连接Redis
 */
async function connectRedis() {
  if (!isRedisEnabled) {
    logger.info('Redis未启用，跳过连接');
    return null;
  }

  try {
    const redis = require('redis');

    client = redis.createClient({
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      db: config.redis.db,
      keyPrefix: config.redis.keyPrefix,
      retryDelayOnFailover: config.redis.retryDelayOnFailover,
      maxRetriesPerRequest: config.redis.maxRetriesPerRequest
    });

    client.on('error', (error) => {
      logger.error('Redis连接错误:', error);
    });

    client.on('connect', () => {
      logger.info('Redis连接成功');
    });

    client.on('ready', () => {
      logger.info('Redis准备就绪');
    });

    client.on('end', () => {
      logger.warn('Redis连接断开');
    });

    await client.connect();
    return client;

  } catch (error) {
    logger.error('Redis连接失败:', error);
    // Redis连接失败时，禁用Redis功能
    isRedisEnabled = false;
    logger.warn('Redis功能已禁用，将使用内存缓存');
    return null;
  }
}

/**
 * 断开Redis连接
 */
async function disconnectRedis() {
  if (client) {
    await client.quit();
    client = null;
    logger.info('Redis连接已断开');
  }
}

/**
 * 获取Redis客户端
 */
function getRedisClient() {
  return client;
}

// 内存缓存作为降级方案
const memoryCache = new Map();
const memoryCacheExpiry = new Map();

/**
 * 缓存工具类
 */
class Cache {
  /**
   * 设置缓存
   */
  static async set(key, value, ttl = config.cache.ttl.default) {
    try {
      if (client && isRedisEnabled) {
        const serializedValue = JSON.stringify(value);
        await client.setEx(key, ttl, serializedValue);
        return true;
      } else {
        // 使用内存缓存
        memoryCache.set(key, value);
        memoryCacheExpiry.set(key, Date.now() + ttl * 1000);
        return true;
      }
    } catch (error) {
      logger.error('设置缓存失败:', error);
      // 降级到内存缓存
      memoryCache.set(key, value);
      memoryCacheExpiry.set(key, Date.now() + ttl * 1000);
      return false;
    }
  }

  /**
   * 获取缓存
   */
  static async get(key) {
    try {
      if (client && isRedisEnabled) {
        const value = await client.get(key);
        return value ? JSON.parse(value) : null;
      } else {
        // 使用内存缓存
        const expiry = memoryCacheExpiry.get(key);
        if (expiry && Date.now() > expiry) {
          memoryCache.delete(key);
          memoryCacheExpiry.delete(key);
          return null;
        }
        return memoryCache.get(key) || null;
      }
    } catch (error) {
      logger.error('获取缓存失败:', error);
      // 降级到内存缓存
      const expiry = memoryCacheExpiry.get(key);
      if (expiry && Date.now() > expiry) {
        memoryCache.delete(key);
        memoryCacheExpiry.delete(key);
        return null;
      }
      return memoryCache.get(key) || null;
    }
  }

  /**
   * 删除缓存
   */
  static async del(key) {
    try {
      if (client && isRedisEnabled) {
        await client.del(key);
        return true;
      } else {
        // 使用内存缓存
        memoryCache.delete(key);
        memoryCacheExpiry.delete(key);
        return true;
      }
    } catch (error) {
      logger.error('删除缓存失败:', error);
      // 降级到内存缓存
      memoryCache.delete(key);
      memoryCacheExpiry.delete(key);
      return false;
    }
  }

  /**
   * 检查缓存是否存在
   */
  static async exists(key) {
    try {
      if (!client) return false;
      
      const result = await client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('检查缓存失败:', error);
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   */
  static async expire(key, ttl) {
    try {
      if (!client) return false;
      
      await client.expire(key, ttl);
      return true;
    } catch (error) {
      logger.error('设置缓存过期时间失败:', error);
      return false;
    }
  }

  /**
   * 批量删除缓存
   */
  static async delPattern(pattern) {
    try {
      if (!client) return false;
      
      const keys = await client.keys(pattern);
      if (keys.length > 0) {
        await client.del(keys);
      }
      return true;
    } catch (error) {
      logger.error('批量删除缓存失败:', error);
      return false;
    }
  }

  /**
   * 增加计数器
   */
  static async incr(key, ttl = null) {
    try {
      if (!client) return 0;
      
      const result = await client.incr(key);
      if (ttl && result === 1) {
        await client.expire(key, ttl);
      }
      return result;
    } catch (error) {
      logger.error('增加计数器失败:', error);
      return 0;
    }
  }

  /**
   * 减少计数器
   */
  static async decr(key) {
    try {
      if (!client) return 0;
      
      const result = await client.decr(key);
      return result;
    } catch (error) {
      logger.error('减少计数器失败:', error);
      return 0;
    }
  }
}

/**
 * 缓存装饰器
 */
function cacheDecorator(keyGenerator, ttl = config.cache.ttl.default) {
  return function(target, propertyName, descriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function(...args) {
      const cacheKey = keyGenerator(...args);
      
      // 尝试从缓存获取
      const cachedResult = await Cache.get(cacheKey);
      if (cachedResult !== null) {
        return cachedResult;
      }
      
      // 执行原方法
      const result = await method.apply(this, args);
      
      // 存储到缓存
      await Cache.set(cacheKey, result, ttl);
      
      return result;
    };
    
    return descriptor;
  };
}

module.exports = {
  connectRedis,
  disconnectRedis,
  getRedisClient,
  Cache,
  cacheDecorator
};
