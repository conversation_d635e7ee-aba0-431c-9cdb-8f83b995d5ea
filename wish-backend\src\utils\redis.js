/**
 * Redis工具
 * 提供Redis连接和缓存功能
 */

const redis = require('redis');
const config = require('../config');
const logger = require('./logger');

let client = null;

/**
 * 连接Redis
 */
async function connectRedis() {
  try {
    client = redis.createClient({
      host: config.redis.host,
      port: config.redis.port,
      password: config.redis.password,
      db: config.redis.db,
      keyPrefix: config.redis.keyPrefix,
      retryDelayOnFailover: config.redis.retryDelayOnFailover,
      maxRetriesPerRequest: config.redis.maxRetriesPerRequest
    });

    client.on('error', (error) => {
      logger.error('Redis连接错误:', error);
    });

    client.on('connect', () => {
      logger.info('Redis连接成功');
    });

    client.on('ready', () => {
      logger.info('Redis准备就绪');
    });

    client.on('end', () => {
      logger.warn('Redis连接断开');
    });

    await client.connect();
    return client;

  } catch (error) {
    logger.error('Redis连接失败:', error);
    throw error;
  }
}

/**
 * 断开Redis连接
 */
async function disconnectRedis() {
  if (client) {
    await client.quit();
    client = null;
    logger.info('Redis连接已断开');
  }
}

/**
 * 获取Redis客户端
 */
function getRedisClient() {
  return client;
}

/**
 * 缓存工具类
 */
class Cache {
  /**
   * 设置缓存
   */
  static async set(key, value, ttl = config.cache.ttl.default) {
    try {
      if (!client) return false;
      
      const serializedValue = JSON.stringify(value);
      await client.setEx(key, ttl, serializedValue);
      return true;
    } catch (error) {
      logger.error('设置缓存失败:', error);
      return false;
    }
  }

  /**
   * 获取缓存
   */
  static async get(key) {
    try {
      if (!client) return null;
      
      const value = await client.get(key);
      return value ? JSON.parse(value) : null;
    } catch (error) {
      logger.error('获取缓存失败:', error);
      return null;
    }
  }

  /**
   * 删除缓存
   */
  static async del(key) {
    try {
      if (!client) return false;
      
      await client.del(key);
      return true;
    } catch (error) {
      logger.error('删除缓存失败:', error);
      return false;
    }
  }

  /**
   * 检查缓存是否存在
   */
  static async exists(key) {
    try {
      if (!client) return false;
      
      const result = await client.exists(key);
      return result === 1;
    } catch (error) {
      logger.error('检查缓存失败:', error);
      return false;
    }
  }

  /**
   * 设置缓存过期时间
   */
  static async expire(key, ttl) {
    try {
      if (!client) return false;
      
      await client.expire(key, ttl);
      return true;
    } catch (error) {
      logger.error('设置缓存过期时间失败:', error);
      return false;
    }
  }

  /**
   * 批量删除缓存
   */
  static async delPattern(pattern) {
    try {
      if (!client) return false;
      
      const keys = await client.keys(pattern);
      if (keys.length > 0) {
        await client.del(keys);
      }
      return true;
    } catch (error) {
      logger.error('批量删除缓存失败:', error);
      return false;
    }
  }

  /**
   * 增加计数器
   */
  static async incr(key, ttl = null) {
    try {
      if (!client) return 0;
      
      const result = await client.incr(key);
      if (ttl && result === 1) {
        await client.expire(key, ttl);
      }
      return result;
    } catch (error) {
      logger.error('增加计数器失败:', error);
      return 0;
    }
  }

  /**
   * 减少计数器
   */
  static async decr(key) {
    try {
      if (!client) return 0;
      
      const result = await client.decr(key);
      return result;
    } catch (error) {
      logger.error('减少计数器失败:', error);
      return 0;
    }
  }
}

/**
 * 缓存装饰器
 */
function cacheDecorator(keyGenerator, ttl = config.cache.ttl.default) {
  return function(target, propertyName, descriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function(...args) {
      const cacheKey = keyGenerator(...args);
      
      // 尝试从缓存获取
      const cachedResult = await Cache.get(cacheKey);
      if (cachedResult !== null) {
        return cachedResult;
      }
      
      // 执行原方法
      const result = await method.apply(this, args);
      
      // 存储到缓存
      await Cache.set(cacheKey, result, ttl);
      
      return result;
    };
    
    return descriptor;
  };
}

module.exports = {
  connectRedis,
  disconnectRedis,
  getRedisClient,
  Cache,
  cacheDecorator
};
