# 愿境应用后端API服务

愿境应用的后端API服务，基于Node.js + Express + MongoDB构建，提供完整的用户认证、心愿管理、守护功能、社交互动、安全审核等功能。

## 功能特性

### 🔐 用户认证系统
- 用户注册、登录、登出
- JWT令牌认证
- 邮箱/手机号验证
- 密码安全策略
- 账户锁定机制
- 角色权限管理

### 💫 心愿管理系统
- 心愿创建、编辑、删除
- 心愿分类和标签
- 心愿可见性控制
- 心愿搜索和推荐
- 心愿统计和排序

### 🛡️ 守护功能系统
- 赐福心愿功能
- 功德值计算
- 守护者等级系统
- 赐福历史记录

### 👥 社交互动系统
- 用户关注和粉丝
- 私信聊天功能
- 评论和点赞
- 分享功能
- 排行榜系统

### 🔒 安全审核系统
- 内容安全检测
- 敏感词过滤
- 用户举报处理
- 内容审核管理
- 违规行为记录

### 📊 数据统计系统
- 用户行为统计
- 心愿数据分析
- 系统运营指标
- 实时数据监控

## 技术栈

- **运行环境**: Node.js 16+
- **Web框架**: Express.js
- **数据库**: MongoDB
- **缓存**: Redis
- **认证**: JWT
- **文件上传**: Multer + Sharp
- **日志**: Winston
- **验证**: Joi + express-validator
- **实时通信**: Socket.IO

## 项目结构

```
wish-backend/
├── src/
│   ├── app.js                 # 应用入口文件
│   ├── config/                # 配置文件
│   │   └── index.js
│   ├── models/                # 数据模型
│   │   ├── User.js
│   │   ├── Wish.js
│   │   ├── Comment.js
│   │   └── Report.js
│   ├── routes/                # 路由文件
│   │   ├── auth.js
│   │   ├── user.js
│   │   ├── wish.js
│   │   ├── guardian.js
│   │   ├── social.js
│   │   ├── safety.js
│   │   └── upload.js
│   ├── middleware/            # 中间件
│   │   ├── auth.js
│   │   ├── errorHandler.js
│   │   └── validation.js
│   ├── services/              # 业务服务
│   │   ├── notification.js
│   │   ├── upload.js
│   │   └── moderation.js
│   ├── utils/                 # 工具函数
│   │   ├── logger.js
│   │   ├── redis.js
│   │   ├── crypto.js
│   │   └── helpers.js
│   └── database/              # 数据库相关
│       ├── connection.js
│       ├── migrate.js
│       └── seed.js
├── uploads/                   # 上传文件目录
├── logs/                      # 日志文件目录
├── tests/                     # 测试文件
├── .env.example              # 环境变量示例
├── package.json
└── README.md
```

## 快速开始

### 环境要求

- Node.js 16.0+
- MongoDB 4.4+
- Redis 6.0+
- npm 8.0+

### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd wish-backend

# 安装依赖
npm install
```

### 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量
vim .env
```

### 数据库初始化

```bash
# 运行数据库迁移
npm run migrate

# 初始化种子数据
npm run seed
```

### 启动服务

```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

服务启动后，访问 http://localhost:3000 查看API文档。

## API文档

### 认证相关

#### 用户注册
```http
POST /api/auth/register
Content-Type: application/json

{
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "Password123",
  "nickname": "测试用户"
}
```

#### 用户登录
```http
POST /api/auth/login
Content-Type: application/json

{
  "identifier": "testuser",
  "password": "Password123"
}
```

#### 获取当前用户信息
```http
GET /api/auth/me
Authorization: Bearer <token>
```

### 心愿相关

#### 获取心愿列表
```http
GET /api/wishes?page=1&limit=20&type=health&sortBy=createdAt
```

#### 创建心愿
```http
POST /api/wishes
Authorization: Bearer <token>
Content-Type: application/json

{
  "title": "希望身体健康",
  "content": "希望家人都身体健康，平安快乐",
  "type": "health",
  "category": "family",
  "visibility": "public",
  "tags": ["健康", "家人"]
}
```

#### 获取心愿详情
```http
GET /api/wishes/:id
```

### 用户相关

#### 获取用户资料
```http
GET /api/users/:id
```

#### 更新用户资料
```http
PUT /api/users/profile
Authorization: Bearer <token>
Content-Type: application/json

{
  "profile": {
    "nickname": "新昵称",
    "bio": "个人简介"
  }
}
```

#### 每日签到
```http
POST /api/users/checkin
Authorization: Bearer <token>
```

## 部署指南

### Docker部署

```bash
# 构建镜像
docker build -t wish-backend .

# 运行容器
docker run -d \
  --name wish-backend \
  -p 3000:3000 \
  -e NODE_ENV=production \
  -e MONGODB_URI=mongodb://mongo:27017/wish_app \
  -e REDIS_HOST=redis \
  wish-backend
```

### PM2部署

```bash
# 安装PM2
npm install -g pm2

# 启动应用
pm2 start src/app.js --name wish-backend

# 查看状态
pm2 status

# 查看日志
pm2 logs wish-backend
```

### Nginx配置

```nginx
server {
    listen 80;
    server_name api.wish.example.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 开发指南

### 代码规范

项目使用ESLint进行代码规范检查：

```bash
# 检查代码规范
npm run lint

# 自动修复
npm run lint:fix
```

### 测试

```bash
# 运行所有测试
npm test

# 运行测试并监听文件变化
npm run test:watch

# 生成测试覆盖率报告
npm run test:coverage
```

### 数据库操作

```bash
# 运行迁移
npm run migrate

# 回滚迁移
npm run migrate:rollback

# 重置数据库
npm run migrate:reset

# 初始化种子数据
npm run seed
```

## 监控和日志

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log

# 使用PM2查看日志
pm2 logs wish-backend
```

### 健康检查

```http
GET /health
```

返回服务健康状态和基本信息。

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MongoDB服务是否启动
   - 验证连接字符串是否正确
   - 确认网络连接是否正常

2. **Redis连接失败**
   - 检查Redis服务是否启动
   - 验证Redis配置是否正确
   - 确认防火墙设置

3. **JWT令牌验证失败**
   - 检查JWT_SECRET是否配置
   - 确认令牌格式是否正确
   - 验证令牌是否过期

4. **文件上传失败**
   - 检查上传目录权限
   - 验证文件大小限制
   - 确认文件类型是否支持

## 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目维护者: Wish Team
- 邮箱: <EMAIL>
- 项目地址: https://github.com/wish-team/wish-backend
