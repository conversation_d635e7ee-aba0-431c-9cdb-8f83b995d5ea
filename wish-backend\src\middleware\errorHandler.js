/**
 * 错误处理中间件
 * 统一处理应用中的错误
 */

const logger = require('../utils/logger');
const config = require('../config');

/**
 * 自定义错误类
 */
class AppError extends Error {
  constructor(message, statusCode, code = null) {
    super(message);
    this.statusCode = statusCode;
    this.code = code;
    this.isOperational = true;

    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * 处理MongoDB重复键错误
 */
function handleDuplicateKeyError(error) {
  const field = Object.keys(error.keyValue)[0];
  const value = error.keyValue[field];
  
  const message = `${field} '${value}' 已存在`;
  return new AppError(message, 409, 'DUPLICATE_KEY');
}

/**
 * 处理MongoDB验证错误
 */
function handleValidationError(error) {
  const errors = Object.values(error.errors).map(err => err.message);
  const message = `数据验证失败: ${errors.join(', ')}`;
  return new AppError(message, 400, 'VALIDATION_ERROR');
}

/**
 * 处理MongoDB转换错误
 */
function handleCastError(error) {
  const message = `无效的 ${error.path}: ${error.value}`;
  return new AppError(message, 400, 'INVALID_ID');
}

/**
 * 处理JWT错误
 */
function handleJWTError() {
  return new AppError('无效的令牌', 401, 'INVALID_TOKEN');
}

/**
 * 处理JWT过期错误
 */
function handleJWTExpiredError() {
  return new AppError('令牌已过期', 401, 'TOKEN_EXPIRED');
}

/**
 * 发送开发环境错误响应
 */
function sendErrorDev(err, res) {
  res.status(err.statusCode).json({
    error: err.message,
    code: err.code,
    stack: err.stack,
    details: err
  });
}

/**
 * 发送生产环境错误响应
 */
function sendErrorProd(err, res) {
  // 操作性错误：发送给客户端
  if (err.isOperational) {
    res.status(err.statusCode).json({
      error: err.message,
      code: err.code
    });
  } else {
    // 编程错误：不泄露错误详情
    logger.error('编程错误:', err);
    
    res.status(500).json({
      error: '服务器内部错误',
      code: 'INTERNAL_SERVER_ERROR'
    });
  }
}

/**
 * 全局错误处理中间件
 */
function errorHandler(err, req, res, next) {
  let error = { ...err };
  error.message = err.message;
  error.statusCode = err.statusCode || 500;

  // 记录错误日志
  logger.error('错误详情:', {
    message: err.message,
    stack: err.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.userId
  });

  // 处理特定类型的错误
  if (err.name === 'CastError') {
    error = handleCastError(error);
  }
  
  if (err.code === 11000) {
    error = handleDuplicateKeyError(error);
  }
  
  if (err.name === 'ValidationError') {
    error = handleValidationError(error);
  }
  
  if (err.name === 'JsonWebTokenError') {
    error = handleJWTError();
  }
  
  if (err.name === 'TokenExpiredError') {
    error = handleJWTExpiredError();
  }

  // 发送错误响应
  if (config.env === 'development') {
    sendErrorDev(error, res);
  } else {
    sendErrorProd(error, res);
  }
}

/**
 * 处理未找到的路由
 */
function notFoundHandler(req, res, next) {
  const err = new AppError(`路由 ${req.originalUrl} 不存在`, 404, 'NOT_FOUND');
  next(err);
}

/**
 * 异步错误捕获包装器
 */
function catchAsync(fn) {
  return (req, res, next) => {
    fn(req, res, next).catch(next);
  };
}

module.exports = {
  AppError,
  errorHandler,
  notFoundHandler,
  catchAsync
};
