<!--
  愿境许愿广场页面
  展示所有用户的心愿，支持筛选和搜索
-->
<template>
  <view class="wish-plaza-page">
    <!-- 搜索和筛选栏 -->
    <view class="search-bar">
      <view class="search-input-wrapper">
        <wish-input
          v-model="searchKeyword"
          placeholder="搜索心愿..."
          prefix-icon="/static/icons/search.png"
          show-clear
          @input="onSearchInput"
          @confirm="handleSearch"
          class="search-input"
        />
      </view>
      <view class="filter-button" @click="showFilterModal = true">
        <image src="/static/icons/filter.png" class="filter-icon" />
      </view>
    </view>
    
    <!-- 筛选标签 -->
    <view class="filter-tags" v-if="activeFilters.length > 0">
      <view 
        class="filter-tag"
        v-for="filter in activeFilters"
        :key="filter.key"
        @click="removeFilter(filter.key)"
      >
        <text class="filter-text">{{ filter.label }}</text>
        <image src="/static/icons/close-small.png" class="filter-close" />
      </view>
      <view class="clear-filters" @click="clearAllFilters">
        <text class="clear-text">清空</text>
      </view>
    </view>
    
    <!-- 排序选项 */
    <view class="sort-options">
      <view 
        class="sort-item"
        :class="{ 'sort-item--active': sortBy === sort.value }"
        v-for="sort in sortOptions"
        :key="sort.value"
        @click="changeSortBy(sort.value)"
      >
        <text class="sort-text">{{ sort.label }}</text>
      </view>
    </view>
    
    <!-- 心愿列表 -->
    <scroll-view 
      class="wish-list"
      scroll-y
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="wish-items">
        <wish-card
          v-for="wish in wishList"
          :key="wish.id"
          class="wish-item"
          shadow="light"
          hover
          clickable
          @click="goToWishDetail(wish.id)"
        >
          <view class="wish-content">
            <!-- 用户信息 -->
            <view class="wish-header">
              <image 
                :src="wish.anonymous ? '/static/default-anonymous.png' : wish.user.avatar" 
                class="user-avatar" 
              />
              <view class="user-info">
                <text class="user-nickname">
                  {{ wish.anonymous ? '匿名用户' : wish.user.nickname }}
                </text>
                <text class="wish-time">{{ formatTime(wish.createdAt) }}</text>
              </view>
              <view class="wish-type">
                <image :src="getTypeIcon(wish.type)" class="type-icon" />
              </view>
            </view>
            
            <!-- 心愿内容 -->
            <text class="wish-title">{{ wish.title }}</text>
            <text class="wish-desc">{{ wish.content }}</text>
            
            <!-- 标签和状态 -->
            <view class="wish-footer">
              <view class="wish-tags">
                <text 
                  v-for="tag in wish.tags" 
                  :key="tag"
                  class="wish-tag"
                >
                  #{{ tag }}
                </text>
              </view>
              
              <view class="wish-status">
                <view class="status-item" v-if="wish.status === 'blessed'">
                  <image src="/static/icons/blessed.png" class="status-icon" />
                  <text class="status-text">已赐福</text>
                </view>
                <view class="status-item" v-else>
                  <image src="/static/icons/pending.png" class="status-icon" />
                  <text class="status-text">等待守护</text>
                </view>
              </view>
            </view>
            
            <!-- 互动数据 -->
            <view class="wish-actions">
              <view class="action-item" @click.stop="supportWish(wish.id)">
                <image 
                  :src="wish.isSupported ? '/static/icons/heart-filled.png' : '/static/icons/heart.png'" 
                  class="action-icon" 
                />
                <text class="action-text">{{ wish.supportCount || 0 }}</text>
              </view>
              
              <view class="action-item" @click.stop="goToComments(wish.id)">
                <image src="/static/icons/comment.png" class="action-icon" />
                <text class="action-text">{{ wish.commentCount || 0 }}</text>
              </view>
              
              <view class="action-item" @click.stop="shareWish(wish)">
                <image src="/static/icons/share.png" class="action-icon" />
                <text class="action-text">分享</text>
              </view>
            </view>
          </view>
        </wish-card>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <wish-loading v-if="loading" type="dots" text="加载中..." />
        <text v-else class="load-text" @click="loadMore">点击加载更多</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-else-if="wishList.length > 0">
        <text class="no-more-text">没有更多心愿了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && wishList.length === 0">
        <image src="/static/icons/empty-wishes.png" class="empty-icon" />
        <text class="empty-text">暂时没有心愿</text>
        <text class="empty-desc">成为第一个发布心愿的人吧</text>
        <wish-button
          type="primary"
          text="发布心愿"
          @click="goToPostWish"
          class="empty-button"
        />
      </view>
    </scroll-view>
    
    <!-- 筛选模态框 -->
    <wish-modal
      v-model:visible="showFilterModal"
      title="筛选心愿"
      position="bottom"
    >
      <view class="filter-content">
        <!-- 标签筛选 -->
        <view class="filter-section">
          <text class="filter-title">标签</text>
          <view class="tag-grid">
            <view 
              class="tag-option"
              :class="{ 'tag-option--selected': filters.tags.includes(tag) }"
              v-for="tag in availableTags"
              :key="tag"
              @click="toggleTagFilter(tag)"
            >
              <text class="tag-option-text">{{ tag }}</text>
            </view>
          </view>
        </view>
        
        <!-- 类型筛选 -->
        <view class="filter-section">
          <text class="filter-title">类型</text>
          <view class="type-grid">
            <view 
              class="type-option"
              :class="{ 'type-option--selected': filters.type === type.value }"
              v-for="type in wishTypes"
              :key="type.value"
              @click="selectTypeFilter(type.value)"
            >
              <image :src="type.icon" class="type-option-icon" />
              <text class="type-option-text">{{ type.name }}</text>
            </view>
          </view>
        </view>
        
        <!-- 状态筛选 -->
        <view class="filter-section">
          <text class="filter-title">状态</text>
          <view class="status-options">
            <view 
              class="status-option"
              :class="{ 'status-option--selected': filters.status === status.value }"
              v-for="status in statusOptions"
              :key="status.value"
              @click="selectStatusFilter(status.value)"
            >
              <text class="status-option-text">{{ status.label }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <template #footer>
        <view class="filter-actions">
          <wish-button
            type="ghost"
            text="重置"
            @click="resetFilters"
            class="filter-button-action"
          />
          <wish-button
            type="primary"
            text="确定"
            @click="applyFilters"
            class="filter-button-action"
          />
        </view>
      </template>
    </wish-modal>
  </view>
</template>

<script>
import { useWishStore, useSystemStore } from '@/store'
import { timeUtils, navigation, toast, debounce } from '@/utils'
import { CONSTANTS } from '@/config'

export default {
  data() {
    return {
      searchKeyword: '',
      sortBy: 'latest',
      loading: false,
      refreshing: false,
      showFilterModal: false,
      filters: {
        tags: [],
        type: '',
        status: ''
      },
      sortOptions: [
        { value: 'latest', label: '最新' },
        { value: 'hot', label: '热门' },
        { value: 'random', label: '随机' }
      ],
      statusOptions: [
        { value: '', label: '全部' },
        { value: 'pending', label: '等待守护' },
        { value: 'blessed', label: '已赐福' }
      ],
      wishTypes: [
        {
          value: '',
          name: '全部',
          icon: '/static/icons/all-wish.png'
        },
        {
          value: 'personal',
          name: '个人',
          icon: '/static/icons/personal-wish.png'
        },
        {
          value: 'family',
          name: '家庭',
          icon: '/static/icons/family-wish.png'
        },
        {
          value: 'career',
          name: '事业',
          icon: '/static/icons/career-wish.png'
        },
        {
          value: 'health',
          name: '健康',
          icon: '/static/icons/health-wish.png'
        },
        {
          value: 'other',
          name: '其他',
          icon: '/static/icons/other-wish.png'
        }
      ],
      availableTags: []
    }
  },
  
  computed: {
    wishStore() {
      return useWishStore()
    },
    
    systemStore() {
      return useSystemStore()
    },
    
    wishList() {
      return this.wishStore.wishList
    },
    
    hasMore() {
      return this.wishStore.pagination.hasMore
    },
    
    activeFilters() {
      const filters = []
      
      // 标签筛选
      this.filters.tags.forEach(tag => {
        filters.push({
          key: `tag_${tag}`,
          label: `#${tag}`
        })
      })
      
      // 类型筛选
      if (this.filters.type) {
        const type = this.wishTypes.find(t => t.value === this.filters.type)
        filters.push({
          key: 'type',
          label: type?.name || this.filters.type
        })
      }
      
      // 状态筛选
      if (this.filters.status) {
        const status = this.statusOptions.find(s => s.value === this.filters.status)
        filters.push({
          key: 'status',
          label: status?.label || this.filters.status
        })
      }
      
      return filters
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.refreshData()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      try {
        // 获取可用标签
        const tags = await this.systemStore.fetchTags()
        this.availableTags = tags || CONSTANTS.WISH_TAGS
        
        // 加载心愿列表
        await this.loadWishList(true)
      } catch (error) {
        console.error('页面初始化失败:', error)
      }
    },
    
    /**
     * 刷新数据
     */
    async refreshData() {
      await this.loadWishList(true)
    },
    
    /**
     * 加载心愿列表
     */
    async loadWishList(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      try {
        const params = {
          sortBy: this.sortBy,
          keyword: this.searchKeyword,
          tags: this.filters.tags,
          type: this.filters.type,
          status: this.filters.status
        }
        
        await this.wishStore.fetchWishList(params, refresh)
      } catch (error) {
        console.error('加载心愿列表失败:', error)
        toast.error('加载失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 加载更多
     */
    async loadMore() {
      if (!this.hasMore || this.loading) return
      await this.loadWishList(false)
    },
    
    /**
     * 下拉刷新
     */
    async onRefresh() {
      this.refreshing = true
      try {
        await this.loadWishList(true)
      } finally {
        this.refreshing = false
      }
    },
    
    /**
     * 搜索输入处理
     */
    onSearchInput: debounce(function() {
      this.handleSearch()
    }, 500),
    
    /**
     * 处理搜索
     */
    async handleSearch() {
      await this.loadWishList(true)
    },
    
    /**
     * 改变排序方式
     */
    async changeSortBy(sortBy) {
      if (this.sortBy === sortBy) return
      
      this.sortBy = sortBy
      await this.wishStore.setFilters({ sortBy })
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      return timeUtils.relativeTime(timestamp)
    },
    
    /**
     * 获取类型图标
     */
    getTypeIcon(type) {
      const typeConfig = this.wishTypes.find(t => t.value === type)
      return typeConfig?.icon || '/static/icons/other-wish.png'
    },
    
    /**
     * 助力心愿
     */
    async supportWish(wishId) {
      try {
        await this.wishStore.supportWish(wishId)
        toast.success('助力成功')
      } catch (error) {
        console.error('助力失败:', error)
        toast.error(error.message || '助力失败')
      }
    },
    
    /**
     * 分享心愿
     */
    shareWish(wish) {
      // TODO: 实现分享功能
      toast.info('分享功能开发中')
    },
    
    // 筛选相关方法
    toggleTagFilter(tag) {
      const index = this.filters.tags.indexOf(tag)
      if (index > -1) {
        this.filters.tags.splice(index, 1)
      } else {
        this.filters.tags.push(tag)
      }
    },
    
    selectTypeFilter(type) {
      this.filters.type = this.filters.type === type ? '' : type
    },
    
    selectStatusFilter(status) {
      this.filters.status = this.filters.status === status ? '' : status
    },
    
    resetFilters() {
      this.filters = {
        tags: [],
        type: '',
        status: ''
      }
    },
    
    async applyFilters() {
      this.showFilterModal = false
      await this.loadWishList(true)
    },
    
    removeFilter(key) {
      if (key.startsWith('tag_')) {
        const tag = key.replace('tag_', '')
        const index = this.filters.tags.indexOf(tag)
        if (index > -1) {
          this.filters.tags.splice(index, 1)
        }
      } else if (key === 'type') {
        this.filters.type = ''
      } else if (key === 'status') {
        this.filters.status = ''
      }
      
      this.loadWishList(true)
    },
    
    clearAllFilters() {
      this.resetFilters()
      this.loadWishList(true)
    },
    
    // 页面跳转
    goToWishDetail(wishId) {
      navigation.navigateTo('/pages/wisher/wish-detail', { id: wishId })
    },
    
    goToComments(wishId) {
      navigation.navigateTo('/pages/social/comments', { targetId: wishId, targetType: 'wish' })
    },
    
    goToPostWish() {
      navigation.switchTab('/pages/wisher/post-wish')
    }
  }
}
</script>

<style lang="scss" scoped>
.wish-plaza-page {
  height: 100vh;
  background-color: $wish-bg-primary;
  display: flex;
  flex-direction: column;
}

/* 搜索和筛选栏 */
.search-bar {
  display: flex;
  align-items: center;
  padding: $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.search-input-wrapper {
  flex: 1;
  margin-right: $wish-spacing-sm;
}

.search-input {
  margin-bottom: 0;
}

.filter-button {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: $wish-bg-primary;
  border-radius: $wish-radius-md;
  border: 2rpx solid $wish-border-light;
}

.filter-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 筛选标签 */
.filter-tags {
  display: flex;
  align-items: center;
  padding: $wish-spacing-sm $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
  overflow-x: auto;
}

.filter-tag {
  display: flex;
  align-items: center;
  padding: $wish-spacing-xs $wish-spacing-sm;
  background-color: rgba(232, 180, 160, 0.1);
  border-radius: $wish-radius-md;
  margin-right: $wish-spacing-sm;
  flex-shrink: 0;
}

.filter-text {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
  margin-right: $wish-spacing-xs;
}

.filter-close {
  width: 24rpx;
  height: 24rpx;
}

.clear-filters {
  padding: $wish-spacing-xs $wish-spacing-sm;
  border: 2rpx solid $wish-border-medium;
  border-radius: $wish-radius-md;
  flex-shrink: 0;
}

.clear-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

/* 排序选项 */
.sort-options {
  display: flex;
  align-items: center;
  padding: $wish-spacing-sm $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.sort-item {
  padding: $wish-spacing-xs $wish-spacing-sm;
  border-radius: $wish-radius-sm;
  margin-right: $wish-spacing-md;
  transition: all 0.3s ease;

  &--active {
    background-color: $wish-color-primary;

    .sort-text {
      color: $wish-text-inverse;
    }
  }
}

.sort-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

/* 心愿列表 */
.wish-list {
  flex: 1;
  padding: $wish-spacing-sm;
}

.wish-items {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.wish-item {
  margin: 0;
}

.wish-content {
  padding: 0;
}

/* 心愿卡片内容 */
.wish-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-sm;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
}

.user-info {
  flex: 1;
}

.user-nickname {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: 4rpx;
}

.wish-time {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.wish-type {
  width: 32rpx;
  height: 32rpx;
}

.type-icon {
  width: 100%;
  height: 100%;
}

.wish-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.wish-desc {
  display: -webkit-box;
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-sm;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.wish-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $wish-spacing-sm;
}

.wish-tags {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-xs;
  flex: 1;
}

.wish-tag {
  font-size: $wish-font-xs;
  color: $wish-color-primary;
  background-color: rgba(232, 180, 160, 0.1);
  padding: 4rpx 8rpx;
  border-radius: $wish-radius-sm;
}

.wish-status {
  display: flex;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
}

.status-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: $wish-spacing-xs;
}

.status-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.wish-actions {
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-top: $wish-spacing-sm;
  border-top: 2rpx solid $wish-border-light;
}

.action-item {
  display: flex;
  align-items: center;
  padding: $wish-spacing-xs $wish-spacing-sm;
  border-radius: $wish-radius-sm;
  transition: background-color 0.3s ease;

  &:active {
    background-color: $wish-bg-primary;
  }
}

.action-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: $wish-spacing-xs;
}

.action-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

/* 加载状态 */
.load-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.load-text {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
}

.no-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.no-more-text {
  font-size: $wish-font-sm;
  color: $wish-text-disabled;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $wish-spacing-xxl;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: $wish-spacing-lg;
  opacity: 0.6;
}

.empty-text {
  font-size: $wish-font-lg;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.empty-desc {
  font-size: $wish-font-md;
  color: $wish-text-disabled;
  margin-bottom: $wish-spacing-xl;
}

.empty-button {
  width: 300rpx;
}

/* 筛选模态框 */
.filter-content {
  max-height: 60vh;
  overflow-y: auto;
}

.filter-section {
  margin-bottom: $wish-spacing-xl;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-title {
  display: block;
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-md;
}

.tag-grid {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-sm;
}

.tag-option {
  padding: $wish-spacing-xs $wish-spacing-sm;
  border: 2rpx solid $wish-border-medium;
  border-radius: $wish-radius-md;
  transition: all 0.3s ease;

  &--selected {
    border-color: $wish-color-primary;
    background-color: rgba(232, 180, 160, 0.1);

    .tag-option-text {
      color: $wish-color-primary;
    }
  }
}

.tag-option-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

.type-grid {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-sm;
}

.type-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $wish-spacing-md;
  border: 2rpx solid $wish-border-light;
  border-radius: $wish-radius-md;
  background-color: $wish-bg-secondary;
  transition: all 0.3s ease;
  min-width: 120rpx;

  &--selected {
    border-color: $wish-color-primary;
    background-color: rgba(232, 180, 160, 0.1);
  }
}

.type-option-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: $wish-spacing-xs;
}

.type-option-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.status-options {
  display: flex;
  gap: $wish-spacing-sm;
}

.status-option {
  flex: 1;
  padding: $wish-spacing-md;
  border: 2rpx solid $wish-border-light;
  border-radius: $wish-radius-md;
  background-color: $wish-bg-secondary;
  text-align: center;
  transition: all 0.3s ease;

  &--selected {
    border-color: $wish-color-primary;
    background-color: rgba(232, 180, 160, 0.1);

    .status-option-text {
      color: $wish-color-primary;
    }
  }
}

.status-option-text {
  font-size: $wish-font-md;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

.filter-actions {
  display: flex;
  gap: $wish-spacing-md;
}

.filter-button-action {
  flex: 1;
}
</style>
