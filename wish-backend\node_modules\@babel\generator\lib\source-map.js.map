{"version": 3, "names": ["_genMapping", "require", "_traceMapping", "SourceMap", "constructor", "opts", "code", "_opts$sourceFileName", "_map", "_rawMappings", "_sourceFileName", "_lastGenLine", "_lastSourceLine", "_lastSourceColumn", "_inputMap", "map", "GenMapping", "sourceRoot", "sourceFileName", "replace", "undefined", "inputSourceMap", "TraceMap", "resolvedSources", "length", "i", "_this$_inputMap$sourc", "setSourceContent", "sourcesContent", "Object", "keys", "get", "toEncodedMap", "getDecoded", "toDecodedMap", "getRawMappings", "allMappings", "mark", "generated", "line", "column", "identifierName", "identifierNamePos", "filename", "_originalMapping", "originalMapping", "originalPositionFor", "name", "originalIdentifierMapping", "source", "maybeAddMapping", "original", "exports", "default"], "sources": ["../src/source-map.ts"], "sourcesContent": ["import {\n  GenMapping,\n  maybeAddMapping,\n  setSourceContent,\n  allMappings,\n  toEncodedMap,\n  toDecodedMap,\n} from \"@jridgewell/gen-mapping\";\n\nimport type {\n  EncodedSourceMap,\n  DecodedSourceMap,\n  Mapping,\n} from \"@jridgewell/gen-mapping\";\n\nimport {\n  type SourceMapInput,\n  originalPositionFor,\n  TraceMap,\n} from \"@jridgewell/trace-mapping\";\n\n/**\n * Build a sourcemap.\n */\n\nexport default class SourceMap {\n  private _map: GenMapping;\n  private _rawMappings: Mapping[] | undefined;\n  private _sourceFileName: string | undefined;\n\n  // Any real line is > 0, so init to 0 is fine.\n  private _lastGenLine = 0;\n  private _lastSourceLine = 0;\n\n  // Source columns can be 0, but we only check in unison with sourceLine, which\n  // inits to an impossible value. So init to 0 is fine.\n  private _lastSourceColumn = 0;\n\n  public _inputMap: TraceMap;\n\n  constructor(\n    opts: {\n      sourceFileName?: string;\n      sourceRoot?: string;\n      inputSourceMap?: SourceMapInput;\n    },\n    code: string | { [sourceFileName: string]: string },\n  ) {\n    const map = (this._map = new GenMapping({ sourceRoot: opts.sourceRoot }));\n    this._sourceFileName = opts.sourceFileName?.replace(/\\\\/g, \"/\");\n    this._rawMappings = undefined;\n\n    if (opts.inputSourceMap) {\n      this._inputMap = new TraceMap(opts.inputSourceMap);\n      const resolvedSources = this._inputMap.resolvedSources;\n      if (resolvedSources.length) {\n        for (let i = 0; i < resolvedSources.length; i++) {\n          setSourceContent(\n            map,\n            resolvedSources[i],\n            this._inputMap.sourcesContent?.[i],\n          );\n        }\n      }\n    }\n\n    if (typeof code === \"string\" && !opts.inputSourceMap) {\n      setSourceContent(map, this._sourceFileName, code);\n    } else if (typeof code === \"object\") {\n      for (const sourceFileName of Object.keys(code)) {\n        setSourceContent(\n          map,\n          sourceFileName.replace(/\\\\/g, \"/\"),\n          code[sourceFileName],\n        );\n      }\n    }\n  }\n\n  /**\n   * Get the sourcemap.\n   */\n  get(): EncodedSourceMap {\n    return toEncodedMap(this._map);\n  }\n\n  getDecoded(): DecodedSourceMap {\n    return toDecodedMap(this._map);\n  }\n\n  getRawMappings(): Mapping[] {\n    return (this._rawMappings ||= allMappings(this._map));\n  }\n\n  /**\n   * Mark the current generated position with a source position. May also be passed null line/column\n   * values to insert a mapping to nothing.\n   */\n\n  mark(\n    generated: { line: number; column: number },\n    line: number,\n    column: number,\n    identifierName?: string | null,\n    identifierNamePos?: { line: number; column: number },\n    filename?: string | null,\n  ) {\n    this._rawMappings = undefined;\n\n    let originalMapping: {\n      source: string | null;\n      name?: string | null;\n      line: number | null;\n      column: number | null;\n    };\n\n    if (line != null) {\n      if (this._inputMap) {\n        // This is the lookup for this mark\n        originalMapping = originalPositionFor(this._inputMap, {\n          line,\n          column,\n        });\n\n        // If the we found a name, nothing else needs to be done\n        // Maybe we're marking a `(` and the input map already had a name attached there,\n        // or we're marking a `(` and the sourcemap spanned a `foo(`,\n        // or we're marking an identifier, etc.\n        if (!originalMapping.name && identifierNamePos) {\n          // We're trying to mark a `(` (as that's the only thing that provides\n          // an identifierNamePos currently), and we the AST had an identifier attached.\n          // Lookup it's original name.\n          const originalIdentifierMapping = originalPositionFor(\n            this._inputMap,\n            identifierNamePos,\n          );\n          if (originalIdentifierMapping.name) {\n            identifierName = originalIdentifierMapping.name;\n          }\n        }\n      } else {\n        originalMapping = {\n          source: filename?.replace(/\\\\/g, \"/\") || this._sourceFileName,\n          line: line,\n          column: column,\n        };\n      }\n    }\n\n    maybeAddMapping(this._map, {\n      name: identifierName,\n      generated,\n      source: originalMapping?.source,\n      original: originalMapping,\n    });\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,WAAA,GAAAC,OAAA;AAeA,IAAAC,aAAA,GAAAD,OAAA;AAUe,MAAME,SAAS,CAAC;EAe7BC,WAAWA,CACTC,IAIC,EACDC,IAAmD,EACnD;IAAA,IAAAC,oBAAA;IAAA,KArBMC,IAAI;IAAA,KACJC,YAAY;IAAA,KACZC,eAAe;IAAA,KAGfC,YAAY,GAAG,CAAC;IAAA,KAChBC,eAAe,GAAG,CAAC;IAAA,KAInBC,iBAAiB,GAAG,CAAC;IAAA,KAEtBC,SAAS;IAUd,MAAMC,GAAG,GAAI,IAAI,CAACP,IAAI,GAAG,IAAIQ,sBAAU,CAAC;MAAEC,UAAU,EAAEZ,IAAI,CAACY;IAAW,CAAC,CAAE;IACzE,IAAI,CAACP,eAAe,IAAAH,oBAAA,GAAGF,IAAI,CAACa,cAAc,qBAAnBX,oBAAA,CAAqBY,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAC/D,IAAI,CAACV,YAAY,GAAGW,SAAS;IAE7B,IAAIf,IAAI,CAACgB,cAAc,EAAE;MACvB,IAAI,CAACP,SAAS,GAAG,IAAIQ,sBAAQ,CAACjB,IAAI,CAACgB,cAAc,CAAC;MAClD,MAAME,eAAe,GAAG,IAAI,CAACT,SAAS,CAACS,eAAe;MACtD,IAAIA,eAAe,CAACC,MAAM,EAAE;QAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,eAAe,CAACC,MAAM,EAAEC,CAAC,EAAE,EAAE;UAAA,IAAAC,qBAAA;UAC/C,IAAAC,4BAAgB,EACdZ,GAAG,EACHQ,eAAe,CAACE,CAAC,CAAC,GAAAC,qBAAA,GAClB,IAAI,CAACZ,SAAS,CAACc,cAAc,qBAA7BF,qBAAA,CAAgCD,CAAC,CACnC,CAAC;QACH;MACF;IACF;IAEA,IAAI,OAAOnB,IAAI,KAAK,QAAQ,IAAI,CAACD,IAAI,CAACgB,cAAc,EAAE;MACpD,IAAAM,4BAAgB,EAACZ,GAAG,EAAE,IAAI,CAACL,eAAe,EAAEJ,IAAI,CAAC;IACnD,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MACnC,KAAK,MAAMY,cAAc,IAAIW,MAAM,CAACC,IAAI,CAACxB,IAAI,CAAC,EAAE;QAC9C,IAAAqB,4BAAgB,EACdZ,GAAG,EACHG,cAAc,CAACC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,EAClCb,IAAI,CAACY,cAAc,CACrB,CAAC;MACH;IACF;EACF;EAKAa,GAAGA,CAAA,EAAqB;IACtB,OAAO,IAAAC,wBAAY,EAAC,IAAI,CAACxB,IAAI,CAAC;EAChC;EAEAyB,UAAUA,CAAA,EAAqB;IAC7B,OAAO,IAAAC,wBAAY,EAAC,IAAI,CAAC1B,IAAI,CAAC;EAChC;EAEA2B,cAAcA,CAAA,EAAc;IAC1B,OAAQ,IAAI,CAAC1B,YAAY,KAAjB,IAAI,CAACA,YAAY,GAAK,IAAA2B,uBAAW,EAAC,IAAI,CAAC5B,IAAI,CAAC;EACtD;EAOA6B,IAAIA,CACFC,SAA2C,EAC3CC,IAAY,EACZC,MAAc,EACdC,cAA8B,EAC9BC,iBAAoD,EACpDC,QAAwB,EACxB;IAAA,IAAAC,gBAAA;IACA,IAAI,CAACnC,YAAY,GAAGW,SAAS;IAE7B,IAAIyB,eAKH;IAED,IAAIN,IAAI,IAAI,IAAI,EAAE;MAChB,IAAI,IAAI,CAACzB,SAAS,EAAE;QAElB+B,eAAe,GAAG,IAAAC,iCAAmB,EAAC,IAAI,CAAChC,SAAS,EAAE;UACpDyB,IAAI;UACJC;QACF,CAAC,CAAC;QAMF,IAAI,CAACK,eAAe,CAACE,IAAI,IAAIL,iBAAiB,EAAE;UAI9C,MAAMM,yBAAyB,GAAG,IAAAF,iCAAmB,EACnD,IAAI,CAAChC,SAAS,EACd4B,iBACF,CAAC;UACD,IAAIM,yBAAyB,CAACD,IAAI,EAAE;YAClCN,cAAc,GAAGO,yBAAyB,CAACD,IAAI;UACjD;QACF;MACF,CAAC,MAAM;QACLF,eAAe,GAAG;UAChBI,MAAM,EAAE,CAAAN,QAAQ,oBAARA,QAAQ,CAAExB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,KAAI,IAAI,CAACT,eAAe;UAC7D6B,IAAI,EAAEA,IAAI;UACVC,MAAM,EAAEA;QACV,CAAC;MACH;IACF;IAEA,IAAAU,2BAAe,EAAC,IAAI,CAAC1C,IAAI,EAAE;MACzBuC,IAAI,EAAEN,cAAc;MACpBH,SAAS;MACTW,MAAM,GAAAL,gBAAA,GAAEC,eAAe,qBAAfD,gBAAA,CAAiBK,MAAM;MAC/BE,QAAQ,EAAEN;IACZ,CAAC,CAAC;EACJ;AACF;AAACO,OAAA,CAAAC,OAAA,GAAAlD,SAAA", "ignoreList": []}