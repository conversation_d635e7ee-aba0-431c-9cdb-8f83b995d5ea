<!--
  愿境分享组件
  提供多种分享方式的统一组件
-->
<template>
  <wish-modal
    v-model:visible="visible"
    title="分享心愿"
    position="bottom"
    @close="handleClose"
  >
    <view class="share-content">
      <!-- 分享预览 -->
      <view class="share-preview">
        <view class="preview-card">
          <view class="preview-header">
            <image :src="shareData.avatar" class="preview-avatar" />
            <view class="preview-info">
              <text class="preview-name">{{ shareData.nickname }}</text>
              <text class="preview-desc">在愿境许下心愿</text>
            </view>
          </view>
          
          <text class="preview-title">{{ shareData.title }}</text>
          <text class="preview-content">{{ shareData.content }}</text>
          
          <view class="preview-footer">
            <text class="preview-app">来自愿境</text>
          </view>
        </view>
      </view>
      
      <!-- 分享方式 -->
      <view class="share-methods">
        <view 
          class="share-method"
          v-for="method in shareMethods"
          :key="method.type"
          @click="shareWith(method.type)"
        >
          <view class="method-icon">
            <image :src="method.icon" class="icon-img" />
          </view>
          <text class="method-name">{{ method.name }}</text>
        </view>
      </view>
      
      <!-- 分享选项 -->
      <view class="share-options">
        <view class="option-item" @click="copyLink">
          <image src="/static/icons/copy.png" class="option-icon" />
          <text class="option-text">复制链接</text>
        </view>
        
        <view class="option-item" @click="saveImage">
          <image src="/static/icons/save.png" class="option-icon" />
          <text class="option-text">保存图片</text>
        </view>
        
        <view class="option-item" @click="generateQRCode">
          <image src="/static/icons/qrcode.png" class="option-icon" />
          <text class="option-text">生成二维码</text>
        </view>
      </view>
    </view>
  </wish-modal>
</template>

<script>
import { toast } from '@/utils'

export default {
  name: 'WishShare',
  
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    shareData: {
      type: Object,
      default: () => ({
        id: '',
        title: '',
        content: '',
        avatar: '',
        nickname: '',
        type: 'wish'
      })
    }
  },
  
  emits: ['update:modelValue', 'shared'],
  
  data() {
    return {
      shareMethods: [
        {
          type: 'wechat',
          name: '微信好友',
          icon: '/static/icons/wechat-share.png'
        },
        {
          type: 'moments',
          name: '朋友圈',
          icon: '/static/icons/moments.png'
        },
        {
          type: 'qq',
          name: 'QQ好友',
          icon: '/static/icons/qq-share.png'
        },
        {
          type: 'qzone',
          name: 'QQ空间',
          icon: '/static/icons/qzone.png'
        },
        {
          type: 'weibo',
          name: '微博',
          icon: '/static/icons/weibo.png'
        }
      ]
    }
  },
  
  computed: {
    visible: {
      get() {
        return this.modelValue
      },
      set(value) {
        this.$emit('update:modelValue', value)
      }
    }
  },
  
  methods: {
    /**
     * 分享到指定平台
     */
    async shareWith(type) {
      try {
        const shareContent = this.generateShareContent()
        
        switch (type) {
          case 'wechat':
            await this.shareToWechat(shareContent)
            break
          case 'moments':
            await this.shareToMoments(shareContent)
            break
          case 'qq':
            await this.shareToQQ(shareContent)
            break
          case 'qzone':
            await this.shareToQzone(shareContent)
            break
          case 'weibo':
            await this.shareToWeibo(shareContent)
            break
          default:
            toast.error('暂不支持该分享方式')
            return
        }
        
        this.handleShared(type)
      } catch (error) {
        console.error('分享失败:', error)
        toast.error('分享失败，请重试')
      }
    },
    
    /**
     * 生成分享内容
     */
    generateShareContent() {
      const { title, content, nickname } = this.shareData
      
      return {
        title: `${nickname}在愿境许下心愿`,
        desc: title,
        content: content.length > 100 ? content.substring(0, 100) + '...' : content,
        imageUrl: this.generateShareImage(),
        path: `/pages/wisher/wish-detail?id=${this.shareData.id}`,
        webUrl: `https://wish.example.com/wish/${this.shareData.id}`
      }
    },
    
    /**
     * 生成分享图片
     */
    generateShareImage() {
      // TODO: 生成分享图片
      return '/static/share-default.png'
    },
    
    /**
     * 分享到微信好友
     */
    async shareToWechat(content) {
      // #ifdef MP-WEIXIN
      return new Promise((resolve, reject) => {
        wx.shareAppMessage({
          title: content.title,
          desc: content.desc,
          path: content.path,
          imageUrl: content.imageUrl,
          success: resolve,
          fail: reject
        })
      })
      // #endif
      
      // #ifndef MP-WEIXIN
      toast.info('请在微信小程序中使用此功能')
      // #endif
    },
    
    /**
     * 分享到朋友圈
     */
    async shareToMoments(content) {
      // #ifdef MP-WEIXIN
      return new Promise((resolve, reject) => {
        wx.shareTimeline({
          title: `${content.title} - ${content.desc}`,
          imageUrl: content.imageUrl,
          success: resolve,
          fail: reject
        })
      })
      // #endif
      
      // #ifndef MP-WEIXIN
      toast.info('请在微信小程序中使用此功能')
      // #endif
    },
    
    /**
     * 分享到QQ
     */
    async shareToQQ(content) {
      // #ifdef MP-QQ
      return new Promise((resolve, reject) => {
        qq.shareAppMessage({
          title: content.title,
          desc: content.desc,
          path: content.path,
          imageUrl: content.imageUrl,
          success: resolve,
          fail: reject
        })
      })
      // #endif
      
      // #ifndef MP-QQ
      toast.info('请在QQ小程序中使用此功能')
      // #endif
    },
    
    /**
     * 分享到QQ空间
     */
    async shareToQzone(content) {
      // TODO: 实现QQ空间分享
      toast.info('QQ空间分享功能开发中')
    },
    
    /**
     * 分享到微博
     */
    async shareToWeibo(content) {
      // TODO: 实现微博分享
      toast.info('微博分享功能开发中')
    },
    
    /**
     * 复制链接
     */
    copyLink() {
      const link = `https://wish.example.com/wish/${this.shareData.id}`
      
      uni.setClipboardData({
        data: link,
        success: () => {
          toast.success('链接已复制到剪贴板')
          this.handleShared('copy')
        },
        fail: () => {
          toast.error('复制失败')
        }
      })
    },
    
    /**
     * 保存图片
     */
    async saveImage() {
      try {
        // TODO: 生成并保存分享图片
        toast.info('保存图片功能开发中')
        this.handleShared('save')
      } catch (error) {
        console.error('保存图片失败:', error)
        toast.error('保存失败')
      }
    },
    
    /**
     * 生成二维码
     */
    generateQRCode() {
      // TODO: 生成二维码
      toast.info('二维码生成功能开发中')
    },
    
    /**
     * 处理分享完成
     */
    handleShared(type) {
      this.$emit('shared', { type, data: this.shareData })
      this.visible = false
    },
    
    /**
     * 处理关闭
     */
    handleClose() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.share-content {
  padding: $wish-spacing-md 0;
}

/* 分享预览 */
.share-preview {
  margin-bottom: $wish-spacing-xl;
}

.preview-card {
  background-color: $wish-bg-primary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-md;
  border: 2rpx solid $wish-border-light;
}

.preview-header {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-sm;
}

.preview-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
}

.preview-info {
  flex: 1;
}

.preview-name {
  display: block;
  font-size: $wish-font-sm;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: 4rpx;
}

.preview-desc {
  font-size: $wish-font-xs;
  color: $wish-text-secondary;
}

.preview-title {
  display: block;
  font-size: $wish-font-md;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
  @extend .ellipsis;
}

.preview-content {
  display: block;
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  line-height: 1.6;
  margin-bottom: $wish-spacing-sm;
  @extend .ellipsis-2;
}

.preview-footer {
  text-align: right;
}

.preview-app {
  font-size: $wish-font-xs;
  color: $wish-text-disabled;
}

/* 分享方式 */
.share-methods {
  display: flex;
  justify-content: space-around;
  margin-bottom: $wish-spacing-xl;
  padding: 0 $wish-spacing-md;
}

.share-method {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform 0.2s ease;
  
  &:active {
    transform: scale(0.95);
  }
}

.method-icon {
  width: 96rpx;
  height: 96rpx;
  background-color: $wish-bg-primary;
  border-radius: $wish-radius-lg;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: $wish-spacing-sm;
  box-shadow: $wish-shadow-sm;
}

.icon-img {
  width: 48rpx;
  height: 48rpx;
}

.method-name {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

/* 分享选项 */
.share-options {
  border-top: 2rpx solid $wish-border-light;
  padding-top: $wish-spacing-md;
}

.option-item {
  display: flex;
  align-items: center;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
  transition: background-color 0.3s ease;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &:active {
    background-color: $wish-bg-primary;
  }
}

.option-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: $wish-spacing-md;
}

.option-text {
  font-size: $wish-font-md;
  color: $wish-text-primary;
}
</style>
