技术要求：
前端：uniAPP，基于移动端开发
后端：node.js + mysql

《愿境》产品设计说明书 (PRD)
	• 文档版本: V1.0
	• 创建日期: 2025年7月17日
	• 创建人: 
	• 产品名称 (暂定): 愿境 (WishRealm)

1.0 产品概述 (Product Overview)
1.1 产品定位
《愿境》是一款以“祈愿-守护”为核心机制的、融合了角色扮演、游戏化激励和情感支持的新型精神社交平台。
1.2 核心理念
我们相信，每个人内心都有表达希望的需求（成为祈愿者），也潜藏着帮助他人、传递善意的渴望（成为守护者）。《愿境》旨在创造一个温暖、正向、安全的虚拟空间，让用户的情感和善意能够具象化地流动、积累和传递，从而获得精神慰藉与个人成就感。
1.3 目标用户 (Target Audience)
	• 核心用户 (Primary): 18-35岁的年轻人群体。他们面临学业、工作、情感等多重压力，需要精神寄托和情绪出口；同时，他们是互联网原住民，对游戏化、社交化的产品接受度高。
	• 次要用户 (Secondary):
		○ 有倾诉欲望，但又不希望在现实社交圈过多暴露负面情绪的用户。
		○ 有助人意愿，希望通过微小的善举获得成就感和社区认可的用户。
		○ 对轻度角色扮演和养成类游戏感兴趣的用户。

2.0 用户角色与核心系统
为了降低宗教敏感性，并提升产品的普适性和想象空间，建议将“信徒”和“神明”的称谓优化为：
	• 祈愿者 (Wisher): 对应“信徒”，是愿望的发出方。
	• 守护者 (Guardian): 对应“神明”，是愿望的回应方。
2.1 核心数值系统

数值名称	所属角色	如何获取	如何消耗/作用
心愿力 (Wish Power)	祈愿者	1. 每日签到（上香/冥想）	1. 发布心愿（基础消耗）
		2. 完成新手任务	2. 提升心愿的曝光度（“助力”）
		3. 参与社区活动
功德值 (Merit Points)	守护者	1. 回应（赐福）祈愿者的心愿	1. 提升守护者等级和称号
		2. 收到祈愿者的“感谢”/好评反馈	2. 解锁更华丽的个人主页/神殿装饰
		3. 创建的“神殿”获得成员	3. 排行榜排名的核心依据
导出到 Google 表格
2.2 角色系统
	• 默认身份: 新用户注册后默认为“祈愿者”身份。
	• 身份切换: 用户可在个人中心自由、即时地切换为“守护者”或“祈愿者”身份。界面和核心功能会随身份变化而改变。

3.0 核心功能模块 (Feature Modules)
3.1 用户系统 (User Module)
	• 3.1.1 注册/登录: 支持手机号注册/登录。
	• 3.1.2 个人主页:
		○ 通用信息: 昵称、头像、ID、个人简介。
		○ 祈愿者视图: 展示当前心愿力、历史愿望列表、收到的赐福。
		○ 守护者视图: 展示当前功德值、守护者等级/称号、历史赐福列表、收到的感谢。
	• 3.1.3 身份切换: 在个人中心提供醒目的切换按钮。
3.2 祈愿模块 (Wisher's Module)
	• 3.2.1 每日仪式 (Daily Ritual):
		○ 功能: 用户每日首次打开App时，进入一个沉浸式动画界面（如点亮一盏香、放飞一个孔明灯、向星空冥想等），完成仪式后获得心愿力。
		○ 目的: 增强仪式感，培养用户每日登录习惯。
	• 3.2.2 发布心愿 (Post a Wish):
		○ 入口: App首页核心按钮。
		○ 表单字段:
			§ 愿望标题 (必填)
			§ 愿望内容 (必填, 限制字数)
			§ 添加标签 (可选, 如 #学业 #健康 #情感 #工作)
			§ 设置隐私 (公开/仅自己可见)
		○ 消耗: 发布一个公开愿望需要消耗固定数量的心愿力。
	• 3.2.3 许愿广场 (Wish Plaza):
		○ 以信息流形式展示所有公开的愿望。
		○ 支持按“最新”、“最热”（被助力多）、按“标签”筛选。
		○ 用户可以消耗少量心愿力为他人的愿望“助力”，增加其曝光。
	• 3.2.4 我的心愿 (My Wishes):
		○ 列表形式展示自己发布的所有心愿及其状态（等待回应/已获赐福）。
		○ 可在此查看守护者的回应。
3.3 守护模块 (Guardian's Module)
	• 3.3.1 聆听世愿 (Listen to Wishes):
		○ 守护者身份的首页，即为许愿广场。
		○ 提供“一键刷新”、“随机降临”（随机匹配一个愿望）等趣味功能。
	• 3.3.2 回应愿望/赐福 (Bestow Blessing):
		○ 入口: 在愿望详情页点击“回应此愿”。
		○ 回应方式:
			§ 文字祝福: 输入一段鼓励、祝福或建议的话。
			§ 赐福卡片: 选择系统预设的、带有精美图文的祝福卡片（如“金榜题名”、“一切顺利”）。
			§ 上传图片: 可上传一张手绘的画、一张风景照等作为回应。
		○ 奖励: 成功回应后，立即获得基础功德值。
	• 3.3.3 接收反馈 (Receive Feedback):
		○ 祈愿者在查看“赐福”后，可选择“叩谢”（相当于好评）。
		○ 守护者收到“叩谢”后，会获得额外的功德值奖励。
		○ 注意: 为避免负向反馈打击守护者积极性，不设置“差评”按钮。祈愿者可以选择不反馈。
3.4 社交与社区模块 (Social & Community Module)
	• 3.4.1 排行榜 (Leaderboard):
		○ 榜单类型: 功德总榜、日榜、周榜、月榜。
		○ 目的: 激励守护者持续活跃，给予高阶玩家荣誉感。
	• 3.4.2 神殿系统 (Temple System) (V1.1或之后版本)
		○ 创建: 达到一定功德值的守护者可以创建自己的“神殿”。
		○ 功能: 神殿是守护者的粉丝社区，有独立的公告板、交流区。祈愿者可以“入驻”神殿，成为该守护者的忠实粉丝。
	• 3.4.3 交流功能 (Communication):
		○ 评论: 允许用户在公开愿望下方进行评论互动，营造社区氛围。
		○ 私信: 允许祈愿者与回应过自己的守护者发起私信。为防止骚扰，可设置为“守护者同意后方可开启对话”。
3.5 商业化模块 (Monetization Module)
	• 3.5.1 虚拟物品购买:
		○ 祈愿者: 购买设计精美的虚拟“贡品”（如七彩莲花灯、锦鲤香烛），使用后发布的心愿会有特殊动态效果，更容易被看到。
		○ 守护者: 购买个人主页或神殿的专属主题、挂件、背景音乐。
	• 3.5.2 会员订阅:
		○ 祈愿者会员: 每日获得更多心愿力、每月可发布一个“置顶心愿”。
		○ 守护者会员: 解锁高级赐福卡片、查看更详细的数据看板（如被感谢率、赐福类型统计等）。
3.6 内容审核与举报模块 (Content Moderation Module)
	3.6.1 内容审核系统 (Content Review System)
	自动审核（AI审核）
		• 审核范围：用户昵称、心愿内容、赐福内容、评论、私信、上传图片等。
		• 关键词过滤：基于敏感词库（定期更新），拦截违规词汇。
		• 图像识别：对上传图片进行智能检测，识别色情、暴力、涉政等内容。
		• 模型支持：引入自然语言处理和图像识别AI模型对文本与图片进行多维审核（例如：OpenAI Whisper + CLIP + 自研模型）。
	人工审核
		• 审核后台系统：为审核员提供专属管理端，呈现待审核内容及用户历史记录。
		• 优先级机制：举报多次、AI置信度高的内容优先审核。
		• 审核标签：人工审核员可标记“违规”、“边界内容”、“安全”，供模型学习与运营介入。
		• 冷却机制：在内容发布后短时间内进行冷却处理，高风险内容延迟展示。
	
	3.6.2 举报系统 (Report System)
	举报入口
		• 每条用户生成内容（UGC）下方均提供“举报”按钮。
		• 举报内容：心愿、赐福、评论、私信、神殿公告、昵称头像等。
	举报理由分类（用户可选）
		• 色情/暴力/低俗内容
		• 违法信息/政治敏感
		• 广告/引流/诈骗
		• 骚扰/攻击性言论
		• 虚假内容/冒充他人
		• 其他（可选填说明）
	举报处理流程
		1. 举报提交后自动进入待审队列；
		2. AI预判内容风险级别，标记高优先级项目；
		3. 人工审核介入处理，并记录处理结论；
		4. 违规确认后处理方式：
			○ 删除内容
			○ 警告用户
			○ 限制功能（如禁止发言/祈愿/赐福）
			○ 永久封号（严重违规）
	举报反馈
		• 举报用户可查看“我的举报记录”；
		• 举报处理结果可选择是否通知举报者（开启通知增强用户信任感）。
	
	3.7 全民祈福活动系统 (Admin-initiated Events)
	3.7.1 活动目的
	用于响应重大事件（如地震、疾病、高考、传统节日等）发起平台级全民祈福/助力活动，营造用户共情与正向能量。
	3.7.2 活动类型
		• 全民祈福墙：平台设定主题，所有用户可发布祈福言语/心愿卡片，展示于同一界面。
		• 功德助力池：用户可通过上香/助力行为积累“全民功德值”，解锁活动阶段奖励。
		• 事件祈愿链：按用户地理位置/兴趣匹配建立祈愿接力链条，形成情感传递。
		• 联合神殿赐福：鼓励多个高等级守护者发起联合赐福，回应全民祈福主题。
	3.7.3 活动发布流程
		• 发布权限：平台运营人员/Admin账号拥有活动发布权限。
		• 发布入口：在后台控制面板中设定标题、封面、活动类型、时间范围、奖励规则等。
		• 展示位置：
			○ 首页Banner
			○ 祈愿页面置顶
			○ 神殿/排行模块横幅
			○ 推送/通知提醒
	3.7.4 活动奖励与反馈
		○ 用户奖励：完成特定行为后获得心愿力/功德值/专属称号或装饰道具。
		○ 排行榜机制：对参与度高的用户或神殿设立活动榜单。
		○ 活动总结：活动结束后生成总结页面，展示总参与人数、总功德值、精选祈愿内容等。
	

4.0 核心用户流程 (Core User Flow)
4.1 祈愿者核心流程
注册/登录 -> 完成每日仪式，获取心愿力 -> 点击发布心愿 -> 填写内容并发布 -> （可选）在许愿广场浏览/助力他人 -> 收到赐福通知 -> 查看守护者的回应 -> （可选）点击“叩谢”表达感谢
4.2 守护者核心流程
切换至守护者身份 -> 在许愿广场浏览心愿 -> 选择一个心愿进入详情页 -> 点击“回应此愿” -> 选择赐福方式并发送 -> 获得基础功德值 -> 收到祈愿者的“叩谢”通知 -> 获得额外功德值 -> 查看功德值排名变化

5.0 非功能性需求 (Non-functional Requirements)
	• 5.1 UI/UX设计:
		○ 风格: 温暖、治愈、简约、静谧。可采用低饱和度色彩、圆角设计、柔和的动画效果。
		○ 体验: 流程清晰，引导性强，减少用户思考成本。仪式感和沉浸感是设计重点。
	• 5.2 性能:
		○ 应用启动、页面切换、信息流加载必须流畅。
		○ 赐福和心愿发布的响应时间应在1秒以内。
	• 5.3 内容安全:
		○ 必须建立严格的关键词过滤系统（AI审核），屏蔽涉政、色情、暴力、广告、导流等违规内容。
		○ 建立用户举报机制，并配备人工审核团队处理举报和复杂内容。
6.0 风险与合规管理建议
6.1 法律合规
	• 遵守《网络信息内容生态治理规定》《未成年人保护法》等法规。
	• 涉宗教相关内容需高度注意表述隐喻，避免引发误解。
6.2 用户行为规范（可嵌入注册协议与社区公约）
	• 不得发布任何具有伤害性、歧视性、诱导性内容。
	• 不得冒充他人进行身份发布与赐福。
	• 对多次被举报用户进行行为限制或注销处理。

