<!--
  愿境私信列表页面
  展示用户的所有对话
-->
<template>
  <view class="messages-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar safe-area-top">
      <view class="navbar-content">
        <view class="navbar-back" @click="goBack">
          <image src="/static/icons/arrow-left.png" class="back-icon" />
        </view>
        <text class="navbar-title">消息</text>
        <view class="navbar-actions">
          <view class="navbar-action" @click="showSearchModal = true">
            <image src="/static/icons/search.png" class="action-icon" />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 消息类型切换 -->
    <view class="message-tabs">
      <view 
        class="tab-item"
        :class="{ 'tab-item--active': messageType === type.value }"
        v-for="type in messageTypes"
        :key="type.value"
        @click="switchMessageType(type.value)"
      >
        <text class="tab-text">{{ type.label }}</text>
        <view v-if="type.count > 0" class="tab-badge">
          <text class="badge-text">{{ type.count > 99 ? '99+' : type.count }}</text>
        </view>
      </view>
    </view>
    
    <!-- 对话列表 -->
    <scroll-view 
      class="conversation-list"
      scroll-y
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="conversation-items">
        <view 
          v-for="conversation in conversationList"
          :key="conversation.id"
          class="conversation-item"
          @click="goToChat(conversation)"
          @longpress="showConversationMenu(conversation)"
        >
          <!-- 用户头像 -->
          <view class="avatar-container">
            <image :src="conversation.user.avatar" class="user-avatar" />
            <view v-if="conversation.user.isOnline" class="online-indicator"></view>
            <view v-if="conversation.unreadCount > 0" class="unread-badge">
              <text class="unread-text">{{ conversation.unreadCount > 99 ? '99+' : conversation.unreadCount }}</text>
            </view>
          </view>
          
          <!-- 对话信息 -->
          <view class="conversation-info">
            <view class="conversation-header">
              <text class="user-nickname">{{ conversation.user.nickname }}</text>
              <text class="message-time">{{ formatTime(conversation.lastMessageTime) }}</text>
            </view>
            
            <view class="last-message">
              <text class="message-content">{{ getLastMessageContent(conversation.lastMessage) }}</text>
              <view v-if="conversation.lastMessage.type === 'image'" class="message-type-icon">
                <image src="/static/icons/image-small.png" class="type-icon" />
              </view>
            </view>
          </view>
          
          <!-- 操作指示 -->
          <view class="conversation-actions">
            <image v-if="conversation.isPinned" src="/static/icons/pin.png" class="pin-icon" />
            <image v-if="conversation.isMuted" src="/static/icons/mute.png" class="mute-icon" />
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <wish-loading v-if="loading" type="dots" text="加载中..." />
        <text v-else class="load-text" @click="loadMore">点击加载更多</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-else-if="conversationList.length > 0">
        <text class="no-more-text">没有更多对话了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && conversationList.length === 0">
        <image src="/static/icons/empty-messages.png" class="empty-icon" />
        <text class="empty-text">{{ getEmptyText() }}</text>
        <text class="empty-desc">{{ getEmptyDesc() }}</text>
        <wish-button
          v-if="messageType === 'chat'"
          type="primary"
          text="去社区逛逛"
          @click="goToCommunity"
          class="empty-button"
        />
      </view>
    </scroll-view>
    
    <!-- 搜索模态框 -->
    <wish-modal
      v-model:visible="showSearchModal"
      title="搜索用户"
      position="bottom"
    >
      <view class="search-content">
        <wish-input
          v-model="searchKeyword"
          placeholder="输入用户昵称或ID"
          prefix-icon="/static/icons/search.png"
          show-clear
          @input="onSearchInput"
          @confirm="handleSearch"
          class="search-input"
        />
        
        <!-- 搜索结果 -->
        <view v-if="searchResults.length > 0" class="search-results">
          <view 
            v-for="user in searchResults"
            :key="user.id"
            class="search-result-item"
            @click="startChat(user)"
          >
            <image :src="user.avatar" class="result-avatar" />
            <view class="result-info">
              <text class="result-nickname">{{ user.nickname }}</text>
              <text class="result-desc">{{ user.bio || '这个人很神秘，什么都没留下' }}</text>
            </view>
          </view>
        </view>
        
        <!-- 搜索为空 -->
        <view v-else-if="searchKeyword && !searchLoading" class="search-empty">
          <text class="search-empty-text">未找到相关用户</text>
        </view>
      </view>
    </wish-modal>
    
    <!-- 对话操作菜单 -->
    <wish-modal
      v-model:visible="showMenuModal"
      title="对话操作"
      position="bottom"
    >
      <view class="menu-actions" v-if="selectedConversation">
        <view class="menu-action" @click="togglePin">
          <image :src="selectedConversation.isPinned ? '/static/icons/unpin.png' : '/static/icons/pin.png'" class="menu-icon" />
          <text class="menu-text">{{ selectedConversation.isPinned ? '取消置顶' : '置顶对话' }}</text>
        </view>
        
        <view class="menu-action" @click="toggleMute">
          <image :src="selectedConversation.isMuted ? '/static/icons/unmute.png' : '/static/icons/mute.png'" class="menu-icon" />
          <text class="menu-text">{{ selectedConversation.isMuted ? '取消免打扰' : '免打扰' }}</text>
        </view>
        
        <view class="menu-action" @click="markAsRead">
          <image src="/static/icons/mark-read.png" class="menu-icon" />
          <text class="menu-text">标记为已读</text>
        </view>
        
        <view class="menu-action" @click="deleteConversation">
          <image src="/static/icons/delete.png" class="menu-icon" />
          <text class="menu-text">删除对话</text>
        </view>
      </view>
    </wish-modal>
  </view>
</template>

<script>
import { useUserStore, useSocialStore } from '@/store'
import { timeUtils, navigation, toast, debounce } from '@/utils'

export default {
  data() {
    return {
      messageType: 'chat',
      loading: false,
      refreshing: false,
      searchLoading: false,
      showSearchModal: false,
      showMenuModal: false,
      searchKeyword: '',
      searchResults: [],
      conversationList: [],
      selectedConversation: null,
      messageTypes: [
        { value: 'chat', label: '聊天', count: 0 },
        { value: 'system', label: '系统通知', count: 0 },
        { value: 'blessing', label: '赐福通知', count: 0 }
      ]
    }
  },
  
  computed: {
    userStore() {
      return useUserStore()
    },
    
    socialStore() {
      return useSocialStore()
    },
    
    hasMore() {
      return this.socialStore.pagination.hasMore
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.refreshData()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      await this.loadConversations(true)
      await this.updateMessageCounts()
    },
    
    /**
     * 刷新数据
     */
    async refreshData() {
      await this.loadConversations(true)
      await this.updateMessageCounts()
    },
    
    /**
     * 加载对话列表
     */
    async loadConversations(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      try {
        const params = {
          type: this.messageType
        }
        
        const result = await this.socialStore.fetchConversations(params, refresh)
        
        if (refresh) {
          this.conversationList = result.list || []
        } else {
          this.conversationList.push(...(result.list || []))
        }
      } catch (error) {
        console.error('加载对话列表失败:', error)
        toast.error('加载失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 更新消息数量
     */
    async updateMessageCounts() {
      try {
        const counts = await this.socialStore.getMessageCounts()
        this.messageTypes.forEach(type => {
          type.count = counts[type.value] || 0
        })
      } catch (error) {
        console.error('获取消息数量失败:', error)
      }
    },
    
    /**
     * 加载更多
     */
    async loadMore() {
      if (!this.hasMore || this.loading) return
      await this.loadConversations(false)
    },
    
    /**
     * 下拉刷新
     */
    async onRefresh() {
      this.refreshing = true
      try {
        await this.loadConversations(true)
        await this.updateMessageCounts()
      } finally {
        this.refreshing = false
      }
    },
    
    /**
     * 切换消息类型
     */
    async switchMessageType(type) {
      if (this.messageType === type) return
      
      this.messageType = type
      await this.loadConversations(true)
    },
    
    /**
     * 搜索输入处理
     */
    onSearchInput: debounce(function() {
      this.handleSearch()
    }, 500),
    
    /**
     * 处理搜索
     */
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = []
        return
      }
      
      this.searchLoading = true
      try {
        const result = await this.socialStore.searchUsers(this.searchKeyword.trim())
        this.searchResults = result.list || []
      } catch (error) {
        console.error('搜索用户失败:', error)
        toast.error('搜索失败')
      } finally {
        this.searchLoading = false
      }
    },
    
    /**
     * 开始聊天
     */
    async startChat(user) {
      try {
        // 创建或获取对话
        const conversation = await this.socialStore.createConversation(user.id)
        
        this.showSearchModal = false
        this.searchKeyword = ''
        this.searchResults = []
        
        // 跳转到聊天页面
        this.goToChat(conversation)
      } catch (error) {
        console.error('创建对话失败:', error)
        toast.error('创建对话失败')
      }
    },
    
    /**
     * 显示对话菜单
     */
    showConversationMenu(conversation) {
      this.selectedConversation = conversation
      this.showMenuModal = true
    },
    
    /**
     * 切换置顶状态
     */
    async togglePin() {
      if (!this.selectedConversation) return
      
      try {
        await this.socialStore.toggleConversationPin(this.selectedConversation.id)
        this.selectedConversation.isPinned = !this.selectedConversation.isPinned
        
        toast.success(this.selectedConversation.isPinned ? '已置顶' : '已取消置顶')
        this.showMenuModal = false
      } catch (error) {
        console.error('操作失败:', error)
        toast.error('操作失败')
      }
    },
    
    /**
     * 切换免打扰状态
     */
    async toggleMute() {
      if (!this.selectedConversation) return
      
      try {
        await this.socialStore.toggleConversationMute(this.selectedConversation.id)
        this.selectedConversation.isMuted = !this.selectedConversation.isMuted
        
        toast.success(this.selectedConversation.isMuted ? '已开启免打扰' : '已关闭免打扰')
        this.showMenuModal = false
      } catch (error) {
        console.error('操作失败:', error)
        toast.error('操作失败')
      }
    },
    
    /**
     * 标记为已读
     */
    async markAsRead() {
      if (!this.selectedConversation) return
      
      try {
        await this.socialStore.markConversationRead(this.selectedConversation.id)
        this.selectedConversation.unreadCount = 0
        
        toast.success('已标记为已读')
        this.showMenuModal = false
      } catch (error) {
        console.error('操作失败:', error)
        toast.error('操作失败')
      }
    },
    
    /**
     * 删除对话
     */
    deleteConversation() {
      if (!this.selectedConversation) return
      
      uni.showModal({
        title: '确认删除',
        content: '确定要删除这个对话吗？删除后无法恢复。',
        confirmText: '删除',
        cancelText: '取消',
        success: async (res) => {
          if (res.confirm) {
            try {
              await this.socialStore.deleteConversation(this.selectedConversation.id)
              
              // 从列表中移除
              const index = this.conversationList.findIndex(c => c.id === this.selectedConversation.id)
              if (index > -1) {
                this.conversationList.splice(index, 1)
              }
              
              toast.success('删除成功')
              this.showMenuModal = false
            } catch (error) {
              console.error('删除失败:', error)
              toast.error('删除失败')
            }
          }
        }
      })
    },
    
    /**
     * 获取最后一条消息内容
     */
    getLastMessageContent(message) {
      if (!message) return '暂无消息'
      
      switch (message.type) {
        case 'text':
          return message.content
        case 'image':
          return '[图片]'
        case 'blessing':
          return '[赐福消息]'
        default:
          return '[消息]'
      }
    },
    
    /**
     * 获取空状态文本
     */
    getEmptyText() {
      const textMap = {
        chat: '暂无聊天记录',
        system: '暂无系统通知',
        blessing: '暂无赐福通知'
      }
      return textMap[this.messageType] || '暂无消息'
    },
    
    /**
     * 获取空状态描述
     */
    getEmptyDesc() {
      const descMap = {
        chat: '去社区认识新朋友吧',
        system: '系统通知会在这里显示',
        blessing: '收到的赐福通知会在这里显示'
      }
      return descMap[this.messageType] || ''
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      return timeUtils.relativeTime(timestamp)
    },
    
    /**
     * 页面跳转
     */
    goBack() {
      navigation.navigateBack()
    },
    
    goToChat(conversation) {
      navigation.navigateTo('/pages/social/chat', { 
        conversationId: conversation.id,
        userId: conversation.user.id
      })
    },
    
    goToCommunity() {
      navigation.switchTab('/pages/wisher/wish-plaza')
    }
  }
}
</script>

<style lang="scss" scoped>
.messages-page {
  height: 100vh;
  background-color: $wish-bg-primary;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $wish-spacing-md;
}

.navbar-back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
}

.navbar-title {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.navbar-action {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 消息类型切换 */
.message-tabs {
  display: flex;
  align-items: center;
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-sm $wish-spacing-md;
  border-bottom: 2rpx solid $wish-border-light;
}

.tab-item {
  position: relative;
  padding: $wish-spacing-xs $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-right: $wish-spacing-md;
  transition: all 0.3s ease;

  &--active {
    background-color: $wish-color-primary;

    .tab-text {
      color: $wish-text-inverse;
    }
  }
}

.tab-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

.tab-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background-color: $wish-color-error;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.badge-text {
  font-size: $wish-font-xs;
  color: $wish-text-inverse;
  font-weight: 500;
}

/* 对话列表 */
.conversation-list {
  flex: 1;
}

.conversation-items {
  display: flex;
  flex-direction: column;
}

.conversation-item {
  display: flex;
  align-items: center;
  padding: $wish-spacing-md;
  border-bottom: 2rpx solid $wish-border-light;
  background-color: $wish-bg-secondary;
  transition: background-color 0.3s ease;

  &:active {
    background-color: $wish-bg-primary;
  }
}

.avatar-container {
  position: relative;
  margin-right: $wish-spacing-md;
}

.user-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
}

.online-indicator {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 24rpx;
  height: 24rpx;
  background-color: $wish-color-success;
  border: 4rpx solid $wish-bg-secondary;
  border-radius: 50%;
}

.unread-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background-color: $wish-color-error;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.unread-text {
  font-size: $wish-font-xs;
  color: $wish-text-inverse;
  font-weight: 500;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $wish-spacing-xs;
}

.user-nickname {
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.message-time {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  flex-shrink: 0;
}

.last-message {
  display: flex;
  align-items: center;
}

.message-content {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.message-type-icon {
  margin-left: $wish-spacing-xs;
  flex-shrink: 0;
}

.type-icon {
  width: 24rpx;
  height: 24rpx;
}

.conversation-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: $wish-spacing-xs;
  margin-left: $wish-spacing-sm;
}

.pin-icon,
.mute-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 加载状态 */
.load-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.load-text {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
}

.no-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.no-more-text {
  font-size: $wish-font-sm;
  color: $wish-text-disabled;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $wish-spacing-xxl;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: $wish-spacing-lg;
  opacity: 0.6;
}

.empty-text {
  font-size: $wish-font-lg;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.empty-desc {
  font-size: $wish-font-md;
  color: $wish-text-disabled;
  margin-bottom: $wish-spacing-xl;
}

.empty-button {
  width: 300rpx;
}

/* 搜索模态框 */
.search-content {
  padding: $wish-spacing-md 0;
}

.search-input {
  margin-bottom: $wish-spacing-md;
}

.search-results {
  max-height: 600rpx;
  overflow-y: auto;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
  transition: background-color 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    background-color: $wish-bg-primary;
  }
}

.result-avatar {
  width: 64rpx;
  height: 64rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-md;
}

.result-info {
  flex: 1;
  min-width: 0;
}

.result-nickname {
  display: block;
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-xs;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-desc {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-empty {
  text-align: center;
  padding: $wish-spacing-xxl;
}

.search-empty-text {
  font-size: $wish-font-md;
  color: $wish-text-disabled;
}

/* 对话操作菜单 */
.menu-actions {
  padding: $wish-spacing-md 0;
}

.menu-action {
  display: flex;
  align-items: center;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-sm;
  transition: background-color 0.3s ease;

  &:last-child {
    margin-bottom: 0;
  }

  &:active {
    background-color: $wish-bg-primary;
  }
}

.menu-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: $wish-spacing-md;
}

.menu-text {
  font-size: $wish-font-md;
  color: $wish-text-primary;
}
</style>
