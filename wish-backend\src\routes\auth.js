/**
 * 认证路由
 * 处理用户注册、登录、验证等认证相关功能
 */

const express = require('express');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const User = require('../models/User');
const authMiddleware = require('../middleware/auth');
const { sendVerificationEmail, sendSMSCode } = require('../services/notification');
const { generateVerificationCode } = require('../utils/crypto');
const logger = require('../utils/logger');

const router = express.Router();

// 登录速率限制
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次尝试
  message: {
    error: '登录尝试过于频繁，请15分钟后再试',
    code: 'LOGIN_RATE_LIMIT'
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 注册速率限制
const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 最多3次注册
  message: {
    error: '注册过于频繁，请1小时后再试',
    code: 'REGISTER_RATE_LIMIT'
  }
});

/**
 * 用户注册
 */
router.post('/register', registerLimiter, [
  body('username')
    .isLength({ min: 3, max: 20 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名长度3-20位，只能包含字母、数字和下划线'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请输入有效的邮箱地址'),
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码至少8位，包含大小写字母和数字'),
  body('nickname')
    .isLength({ min: 1, max: 50 })
    .withMessage('昵称长度1-50位')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        code: 'VALIDATION_ERROR',
        details: errors.array()
      });
    }

    const { username, email, password, nickname, phone } = req.body;

    // 检查用户是否已存在
    const existingUser = await User.findOne({
      $or: [
        { username },
        { email },
        ...(phone ? [{ phone }] : [])
      ]
    });

    if (existingUser) {
      let field = 'username';
      if (existingUser.email === email) field = 'email';
      if (existingUser.phone === phone) field = 'phone';
      
      return res.status(409).json({
        error: `该${field === 'username' ? '用户名' : field === 'email' ? '邮箱' : '手机号'}已被注册`,
        code: 'USER_EXISTS',
        field
      });
    }

    // 创建新用户
    const user = new User({
      username,
      email,
      password,
      phone,
      profile: {
        nickname
      }
    });

    await user.save();

    // 生成邮箱验证码
    const verificationToken = generateVerificationCode();
    user.verification.email.token = verificationToken;
    user.verification.email.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时
    await user.save();

    // 发送验证邮件
    try {
      await sendVerificationEmail(email, verificationToken, nickname);
    } catch (error) {
      logger.error('发送验证邮件失败:', error);
    }

    // 生成JWT令牌
    const token = user.generateAuthToken();
    const refreshToken = user.generateRefreshToken();

    // 记录注册日志
    logger.info(`用户注册成功: ${username} (${email})`);

    res.status(201).json({
      message: '注册成功',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        profile: user.profile,
        emailVerified: user.verification.email.verified
      },
      tokens: {
        accessToken: token,
        refreshToken,
        expiresIn: '7d'
      }
    });

  } catch (error) {
    logger.error('用户注册失败:', error);
    res.status(500).json({
      error: '注册失败，请稍后重试',
      code: 'REGISTER_ERROR'
    });
  }
});

/**
 * 用户登录
 */
router.post('/login', loginLimiter, [
  body('identifier')
    .notEmpty()
    .withMessage('请输入用户名、邮箱或手机号'),
  body('password')
    .notEmpty()
    .withMessage('请输入密码')
], async (req, res) => {
  try {
    // 验证输入
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        code: 'VALIDATION_ERROR',
        details: errors.array()
      });
    }

    const { identifier, password } = req.body;
    const clientIP = req.ip || req.connection.remoteAddress;

    // 查找并验证用户
    const user = await User.findByCredentials(identifier, password);

    // 更新最后登录信息
    await user.updateLastLogin(clientIP);

    // 生成JWT令牌
    const token = user.generateAuthToken();
    const refreshToken = user.generateRefreshToken();

    // 记录登录日志
    logger.info(`用户登录成功: ${user.username} (${clientIP})`);

    res.json({
      message: '登录成功',
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        profile: user.profile,
        points: user.points,
        level: user.level,
        emailVerified: user.verification.email.verified,
        phoneVerified: user.verification.phone.verified
      },
      tokens: {
        accessToken: token,
        refreshToken,
        expiresIn: '7d'
      }
    });

  } catch (error) {
    logger.error('用户登录失败:', error);
    
    if (error.message.includes('用户不存在') || 
        error.message.includes('密码错误') ||
        error.message.includes('账户已被锁定') ||
        error.message.includes('账户已被禁用')) {
      return res.status(401).json({
        error: error.message,
        code: 'LOGIN_FAILED'
      });
    }

    res.status(500).json({
      error: '登录失败，请稍后重试',
      code: 'LOGIN_ERROR'
    });
  }
});

/**
 * 刷新令牌
 */
router.post('/refresh', [
  body('refreshToken')
    .notEmpty()
    .withMessage('刷新令牌不能为空')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        code: 'VALIDATION_ERROR',
        details: errors.array()
      });
    }

    const { refreshToken } = req.body;

    // 验证刷新令牌
    const jwt = require('jsonwebtoken');
    const config = require('../config');
    
    const decoded = jwt.verify(refreshToken, config.jwt.secret);
    
    if (decoded.type !== 'refresh') {
      return res.status(401).json({
        error: '无效的刷新令牌',
        code: 'INVALID_REFRESH_TOKEN'
      });
    }

    // 查找用户
    const user = await User.findById(decoded.userId);
    if (!user || user.status !== 'active') {
      return res.status(401).json({
        error: '用户不存在或已被禁用',
        code: 'USER_NOT_FOUND'
      });
    }

    // 生成新的访问令牌
    const newAccessToken = user.generateAuthToken();

    res.json({
      message: '令牌刷新成功',
      tokens: {
        accessToken: newAccessToken,
        expiresIn: '7d'
      }
    });

  } catch (error) {
    logger.error('令牌刷新失败:', error);
    
    if (error.name === 'JsonWebTokenError' || error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: '无效或过期的刷新令牌',
        code: 'INVALID_REFRESH_TOKEN'
      });
    }

    res.status(500).json({
      error: '令牌刷新失败',
      code: 'REFRESH_ERROR'
    });
  }
});

/**
 * 发送邮箱验证码
 */
router.post('/send-email-verification', authMiddleware.required, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId);
    
    if (user.verification.email.verified) {
      return res.status(400).json({
        error: '邮箱已验证',
        code: 'EMAIL_ALREADY_VERIFIED'
      });
    }

    // 生成验证码
    const verificationToken = generateVerificationCode();
    user.verification.email.token = verificationToken;
    user.verification.email.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);
    await user.save();

    // 发送验证邮件
    await sendVerificationEmail(user.email, verificationToken, user.profile.nickname);

    res.json({
      message: '验证邮件已发送',
      expiresIn: '24小时'
    });

  } catch (error) {
    logger.error('发送邮箱验证码失败:', error);
    res.status(500).json({
      error: '发送验证邮件失败',
      code: 'SEND_EMAIL_ERROR'
    });
  }
});

/**
 * 验证邮箱
 */
router.post('/verify-email', [
  body('token')
    .notEmpty()
    .withMessage('验证码不能为空')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: '输入验证失败',
        code: 'VALIDATION_ERROR',
        details: errors.array()
      });
    }

    const { token } = req.body;

    // 查找用户
    const user = await User.findOne({
      'verification.email.token': token,
      'verification.email.expiresAt': { $gt: new Date() }
    });

    if (!user) {
      return res.status(400).json({
        error: '验证码无效或已过期',
        code: 'INVALID_VERIFICATION_TOKEN'
      });
    }

    // 验证邮箱
    user.verification.email.verified = true;
    user.verification.email.token = undefined;
    user.verification.email.expiresAt = undefined;
    await user.save();

    // 奖励心愿力
    await user.addPoints('wishPower', 10);

    logger.info(`邮箱验证成功: ${user.email}`);

    res.json({
      message: '邮箱验证成功',
      reward: {
        wishPower: 10
      }
    });

  } catch (error) {
    logger.error('邮箱验证失败:', error);
    res.status(500).json({
      error: '邮箱验证失败',
      code: 'VERIFY_EMAIL_ERROR'
    });
  }
});

/**
 * 登出
 */
router.post('/logout', authMiddleware.required, async (req, res) => {
  try {
    // 这里可以实现令牌黑名单机制
    // 目前简单返回成功
    
    logger.info(`用户登出: ${req.user.username}`);
    
    res.json({
      message: '登出成功'
    });

  } catch (error) {
    logger.error('用户登出失败:', error);
    res.status(500).json({
      error: '登出失败',
      code: 'LOGOUT_ERROR'
    });
  }
});

/**
 * 获取当前用户信息
 */
router.get('/me', authMiddleware.required, async (req, res) => {
  try {
    const user = await User.findById(req.user.userId)
      .select('-password -security -verification');

    if (!user) {
      return res.status(404).json({
        error: '用户不存在',
        code: 'USER_NOT_FOUND'
      });
    }

    res.json({
      user
    });

  } catch (error) {
    logger.error('获取用户信息失败:', error);
    res.status(500).json({
      error: '获取用户信息失败',
      code: 'GET_USER_ERROR'
    });
  }
});

module.exports = router;
