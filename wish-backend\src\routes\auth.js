/**
 * 认证路由
 * 处理用户注册、登录、令牌刷新等认证相关功能
 */

const express = require('express');
const { body, validationResult } = require('express-validator');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { getModel } = require('../models');
const authMiddleware = require('../middleware/auth');
const { AppError, catchAsync, handleValidationErrors } = require('../middleware/errorHandler');
const { Cache } = require('../utils/redis');
const logger = require('../utils/logger');
const config = require('../config');

const router = express.Router();

/**
 * 认证模块根路由
 */
router.get('/', (req, res) => {
  res.json({
    module: '认证模块',
    endpoints: {
      register: 'POST /api/auth/register',
      login: 'POST /api/auth/login',
      refresh: 'POST /api/auth/refresh',
      me: 'GET /api/auth/me',
      logout: 'POST /api/auth/logout',
      sendEmailVerification: 'POST /api/auth/send-email-verification',
      verifyEmail: 'POST /api/auth/verify-email'
    }
  });
});

/**
 * 生成验证码
 */
function generateVerificationCode() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

/**
 * 用户注册
 */
router.post('/register', [
  body('username')
    .isLength({ min: 3, max: 20 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名长度3-20位，只能包含字母、数字和下划线'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请提供有效的邮箱地址'),
  body('password')
    .isLength({ min: 8 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('密码至少8位，包含大小写字母和数字'),
  body('nickname')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('昵称长度1-50字符'),
  body('phone')
    .optional()
    .matches(/^1[3-9]\d{9}$/)
    .withMessage('请提供有效的手机号码')
], handleValidationErrors, catchAsync(async (req, res) => {
  const { username, email, password, nickname, phone } = req.body;
  const User = getModel('User');

  // 检查用户是否已存在
  const { Op } = require('sequelize');
  const existingUser = await User.findOne({
    where: {
      [Op.or]: [
        { username },
        { email },
        ...(phone ? [{ phone }] : [])
      ]
    }
  });

  if (existingUser) {
    if (existingUser.username === username) {
      throw new AppError('用户名已被使用', 409, 'USERNAME_EXISTS');
    }
    if (existingUser.email === email) {
      throw new AppError('邮箱已被注册', 409, 'EMAIL_EXISTS');
    }
    if (existingUser.phone === phone) {
      throw new AppError('手机号已被注册', 409, 'PHONE_EXISTS');
    }
  }

  // 创建新用户
  const user = await User.create({
    username,
    email,
    password,
    phone,
    nickname: nickname || username
  });

  // 生成邮箱验证码
  const verificationToken = generateVerificationCode();
  await user.update({
    email_verification_token: verificationToken,
    email_verification_expires: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24小时
  });

  // 生成JWT令牌
  const token = user.generateAuthToken();
  const refreshToken = user.generateRefreshToken();

  // 记录注册日志
  logger.info(`新用户注册: ${username} (${email})`);

  res.status(201).json({
    message: '注册成功',
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      nickname: user.nickname,
      emailVerified: user.email_verified
    },
    tokens: {
      accessToken: token,
      refreshToken,
      expiresIn: config.jwt.expiresIn
    }
  });
}));

/**
 * 用户登录
 */
router.post('/login', [
  body('identifier')
    .notEmpty()
    .withMessage('请提供用户名、邮箱或手机号'),
  body('password')
    .notEmpty()
    .withMessage('请提供密码')
], handleValidationErrors, catchAsync(async (req, res) => {
  const { identifier, password } = req.body;
  const clientIP = req.ip || req.connection.remoteAddress;
  const User = getModel('User');

  // 查找并验证用户
  const user = await User.findByCredentials(identifier, password);

  // 更新最后登录信息
  await user.updateLastLogin(clientIP);

  // 生成JWT令牌
  const token = user.generateAuthToken();
  const refreshToken = user.generateRefreshToken();

  // 记录登录日志
  logger.info(`用户登录: ${user.username} (${clientIP})`);

  res.json({
    message: '登录成功',
    user: {
      id: user.id,
      username: user.username,
      email: user.email,
      nickname: user.nickname,
      wishPowerPoints: user.wish_power_points,
      meritPoints: user.merit_points,
      level: user.level,
      emailVerified: user.email_verified,
      phoneVerified: user.phone_verified
    },
    tokens: {
      accessToken: token,
      refreshToken,
      expiresIn: config.jwt.expiresIn
    }
  });
}));

/**
 * 刷新令牌
 */
router.post('/refresh', [
  body('refreshToken')
    .notEmpty()
    .withMessage('请提供刷新令牌')
], handleValidationErrors, catchAsync(async (req, res) => {
  const { refreshToken } = req.body;

  try {
    // 验证刷新令牌
    const decoded = jwt.verify(refreshToken, config.jwt.secret, {
      issuer: config.jwt.issuer,
      audience: config.jwt.audience
    });

    if (decoded.type !== 'refresh') {
      throw new AppError('无效的刷新令牌', 401, 'INVALID_REFRESH_TOKEN');
    }

    // 查找用户
    const User = getModel('User');
    const user = await User.findByPk(decoded.userId);
    if (!user || user.status !== 'active') {
      throw new AppError('用户不存在或已被禁用', 401, 'USER_NOT_FOUND');
    }

    // 生成新的访问令牌
    const newAccessToken = user.generateAuthToken();
    const newRefreshToken = user.generateRefreshToken();

    res.json({
      message: '令牌刷新成功',
      tokens: {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
        expiresIn: config.jwt.expiresIn
      }
    });

  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new AppError('刷新令牌已过期，请重新登录', 401, 'REFRESH_TOKEN_EXPIRED');
    } else if (error.name === 'JsonWebTokenError') {
      throw new AppError('无效的刷新令牌', 401, 'INVALID_REFRESH_TOKEN');
    }
    throw error;
  }
}));

/**
 * 发送邮箱验证码
 */
router.post('/send-email-verification', authMiddleware.required, catchAsync(async (req, res) => {
  const User = getModel('User');
  const user = await User.findByPk(req.user.userId);
  
  if (user.email_verified) {
    throw new AppError('邮箱已验证', 400, 'EMAIL_ALREADY_VERIFIED');
  }

  // 生成验证码
  const verificationToken = generateVerificationCode();
  await user.update({
    email_verification_token: verificationToken,
    email_verification_expires: new Date(Date.now() + 24 * 60 * 60 * 1000)
  });

  // TODO: 发送邮件验证码
  logger.info(`发送邮箱验证码: ${user.email} - ${verificationToken}`);

  res.json({
    message: '验证码已发送到您的邮箱'
  });
}));

/**
 * 验证邮箱
 */
router.post('/verify-email', [
  body('token')
    .isLength({ min: 6, max: 6 })
    .isNumeric()
    .withMessage('请提供6位数字验证码')
], handleValidationErrors, catchAsync(async (req, res) => {
  const { token } = req.body;

  // 查找用户
  const User = getModel('User');
  const user = await User.findOne({
    where: {
      email_verification_token: token,
      email_verification_expires: { [require('sequelize').Op.gt]: new Date() }
    }
  });

  if (!user) {
    throw new AppError('验证码无效或已过期', 400, 'INVALID_VERIFICATION_TOKEN');
  }

  // 验证邮箱
  await user.update({
    email_verified: true,
    email_verification_token: null,
    email_verification_expires: null
  });

  // 奖励心愿力
  await user.addPoints('wishPower', 10);

  logger.info(`邮箱验证成功: ${user.email}`);

  res.json({
    message: '邮箱验证成功',
    reward: {
      wishPower: 10
    }
  });
}));

/**
 * 获取当前用户信息
 */
router.get('/me', authMiddleware.required, catchAsync(async (req, res) => {
  const User = getModel('User');
  const user = await User.findByPk(req.user.userId, {
    attributes: { exclude: ['password', 'email_verification_token', 'phone_verification_code'] }
  });

  if (!user) {
    throw new AppError('用户不存在', 404, 'USER_NOT_FOUND');
  }

  res.json({
    user
  });
}));

/**
 * 用户登出
 */
router.post('/logout', authMiddleware.required, catchAsync(async (req, res) => {
  // TODO: 将令牌加入黑名单（如果需要）
  
  logger.info(`用户登出: ${req.user.username}`);

  res.json({
    message: '登出成功'
  });
}));

module.exports = router;
