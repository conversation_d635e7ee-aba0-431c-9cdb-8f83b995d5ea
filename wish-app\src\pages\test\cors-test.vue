<template>
  <view class="cors-test-page">
    <view class="header">
      <text class="title">CORS连接测试</text>
      <text class="subtitle">测试前端与后端的跨域请求</text>
    </view>
    
    <view class="info-section">
      <view class="info-item">
        <text class="label">前端地址:</text>
        <text class="value">{{ frontendURL }}</text>
      </view>
      <view class="info-item">
        <text class="label">后端地址:</text>
        <text class="value">{{ backendURL }}</text>
      </view>
    </view>
    
    <view class="test-section">
      <button class="test-btn" @click="runCORSTest" :disabled="testing">
        {{ testing ? '测试中...' : '开始CORS测试' }}
      </button>
      
      <button class="clear-btn" @click="clearResults" :disabled="testing">
        清除结果
      </button>
    </view>
    
    <view class="results-section" v-if="testResults.length > 0">
      <view class="summary">
        <text class="summary-title">测试结果</text>
        <view class="summary-stats">
          <text :class="['status', allPassed ? 'success' : 'error']">
            {{ allPassed ? '✅ 全部通过' : '❌ 部分失败' }}
          </text>
        </view>
      </view>
      
      <view class="results-list">
        <view 
          class="result-item" 
          v-for="(result, index) in testResults" 
          :key="index"
          :class="{ success: result.success, failed: !result.success }"
        >
          <view class="result-header">
            <text class="result-name">{{ result.name }}</text>
            <text class="result-status">{{ result.success ? '✅' : '❌' }}</text>
          </view>
          <view class="result-details" v-if="!result.success">
            <text class="error-text">{{ result.error }}</text>
            <text class="error-details" v-if="result.details">{{ result.details }}</text>
          </view>
          <view class="result-details" v-else-if="result.data">
            <text class="success-text">{{ JSON.stringify(result.data, null, 2) }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <view class="logs-section" v-if="logs.length > 0">
      <text class="logs-title">测试日志</text>
      <view class="logs-list">
        <text 
          class="log-item" 
          v-for="(log, index) in logs" 
          :key="index"
        >
          {{ log }}
        </text>
      </view>
    </view>
    
    <view class="help-section">
      <text class="help-title">常见问题解决方案</text>
      <view class="help-item">
        <text class="help-question">❌ 简单请求失败</text>
        <text class="help-answer">检查后端服务是否正常运行 (http://localhost:3000/health)</text>
      </view>
      <view class="help-item">
        <text class="help-question">❌ 预检请求失败</text>
        <text class="help-answer">检查后端CORS配置是否包含前端域名</text>
      </view>
      <view class="help-item">
        <text class="help-question">❌ 复杂请求失败</text>
        <text class="help-answer">检查CORS credentials和headers配置</text>
      </view>
    </view>
  </view>
</template>

<script>
import { APP_CONFIG } from '@/config'

export default {
  name: 'CORSTest',
  data() {
    return {
      testing: false,
      testResults: [],
      logs: [],
      allPassed: false,
      frontendURL: '',
      backendURL: APP_CONFIG.baseURL.replace('/api', '')
    }
  },
  
  mounted() {
    // 获取当前前端地址
    this.frontendURL = window.location.origin
  },
  
  methods: {
    async runCORSTest() {
      this.testing = true
      this.logs = []
      this.testResults = []
      
      try {
        // 重写console.log来捕获日志
        const originalLog = console.log
        console.log = (...args) => {
          this.logs.push(args.join(' '))
          originalLog.apply(console, args)
        }
        
        // 运行CORS测试
        await this.testSimpleRequest()
        await this.testPreflightRequest()
        await this.testComplexRequest()
        
        // 恢复console.log
        console.log = originalLog
        
        // 检查是否全部通过
        this.allPassed = this.testResults.every(result => result.success)
        
      } catch (error) {
        console.error('CORS测试失败:', error)
        uni.showToast({
          title: 'CORS测试失败',
          icon: 'error'
        })
      } finally {
        this.testing = false
      }
    },
    
    async testSimpleRequest() {
      try {
        console.log('🧪 测试简单GET请求...')
        
        const response = await uni.request({
          url: `${this.backendURL}/health`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          }
        })
        
        if (response.statusCode === 200) {
          console.log('✅ 简单GET请求成功:', response.data)
          this.testResults.push({
            name: '简单GET请求',
            success: true,
            data: response.data
          })
        } else {
          console.log('❌ 简单GET请求失败:', response.statusCode)
          this.testResults.push({
            name: '简单GET请求',
            success: false,
            error: `HTTP ${response.statusCode}`
          })
        }
      } catch (error) {
        console.log('❌ 简单GET请求异常:', error.message)
        this.testResults.push({
          name: '简单GET请求',
          success: false,
          error: error.message
        })
      }
    },
    
    async testPreflightRequest() {
      try {
        console.log('🧪 测试预检OPTIONS请求...')
        
        // uni.request不直接支持OPTIONS，我们通过复杂请求来触发预检
        const response = await uni.request({
          url: `${this.backendURL}/api`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json',
            'X-Custom-Header': 'test'
          }
        })
        
        if (response.statusCode === 200) {
          console.log('✅ 预检请求成功 (通过复杂请求验证)')
          this.testResults.push({
            name: '预检OPTIONS请求',
            success: true,
            data: '预检请求正常处理'
          })
        } else {
          console.log('❌ 预检请求失败:', response.statusCode)
          this.testResults.push({
            name: '预检OPTIONS请求',
            success: false,
            error: `HTTP ${response.statusCode}`
          })
        }
      } catch (error) {
        console.log('❌ 预检请求异常:', error.message)
        this.testResults.push({
          name: '预检OPTIONS请求',
          success: false,
          error: error.message
        })
      }
    },
    
    async testComplexRequest() {
      try {
        console.log('🧪 测试复杂请求...')
        
        const response = await uni.request({
          url: `${this.backendURL}/api/users/checkin-status`,
          method: 'GET',
          header: {
            'Content-Type': 'application/json'
          }
        })
        
        // 即使返回401也说明CORS工作正常，只是需要认证
        if (response.statusCode === 200 || response.statusCode === 401) {
          console.log('✅ 复杂请求成功 (CORS正常):', response.statusCode)
          this.testResults.push({
            name: '复杂请求',
            success: true,
            data: response.statusCode === 401 ? '需要认证 (CORS正常)' : response.data
          })
        } else {
          console.log('❌ 复杂请求失败:', response.statusCode)
          this.testResults.push({
            name: '复杂请求',
            success: false,
            error: `HTTP ${response.statusCode}`
          })
        }
      } catch (error) {
        console.log('❌ 复杂请求异常:', error.message)
        this.testResults.push({
          name: '复杂请求',
          success: false,
          error: error.message
        })
      }
    },
    
    clearResults() {
      this.testResults = []
      this.logs = []
      this.allPassed = false
    }
  }
}
</script>

<style lang="scss" scoped>
.cors-test-page {
  padding: $wish-spacing-lg;
  background-color: $wish-bg-primary;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: $wish-spacing-lg;
  
  .title {
    font-size: $wish-font-xl;
    font-weight: 600;
    color: $wish-text-primary;
    display: block;
    margin-bottom: $wish-spacing-xs;
  }
  
  .subtitle {
    font-size: $wish-font-sm;
    color: $wish-text-secondary;
  }
}

.info-section {
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-lg;
  
  .info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: $wish-spacing-xs;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .label {
      font-weight: 500;
      color: $wish-text-primary;
    }
    
    .value {
      color: $wish-text-secondary;
      font-family: monospace;
      font-size: $wish-font-sm;
    }
  }
}

.test-section {
  display: flex;
  gap: $wish-spacing-md;
  margin-bottom: $wish-spacing-lg;
  
  .test-btn, .clear-btn {
    flex: 1;
    padding: $wish-spacing-md;
    border-radius: $wish-radius-md;
    border: none;
    font-size: $wish-font-md;
    font-weight: 500;
  }
  
  .test-btn {
    background-color: $wish-primary;
    color: white;
    
    &:disabled {
      background-color: $wish-text-muted;
    }
  }
  
  .clear-btn {
    background-color: $wish-bg-secondary;
    color: $wish-text-primary;
  }
}

.results-section {
  margin-bottom: $wish-spacing-lg;
}

.summary {
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-bottom: $wish-spacing-md;
  
  .summary-title {
    font-size: $wish-font-lg;
    font-weight: 600;
    color: $wish-text-primary;
    display: block;
    margin-bottom: $wish-spacing-sm;
  }
  
  .status {
    font-size: $wish-font-md;
    font-weight: 500;
    
    &.success {
      color: $wish-success;
    }
    
    &.error {
      color: $wish-error;
    }
  }
}

.results-list {
  .result-item {
    background-color: $wish-bg-secondary;
    padding: $wish-spacing-md;
    border-radius: $wish-radius-md;
    margin-bottom: $wish-spacing-sm;
    border-left: 4px solid transparent;
    
    &.success {
      border-left-color: $wish-success;
    }
    
    &.failed {
      border-left-color: $wish-error;
    }
  }
  
  .result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: $wish-spacing-xs;
    
    .result-name {
      font-weight: 500;
      color: $wish-text-primary;
    }
    
    .result-status {
      font-size: $wish-font-lg;
    }
  }
  
  .result-details {
    .error-text {
      color: $wish-error;
      font-size: $wish-font-sm;
      display: block;
      margin-bottom: $wish-spacing-xs;
    }
    
    .error-details {
      color: $wish-text-muted;
      font-size: $wish-font-xs;
      font-family: monospace;
    }
    
    .success-text {
      color: $wish-success;
      font-size: $wish-font-sm;
      font-family: monospace;
    }
  }
}

.logs-section {
  margin-bottom: $wish-spacing-lg;
  
  .logs-title {
    font-size: $wish-font-lg;
    font-weight: 600;
    color: $wish-text-primary;
    display: block;
    margin-bottom: $wish-spacing-sm;
  }
  
  .logs-list {
    background-color: #1a1a1a;
    padding: $wish-spacing-md;
    border-radius: $wish-radius-md;
    max-height: 400rpx;
    overflow-y: auto;
    
    .log-item {
      display: block;
      color: #00ff00;
      font-family: monospace;
      font-size: $wish-font-sm;
      line-height: 1.4;
      margin-bottom: $wish-spacing-xs;
    }
  }
}

.help-section {
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-md;
  border-radius: $wish-radius-md;
  
  .help-title {
    font-size: $wish-font-lg;
    font-weight: 600;
    color: $wish-text-primary;
    display: block;
    margin-bottom: $wish-spacing-md;
  }
  
  .help-item {
    margin-bottom: $wish-spacing-md;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .help-question {
      font-weight: 500;
      color: $wish-text-primary;
      display: block;
      margin-bottom: $wish-spacing-xs;
    }
    
    .help-answer {
      color: $wish-text-secondary;
      font-size: $wish-font-sm;
      line-height: 1.4;
    }
  }
}
</style>
