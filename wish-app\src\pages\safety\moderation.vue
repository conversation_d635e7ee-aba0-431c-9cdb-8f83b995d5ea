<!--
  愿境内容审核管理页面
  管理员审核举报内容和处理违规行为
-->
<template>
  <view class="moderation-page">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar safe-area-top">
      <view class="navbar-content">
        <view class="navbar-back" @click="goBack">
          <image src="/static/icons/arrow-left.png" class="back-icon" />
        </view>
        <text class="navbar-title">内容审核</text>
        <view class="navbar-actions">
          <view class="navbar-action" @click="showFilterModal = true">
            <image src="/static/icons/filter.png" class="action-icon" />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 审核统计 -->
    <view class="moderation-stats">
      <view class="stat-item">
        <text class="stat-number">{{ stats.pending || 0 }}</text>
        <text class="stat-label">待处理</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.processed || 0 }}</text>
        <text class="stat-label">已处理</text>
      </view>
      <view class="stat-divider"></view>
      <view class="stat-item">
        <text class="stat-number">{{ stats.today || 0 }}</text>
        <text class="stat-label">今日处理</text>
      </view>
    </view>
    
    <!-- 状态筛选 -->
    <view class="status-filter">
      <view 
        class="filter-item"
        :class="{ 'filter-item--active': statusFilter === status.value }"
        v-for="status in statusOptions"
        :key="status.value"
        @click="changeStatusFilter(status.value)"
      >
        <text class="filter-text">{{ status.label }}</text>
        <view v-if="status.count > 0" class="filter-badge">
          <text class="badge-text">{{ status.count }}</text>
        </view>
      </view>
    </view>
    
    <!-- 举报列表 -->
    <scroll-view 
      class="report-list"
      scroll-y
      @scrolltolower="loadMore"
      refresher-enabled
      :refresher-triggered="refreshing"
      @refresherrefresh="onRefresh"
    >
      <view class="report-items">
        <view 
          v-for="report in reportList"
          :key="report.id"
          class="report-item"
          @click="goToReportDetail(report.id)"
        >
          <!-- 举报状态标识 -->
          <view class="report-status">
            <view 
              class="status-dot"
              :class="`status-dot--${report.status}`"
            ></view>
            <text class="status-text">{{ getStatusText(report.status) }}</text>
          </view>
          
          <!-- 举报信息 -->
          <view class="report-info">
            <view class="report-header">
              <text class="report-type">{{ getReportTypeText(report.type) }}</text>
              <text class="report-time">{{ formatTime(report.createdAt) }}</text>
            </view>
            
            <text class="report-reason">{{ getReasonText(report.reason) }}</text>
            
            <!-- 举报对象预览 -->
            <view class="report-target">
              <view v-if="report.targetType === 'user'" class="target-user">
                <image :src="report.target.avatar" class="target-avatar" />
                <text class="target-name">{{ report.target.nickname }}</text>
              </view>
              
              <view v-else class="target-content">
                <text class="target-preview">{{ getTargetPreview(report.target) }}</text>
              </view>
            </view>
            
            <!-- 举报者信息 -->
            <view class="reporter-info">
              <text class="reporter-label">举报者：</text>
              <text class="reporter-name">{{ report.reporter.nickname }}</text>
            </view>
          </view>
          
          <!-- 快速操作 -->
          <view class="report-actions" v-if="report.status === 'pending'">
            <wish-button
              type="success"
              size="small"
              text="通过"
              @click.stop="quickApprove(report.id)"
              class="action-button"
            />
            <wish-button
              type="error"
              size="small"
              text="违规"
              @click.stop="quickReject(report.id)"
              class="action-button"
            />
          </view>
        </view>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more" v-if="hasMore">
        <wish-loading v-if="loading" type="dots" text="加载中..." />
        <text v-else class="load-text" @click="loadMore">点击加载更多</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" v-else-if="reportList.length > 0">
        <text class="no-more-text">没有更多举报了</text>
      </view>
      
      <!-- 空状态 -->
      <view class="empty-state" v-if="!loading && reportList.length === 0">
        <image src="/static/icons/empty-reports.png" class="empty-icon" />
        <text class="empty-text">{{ getEmptyText() }}</text>
        <text class="empty-desc">{{ getEmptyDesc() }}</text>
      </view>
    </scroll-view>
    
    <!-- 筛选模态框 -->
    <wish-modal
      v-model:visible="showFilterModal"
      title="筛选条件"
      position="bottom"
    >
      <view class="filter-content">
        <!-- 举报类型 */
        <view class="filter-section">
          <text class="filter-title">举报类型</text>
          <view class="filter-options">
            <view 
              class="filter-option"
              :class="{ 'filter-option--active': typeFilter === type.value }"
              v-for="type in typeOptions"
              :key="type.value"
              @click="typeFilter = type.value"
            >
              <text class="option-text">{{ type.label }}</text>
            </view>
          </view>
        </view>
        
        <!-- 时间范围 -->
        <view class="filter-section">
          <text class="filter-title">时间范围</text>
          <view class="filter-options">
            <view 
              class="filter-option"
              :class="{ 'filter-option--active': timeFilter === time.value }"
              v-for="time in timeOptions"
              :key="time.value"
              @click="timeFilter = time.value"
            >
              <text class="option-text">{{ time.label }}</text>
            </view>
          </view>
        </view>
      </view>
      
      <template #footer>
        <view class="filter-actions">
          <wish-button
            type="ghost"
            text="重置"
            @click="resetFilter"
            class="filter-button"
          />
          <wish-button
            type="primary"
            text="确定"
            @click="applyFilter"
            class="filter-button"
          />
        </view>
      </template>
    </wish-modal>
  </view>
</template>

<script>
import { useSafetyStore } from '@/store'
import { timeUtils, navigation, toast } from '@/utils'

export default {
  data() {
    return {
      statusFilter: 'pending',
      typeFilter: 'all',
      timeFilter: 'all',
      loading: false,
      refreshing: false,
      showFilterModal: false,
      reportList: [],
      stats: {},
      statusOptions: [
        { value: 'pending', label: '待处理', count: 0 },
        { value: 'approved', label: '已通过', count: 0 },
        { value: 'rejected', label: '已违规', count: 0 },
        { value: 'all', label: '全部', count: 0 }
      ],
      typeOptions: [
        { value: 'all', label: '全部类型' },
        { value: 'user', label: '用户举报' },
        { value: 'wish', label: '心愿举报' },
        { value: 'comment', label: '评论举报' },
        { value: 'blessing', label: '赐福举报' }
      ],
      timeOptions: [
        { value: 'all', label: '全部时间' },
        { value: 'today', label: '今天' },
        { value: 'week', label: '本周' },
        { value: 'month', label: '本月' }
      ]
    }
  },
  
  computed: {
    safetyStore() {
      return useSafetyStore()
    },
    
    hasMore() {
      return this.safetyStore.pagination.hasMore
    }
  },
  
  onLoad() {
    this.initPage()
  },
  
  onShow() {
    this.refreshData()
  },
  
  methods: {
    /**
     * 初始化页面
     */
    async initPage() {
      await Promise.all([
        this.loadReports(true),
        this.loadStats()
      ])
    },
    
    /**
     * 刷新数据
     */
    async refreshData() {
      await Promise.all([
        this.loadReports(true),
        this.loadStats()
      ])
    },
    
    /**
     * 加载举报列表
     */
    async loadReports(refresh = false) {
      if (this.loading) return
      
      this.loading = true
      try {
        const params = {
          status: this.statusFilter === 'all' ? undefined : this.statusFilter,
          type: this.typeFilter === 'all' ? undefined : this.typeFilter,
          timeRange: this.timeFilter === 'all' ? undefined : this.timeFilter
        }
        
        const result = await this.safetyStore.fetchReports(params, refresh)
        
        if (refresh) {
          this.reportList = result.list || []
        } else {
          this.reportList.push(...(result.list || []))
        }
      } catch (error) {
        console.error('加载举报列表失败:', error)
        toast.error('加载失败，请重试')
      } finally {
        this.loading = false
      }
    },
    
    /**
     * 加载统计数据
     */
    async loadStats() {
      try {
        this.stats = await this.safetyStore.getModerationStats()
        
        // 更新状态选项的数量
        this.statusOptions.forEach(option => {
          option.count = this.stats[option.value] || 0
        })
      } catch (error) {
        console.error('加载统计数据失败:', error)
      }
    },
    
    /**
     * 加载更多
     */
    async loadMore() {
      if (!this.hasMore || this.loading) return
      await this.loadReports(false)
    },
    
    /**
     * 下拉刷新
     */
    async onRefresh() {
      this.refreshing = true
      try {
        await this.refreshData()
      } finally {
        this.refreshing = false
      }
    },
    
    /**
     * 改变状态筛选
     */
    async changeStatusFilter(status) {
      if (this.statusFilter === status) return
      
      this.statusFilter = status
      await this.loadReports(true)
    },
    
    /**
     * 快速通过
     */
    async quickApprove(reportId) {
      try {
        await this.safetyStore.processReport(reportId, 'approved', '内容正常，举报不成立')
        
        // 更新列表中的状态
        const report = this.reportList.find(r => r.id === reportId)
        if (report) {
          report.status = 'approved'
        }
        
        toast.success('已标记为通过')
      } catch (error) {
        console.error('处理举报失败:', error)
        toast.error('操作失败')
      }
    },
    
    /**
     * 快速拒绝
     */
    async quickReject(reportId) {
      try {
        await this.safetyStore.processReport(reportId, 'rejected', '内容违规，举报成立')
        
        // 更新列表中的状态
        const report = this.reportList.find(r => r.id === reportId)
        if (report) {
          report.status = 'rejected'
        }
        
        toast.success('已标记为违规')
      } catch (error) {
        console.error('处理举报失败:', error)
        toast.error('操作失败')
      }
    },
    
    /**
     * 应用筛选
     */
    async applyFilter() {
      this.showFilterModal = false
      await this.loadReports(true)
    },
    
    /**
     * 重置筛选
     */
    resetFilter() {
      this.typeFilter = 'all'
      this.timeFilter = 'all'
    },
    
    /**
     * 获取状态文本
     */
    getStatusText(status) {
      const statusMap = {
        pending: '待处理',
        approved: '已通过',
        rejected: '已违规'
      }
      return statusMap[status] || '未知'
    },
    
    /**
     * 获取举报类型文本
     */
    getReportTypeText(type) {
      const typeMap = {
        user: '用户举报',
        wish: '心愿举报',
        comment: '评论举报',
        blessing: '赐福举报'
      }
      return typeMap[type] || '其他举报'
    },
    
    /**
     * 获取原因文本
     */
    getReasonText(reason) {
      const reasonMap = {
        harassment: '骚扰他人',
        spam: '垃圾信息',
        fake: '虚假信息',
        inappropriate: '不当行为',
        inappropriate_content: '不当内容',
        hate_speech: '仇恨言论',
        false_info: '虚假信息',
        copyright: '版权侵犯',
        other: '其他原因'
      }
      return reasonMap[reason] || '其他原因'
    },
    
    /**
     * 获取目标预览
     */
    getTargetPreview(target) {
      const content = target.content || target.title || target.text || ''
      return content.length > 50 ? content.substring(0, 50) + '...' : content
    },
    
    /**
     * 获取空状态文本
     */
    getEmptyText() {
      if (this.statusFilter === 'pending') {
        return '暂无待处理举报'
      } else if (this.statusFilter === 'approved') {
        return '暂无已通过举报'
      } else if (this.statusFilter === 'rejected') {
        return '暂无已违规举报'
      }
      return '暂无举报记录'
    },
    
    /**
     * 获取空状态描述
     */
    getEmptyDesc() {
      return '社区环境良好，继续保持'
    },
    
    /**
     * 格式化时间
     */
    formatTime(timestamp) {
      return timeUtils.relativeTime(timestamp)
    },
    
    /**
     * 页面跳转
     */
    goBack() {
      navigation.navigateBack()
    },
    
    goToReportDetail(reportId) {
      navigation.navigateTo('/pages/safety/report-detail', { id: reportId })
    }
  }
}
</script>

<style lang="scss" scoped>
.moderation-page {
  height: 100vh;
  background-color: $wish-bg-primary;
  display: flex;
  flex-direction: column;
}

/* 自定义导航栏 */
.custom-navbar {
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.navbar-content {
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 $wish-spacing-md;
}

.navbar-back {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  width: 24rpx;
  height: 24rpx;
}

.navbar-title {
  font-size: $wish-font-lg;
  font-weight: 600;
  color: $wish-text-primary;
}

.navbar-actions {
  display: flex;
  align-items: center;
}

.navbar-action {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 审核统计 */
.moderation-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background-color: $wish-bg-secondary;
  padding: $wish-spacing-lg;
  border-bottom: 2rpx solid $wish-border-light;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.stat-number {
  font-size: $wish-font-xxl;
  font-weight: 600;
  color: $wish-color-primary;
  margin-bottom: $wish-spacing-xs;
}

.stat-label {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background-color: $wish-border-light;
}

/* 状态筛选 */
.status-filter {
  display: flex;
  align-items: center;
  padding: $wish-spacing-sm $wish-spacing-md;
  background-color: $wish-bg-secondary;
  border-bottom: 2rpx solid $wish-border-light;
}

.filter-item {
  position: relative;
  padding: $wish-spacing-xs $wish-spacing-md;
  border-radius: $wish-radius-md;
  margin-right: $wish-spacing-md;
  transition: all 0.3s ease;

  &--active {
    background-color: $wish-color-primary;

    .filter-text {
      color: $wish-text-inverse;
    }
  }
}

.filter-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

.filter-badge {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  min-width: 32rpx;
  height: 32rpx;
  background-color: $wish-color-error;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8rpx;
}

.badge-text {
  font-size: $wish-font-xs;
  color: $wish-text-inverse;
  font-weight: 500;
}

/* 举报列表 */
.report-list {
  flex: 1;
  padding: $wish-spacing-sm;
}

.report-items {
  display: flex;
  flex-direction: column;
  gap: $wish-spacing-sm;
}

.report-item {
  background-color: $wish-bg-secondary;
  border-radius: $wish-radius-lg;
  padding: $wish-spacing-md;
  box-shadow: $wish-shadow-sm;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.report-status {
  display: flex;
  align-items: center;
  margin-bottom: $wish-spacing-sm;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-xs;

  &--pending {
    background-color: $wish-color-warning;
  }

  &--approved {
    background-color: $wish-color-success;
  }

  &--rejected {
    background-color: $wish-color-error;
  }
}

.status-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.report-info {
  margin-bottom: $wish-spacing-md;
}

.report-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: $wish-spacing-xs;
}

.report-type {
  font-size: $wish-font-md;
  font-weight: 500;
  color: $wish-text-primary;
}

.report-time {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
}

.report-reason {
  font-size: $wish-font-sm;
  color: $wish-color-warning;
  margin-bottom: $wish-spacing-sm;
}

.report-target {
  background-color: $wish-bg-primary;
  border-radius: $wish-radius-md;
  padding: $wish-spacing-sm;
  margin-bottom: $wish-spacing-sm;
}

.target-user {
  display: flex;
  align-items: center;
}

.target-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  margin-right: $wish-spacing-sm;
}

.target-name {
  font-size: $wish-font-sm;
  color: $wish-text-primary;
  font-weight: 500;
}

.target-content {
  display: flex;
  flex-direction: column;
}

.target-preview {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  line-height: 1.6;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.reporter-info {
  display: flex;
  align-items: center;
}

.reporter-label {
  font-size: $wish-font-xs;
  color: $wish-text-disabled;
  margin-right: $wish-spacing-xs;
}

.reporter-name {
  font-size: $wish-font-xs;
  color: $wish-text-secondary;
}

.report-actions {
  display: flex;
  gap: $wish-spacing-sm;
}

.action-button {
  flex: 1;
  min-height: auto;
  padding: $wish-spacing-xs $wish-spacing-sm;
}

/* 加载状态 */
.load-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.load-text {
  font-size: $wish-font-sm;
  color: $wish-color-primary;
}

.no-more {
  text-align: center;
  padding: $wish-spacing-lg;
}

.no-more-text {
  font-size: $wish-font-sm;
  color: $wish-text-disabled;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: $wish-spacing-xxl;
  text-align: center;
}

.empty-icon {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: $wish-spacing-lg;
  opacity: 0.6;
}

.empty-text {
  font-size: $wish-font-lg;
  color: $wish-text-secondary;
  margin-bottom: $wish-spacing-xs;
}

.empty-desc {
  font-size: $wish-font-md;
  color: $wish-text-disabled;
}

/* 筛选模态框 */
.filter-content {
  padding: $wish-spacing-md 0;
}

.filter-section {
  margin-bottom: $wish-spacing-lg;

  &:last-child {
    margin-bottom: 0;
  }
}

.filter-title {
  display: block;
  font-size: $wish-font-md;
  font-weight: 600;
  color: $wish-text-primary;
  margin-bottom: $wish-spacing-md;
}

.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: $wish-spacing-sm;
}

.filter-option {
  padding: $wish-spacing-xs $wish-spacing-md;
  border: 2rpx solid $wish-border-medium;
  border-radius: $wish-radius-md;
  transition: all 0.3s ease;

  &--active {
    border-color: $wish-color-primary;
    background-color: rgba(232, 180, 160, 0.1);

    .option-text {
      color: $wish-color-primary;
    }
  }
}

.option-text {
  font-size: $wish-font-sm;
  color: $wish-text-secondary;
  transition: color 0.3s ease;
}

.filter-actions {
  display: flex;
  gap: $wish-spacing-md;
}

.filter-button {
  flex: 1;
}
</style>
